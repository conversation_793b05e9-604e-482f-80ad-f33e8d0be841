import React, { useState, useEffect, useRef } from 'react'
import { Container, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import SubHeader from '../../../components/common/SubHeader';
import MenuItemList from '../NewMenuItem/MenuItemComponents/MenuItemList'
import MenuItemForm from '../NewMenuItem/MenuItemComponents/MenuItemForm'

import '../MenuItem.scss'

export const NewMenuItem = ({modal=false, editItem, highestSortOrder, ...props}) => {

    const cId = JSON.parse(localStorage.getItem("user")).company_id;
    const mountedRef = useRef(false);
    const companyId = window.location.pathname.includes("default") ? null : cId; 
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [activeModule, setActiveModule ]=useState();

    useEffect(()=>{
        if(editItem && mountedRef.current){
            setActiveModule(
                {
                    id: editItem.id,
                    url: editItem?.module?.url,
                    description: editItem?.module?.description,
                    default_menu_item:{
                        icon: editItem?.icon,
                        text: editItem?.text
                    },
                    module_id: editItem?.module_id,
                    name: editItem?.name
                }
            )
        }
    },[editItem]);

    const handleActiveModule=(module)=>{
        setActiveModule({
            module_id: module.id,
            url: module.url,
            description: module.description,
            default_menu_item: module.default_menu_item,
            name: module.name,
        })
    }

    // create the breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/" }, text: "Home" },
        { linkAs: Link, linkProps: { to: "/p/menu" }, text: "Menu Settings" },
        { text: editItem ? "Edit Menu Item" : "New Menu Item" }
    ];

    return (
        <Container fluid>
            {!modal &&
                <SubHeader items={breadcrumbs} />
            }
            <Card className="content-card">
                <main className="new-menu-item-wrapper">
                    {success}
                    {error}
                    {!modal &&
                        <h4 className="section-title">
                            {editItem ? "Edit Menu Item" : "Add New Menu Item"}
                        </h4>
                    }
                    <div>
                        {!editItem &&
                            <MenuItemList  
                                setActiveModule={handleActiveModule} 
                                activeModule={activeModule}    
                            />
                        }
                    </div>
                    <div>
                        <MenuItemForm 
                            activeModule={activeModule}
                            onClose={props.onClose}
                            highestSortOrder={highestSortOrder}
                            edit={editItem ? true : false}
                            companyId={companyId}
                        />
                    </div>
                </main>
            </Card>
        </Container>
    )
}

