import React, {useState, useEffect, useCallback, useRef, useMemo, useLayoutEffect} from 'react';
import { useParams } from "react-router-dom";
import { useSelector, useDispatch} from 'react-redux';
import { Button, Card, Modal } from 'react-bootstrap';
import { format, endOfDay, startOfDay } from 'date-fns';

import { DisableRegisterButton } from "./DisableRegisterButton";
import OrderDetail from "./OrderDetail";
import OrderSummary from "../../components/OrderSummary";
import ErrorCatcher from "../../components/common/ErrorCatcher";
import Pos from '../../api/Pos';

import "./OrdersView.scss";

// need some way of specifying which KMS views are single-day and which are multi-day (show the date)
const REGISTER_GROUPS_SHOWING_TODAY_ONLY = [1];

export const OrdersView = (props) => {

    const params = useParams();
    const mountedRef = useRef(false);
    const timeoutRef = useRef(null);

    let register_id = props.register_id || params.id || 1;

    const [registerGroup, setRegisterGroup] = useState();
    const [registerInfo, setRegisterInfo] = useState();
    const [error, setError] = useState();
    const [viewDoneColumns, setViewDoneColumns] = useState(false);
    const [newestOrdersData, setNewestOrdersData] = useState({});
    const [newestOrdersTrigger, setNewestOrdersTrigger] = useState(0);
    const [displayedOrdersData, setDisplayedOrdersData] = useState({});
    const [selectedOrderId, setSelectedOrderId] = useState(null);
    const [columnIsScrolled, setColumnIsScrolled] = useState({});
    const [columnNeedsRefresh, setColumnNeedsRefresh] = useState({});
    const [showTodayOnly, setShowTodayOnly] = useState(null);
    const [refreshRegisterInfo, setRefreshRegisterInfo] = useState(0);

    const getColumnNeedsRefresh = useCallback((column_id) => {
        return (columnNeedsRefresh.hasOwnProperty(column_id) ? columnNeedsRefresh[column_id] : false);
    },[columnNeedsRefresh]);

    const setColumnRefresh = useCallback((column_id, value=true) => {
        setColumnNeedsRefresh(prev => ({
            ...prev,
            [column_id]: value
        }));
    },[])

    // load all of the orders
    const refreshAllOrders = useCallback(() => {
        if (register_id && registerGroup?.custom_statuses && !selectedOrderId) {
            registerGroup.custom_statuses.forEach(order_status => {
                if (order_status?.id && !!order_status.is_done === viewDoneColumns) {
                    Pos.order.get({
                        method: "POST",
                        register_id: register_id,
                        order_completed_status_id: order_status.id,
                        page_no: 1,
                        max_records: 999,
                        sort_direction: "ASC",  // change this back to DESC when done testing
                        start_datetime: startOfDay(new Date("10/27/2022")),
                        end_datetime: endOfDay(new Date()),
                    })
                    .then( async response => {
                        if(!response.errors && mountedRef.current){
                            saveNewestOrdersData(order_status.id, response.data.orders);
                        } else if(response.errors){
                            console.error(response.errors);
                            setError("There was a problem retrieving the orders.");
                        }
                    });
                }
            });
        }
    },[register_id, registerGroup, selectedOrderId, viewDoneColumns]);  // be very careful what dependencies you put here, will create a loop

    const handleViewDoneBtn = () => {
        setViewDoneColumns(prev => !prev);
    }

    const handleShowOrderDetails = (order_id) => {
        setSelectedOrderId(parseInt(order_id));
    }

    // this has to be done this way because the API calls come back at separate times and we don't want to overwrite the other results
    const saveNewestOrdersData = (custom_order_status_id, orders) => {
        setNewestOrdersData(prevOrders => ({...prevOrders, [custom_order_status_id]: orders }));
    };

    // this checks the DOM to see if the column is scrolled
    const checkColumnIsScrolled = (column) => {
        // column could either be the column id or the column element
        let el = isNaN(column) ? column : document.getElementById(`status-column-${column}`);
        let isScrolled = el ? el.scrollTop > 0 : false;
        return isScrolled;
    };

    const checkColumnsScrollPosition = useCallback(() => {
        let columnScrolled = {};
        registerGroup.custom_statuses.forEach(status => {
            columnScrolled[status.id] = checkColumnIsScrolled(status.id);
        });
        setColumnIsScrolled(columnScrolled);
    }, [registerGroup]);

    // this scroll event is debounced to stop it firing every bit of scroll
    const debouncedHandleScroll = useCallback((e) => {
        let debounceTime = 100; // ms - can be less than 100 for this situation
        clearTimeout(timeoutRef.current);
        timeoutRef.current = setTimeout(() => {
            let status_id = parseInt(e.target.id.slice(14));
            let scrolled = checkColumnIsScrolled(e.target);
            setColumnIsScrolled(prev => ({...prev, [status_id]: scrolled}));
            // if column is at the top AND it needs a refresh, then refresh all
            if (!scrolled && getColumnNeedsRefresh(status_id)) {
                refreshAllOrders();
            }
        }, debounceTime);
    //eslint is triggering on the columnNeedsRefresh but it is here as a trigger
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[checkColumnIsScrolled, columnNeedsRefresh, getColumnNeedsRefresh, refreshAllOrders]);
    
    const handleGoToTopButton = useCallback((e) => {
        let status_id = parseInt(e.target.id.slice(11));
        let column = document.getElementById(`status-column-${status_id}`);
        column.scrollTop = 0;
        // also trigger a refresh of the orders
        refreshAllOrders();
    },[refreshAllOrders]);

    const handleGoToTopAll = useCallback(() => {
        // push each column up to the top and refresh the content
        registerGroup?.custom_statuses.forEach(custom_status => {
            let column = document.getElementById(`status-column-${custom_status.id}`);
            if (column) column.scrollTop = 0;
        });
        // also trigger a refresh of the orders
        refreshAllOrders();
    },[refreshAllOrders, registerGroup]);

    const saveNewOrderStatus = useCallback((order_id, status_id) => {
        if (order_id) {
            Pos.order.logStatus({
                order_id: order_id,
                order_status_id: status_id
            })
            .then( async response => {
                if (mountedRef.current && !response.errors && response.data) {
                    setSelectedOrderId(null);   // close the modal
                } else if (response.errors) {
                    setError(<ErrorCatcher error={response.errors} />)
                }
            });
        }
    }, []);

    const modalPagePart = useMemo(() => {
        const handleCloseModal = () => {
            setSelectedOrderId(null);
        }

        if (selectedOrderId) {
            return (
                <Modal
                    show={!!selectedOrderId}
                    onHide={handleCloseModal}
                    size="lg"
                    className="order-details-modal"
                >
                    <Modal.Header closeButton>
                    </Modal.Header>
                    <Modal.Body>
                        <OrderDetail
                            orderId={selectedOrderId}
                            customStatuses={registerGroup?.custom_statuses}
                            onSelectStatus={(order_id, status_id) => {
                                saveNewOrderStatus(order_id, status_id);
                                handleGoToTopAll();
                            }}
                            hideDate={showTodayOnly}
                        />
                    </Modal.Body>
                </Modal>
            )
        } else {
            return null;
        }
    }, [selectedOrderId, registerGroup, saveNewOrderStatus, handleGoToTopAll, showTodayOnly]);

	useEffect(() => {
		mountedRef.current = true;

        return () => {
            mountedRef.current = false;
        }
	// eslint-disable-next-line react-hooks/exhaustive-deps
	},[]);

    // load the statuses and registerInfo for the register
    useEffect(() => {
        if (register_id) {
            Pos.registerGroups.get({ register_id: register_id })
            .then( async response => {
                if (mountedRef.current && !response.errors && response.data[0]) {
                    setRegisterGroup(response.data[0]);
                } else if (response.errors) {
                    setError(<ErrorCatcher error={response.errors} />)
                }
            });
        }
    }, [register_id]);

    const triggerRefreshRegister = () => {
        setRefreshRegisterInfo(prev => prev-1);
    }

    // when triggered to refresh the registerInfo
    useEffect(() => {
        if (register_id) {
            Pos.register.get({ id: register_id })
            .then( async response => {
                if (mountedRef.current && !response.errors && response.data[0]) {
                    setRegisterInfo(response.data[0]);
                } else if (response.errors) {
                    setError(<ErrorCatcher error={response.errors} />)
                }
            });
        }
    }, [refreshRegisterInfo, register_id]);

    useEffect(() => {
        // for each of the custom statuses set the status view to be not scrolled
        if (registerGroup?.custom_statuses) {
            checkColumnsScrollPosition();
        }
        setShowTodayOnly(REGISTER_GROUPS_SHOWING_TODAY_ONLY.includes(registerGroup?.id));
    }, [registerGroup, checkColumnsScrollPosition]);
    
    // reload the page columns
    useEffect(() => {
        refreshAllOrders();
        // start a timer to refresh the orders every 30 second
        const interval = setInterval(() => {
            refreshAllOrders();
        }, 5000); // 5 seconds

        return () => {
            clearInterval(interval);
        }
    }, [registerGroup, register_id, viewDoneColumns, selectedOrderId, refreshAllOrders]);

    // what happens after the new orders are loaded
    // take the most recently saved data and process it
    useEffect(() => {
        // this returns from the saved values
        const getColumnIsScrolled = (column_id) => {
            let isScrolled = columnIsScrolled.hasOwnProperty(column_id) ? columnIsScrolled[column_id] : false;
            return isScrolled;
        };

        // this only needs to trigger when newestOrdersData changes - ignore when the others change
        if (newestOrdersTrigger) {
            // go through each column of new data and compare to the old data
            let dataToDisplay = {};
            Object.keys(newestOrdersData).forEach(status_id => {
                let isScrolled = getColumnIsScrolled(status_id);
                if (isScrolled) {
                    // if the page is scrolled down and the content of the column doesn't change, then keep the old data
                    // if the page is scrolled down and the content of the column changes, then keep the old data and flag for refresh
                    // if the page is still scrolled down and the refresh flag is already on keep the old data
                    dataToDisplay[status_id] = displayedOrdersData[status_id];
                    if (columnNeedsRefresh[status_id]!==true) {
                        let contentHasChanged = JSON.stringify(newestOrdersData[status_id]) !== JSON.stringify(displayedOrdersData[status_id]);
                        if (contentHasChanged) {
                            setColumnRefresh(status_id, true);
                        }
                    }
                } else {
                    // page is not scrolled down, just update with the new data
                    dataToDisplay[status_id] = newestOrdersData[status_id];
                    if (columnNeedsRefresh[status_id]!==false) {
                        setColumnRefresh(status_id, false);
                    }
                }
            });
            setDisplayedOrdersData(dataToDisplay);
            setNewestOrdersTrigger(0);
        }
    }, [newestOrdersTrigger, newestOrdersData, displayedOrdersData, columnIsScrolled, columnNeedsRefresh, setColumnRefresh]);

    // the useEffect above needs to trigger ONLY when newestOrdersData changes - this was the least intensive way I could do it
    // I don't want to compare the two arrays every single iteration, that could easily take too much processing power
    useEffect(() => {
        setNewestOrdersTrigger(1);
        // processNewOrders();
    }, [newestOrdersData]);

    return (
        <section className="orders-view-container">

            <div className="orders-view-header">
                <div className="orders-view-header-left">
                    <a href="/p/" className="btn btn-back m-0 px-0">
                        <i className="far fa-arrow-circle-left" />
                    </a>
                    <div className="page-title">Orders for: <span className="register-name">{registerInfo?.name || register_id}</span></div>
                    <Button onClick={handleViewDoneBtn}>{viewDoneColumns ? "View Active" : "View Done"}</Button>
                </div>
                <div className="orders-view-header-right">
                    <DisableRegisterButton
                        registerInfo={registerInfo}
                        displayError={setError}
                        triggerRefresh={triggerRefreshRegister}
                    />
                </div>
            </div>

            <div className="page-columns no-select-text">
                {registerGroup && registerGroup.custom_statuses
                    .filter(custom_status => !!custom_status.is_done===viewDoneColumns)
                    .map(custom_status => {
                        let needsRefresh = getColumnNeedsRefresh(custom_status.id);
                        let numOrders = displayedOrdersData[custom_status.id]?.length || 0;
                        return (
                            <div key={`column-${custom_status.id}`} className={`view-column ${viewDoneColumns ? 'done' : 'incomplete'}`}>
                                <h2>{custom_status.name}{` (${numOrders})`}</h2>
                                <div className="view-column-cards" id={`status-column-${custom_status.id}`} onScroll={debouncedHandleScroll}>
                                    {(custom_status.id in displayedOrdersData) ?
                                        <>
                                            {!!columnIsScrolled[custom_status.id] && 
                                                <div className="scroll-to-top">
                                                    <Button
                                                        id={`top-button-${custom_status.id}`}
                                                        size="sm"
                                                        onClick={handleGoToTopButton}
                                                    >Scroll to Top {needsRefresh && " - View New Orders"}</Button>
                                                </div>
                                            }
                                            {displayedOrdersData[custom_status.id] && displayedOrdersData[custom_status.id].map(order => (
                                                <Card className={`order-card ${selectedOrderId===order.id ? 'selected' : ''}`}
                                                    key={`order-card-${order.id}`}
                                                    onClick={() => handleShowOrderDetails(order.id)}
                                                >
                                                    <OrderSummary
                                                        order={order}
                                                        userTitle="Name:"
                                                        hideDate={showTodayOnly}
                                                    />
                                                </Card>
                                            ))}
                                        </>
                                    :
                                        <>loading...</>
                                    }
                                </div>
                            </div>
                        )
                    })
                }
            </div>

            {error}
            {modalPagePart}
        </section>
    )
}