@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/themes';

.owner-permission-dashboard-wrapper{
    .long-row{
        @include basic-flex-column;
        width: 800px;
        border: 2px solid $primary-color;
        margin-bottom: 1.5rem;
        padding: 12px;
        border-radius: $card-border-radius;
    }
    .row{
        @include basic-flex-row;
        max-width: 525px;
        flex-wrap: nowrap;
        .col{
            min-width: 275px;
            max-width: 275px;
            @include basic-flex-column;
            input{
                margin-bottom: 1rem;
            }
            .rbt-close{
                width: 30px;
                padding: 8px 12px;
                margin: 0 auto;
            }
        }
        button{
            @include basic-button;
            margin-right: 2rem;
            width: 200px;
        }
        a{
            text-decoration: none;
        }
    }
}