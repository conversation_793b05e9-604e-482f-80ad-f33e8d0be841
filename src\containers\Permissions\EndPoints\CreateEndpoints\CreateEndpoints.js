import React, { useState, useEffect, useRef} from 'react'
import { Redirect, useHistory } from 'react-router-dom'
import { <PERSON><PERSON>, Modal } from 'react-bootstrap'

import Toast from '../../../../components/Toast'
import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import ModuleTypeahead from '../../../../components/Typeahead/ModuleTypeahead';
import RedirectModal from '../../../../components/common/RedirectModal';
import { NewEndpointRow } from './NewEndpointRow';
import { EndpointExists } from './EndpointExists';
import { getEndpoints } from '../../PermissionsUtils/PermissionUtils';
import { checkForSlash, checkForUniqueness, checkEndpoints } from '../EndpointUtils';

import Permissions from '../../../../api/Permissions'

export const CreateEndpoints = ({setHide, setSuccess, isModal=false, passAddition=()=>{}, ...props}) => {

    const mountedRef = useRef(false);
    const editRef = useRef();
    const history = useHistory();
    const ownPage = history.location.pathname.includes("new") ? true : false;
    const [ endpoints, setEndpoints ] =useState([{tempId: 1, slug:null, method: null}]);
    const [ selectedModules, setSelectedModules ] =useState([]);
    const [ error, setError ]=useState(null);
    const [ showRedirect, setShowRedirect ] = useState(false);
    const [ showExists, setShowExists ]=useState(false);
    const [ existing, setExisting ]=useState([]);

    useEffect(()=>{
        mountedRef.current = true 
       
        return ()=> mountedRef.current = false
    },[]);

    const passEndpoints=(tempId, slug, method)=>{
        let tempEndpoints=[]
        setError(null);
        for(let i = 0; i<endpoints.length; i++){
            if(endpoints[i].tempId===tempId){
                tempEndpoints.push({
                    tempId : tempId,
                    slug : slug,
                    method : method
                })
            }
            else tempEndpoints.push(endpoints[i])
        }
        if(mountedRef.current)setEndpoints(tempEndpoints)   
    }

    const addNewEndpoint =()=>{
        let allTempIds = endpoints.map((endpoint)=>endpoint.tempId)
        let tempId = Math.max(...allTempIds);
        setEndpoints([...endpoints, {tempId: tempId+1}])
    }

    const removeEndpoint=(tempId)=>{
        const tempEndpoints=[];
        if(endpoints.length === 0 ) tempEndpoints.push([{tempId: 1, slug:null, method: null}])
        else{
            for(let i = 0; i<endpoints.length; i++){
                if(endpoints[i].tempId!==tempId){
                    tempEndpoints.push(endpoints[i]) 
                }
            }
        }
        setEndpoints(tempEndpoints)
    }

    const saveEndpoints=async(e)=>{
        e.preventDefault();
        setError(null);
        setSuccess(null);

        let moduleIds=selectedModules.map((modules)=>modules.id)
        let matches=[];
        //check to make sure every endpoint has a url and method
        let allGood = checkEndpoints(endpoints);
        if(!allGood) setError(<ErrorCatcher warning={true} error={"Please make sure every endpoint has a URL and Method"} />)

        if(allGood){
            for(let i =0; i< endpoints.length; i++){
                //make sure it starts with a slash and does not end with one
                let modified = checkForSlash(endpoints[i].slug);
                if(modified) endpoints[i].slug = modified;
                //check to see if the endpoint already exists
                let match = await checkForUniqueness(endpoints[i].slug, endpoints[i].tempId);
                if(match) matches.push(match);
            }
            if(matches.length > 0) {
                setExisting(matches);
                setShowExists(true);
            }
        }
        let response;
        if(allGood && !matches.length){
            try{
                if(endpoints.length ===1) response = await Permissions.Endpoints.create({
                    slug: endpoints[0].slug,
                    method: endpoints[0].method,
                    modules_to_associate: moduleIds
                });
                else if(endpoints.length > 1) response = await Permissions.Endpoints.createMany({
                    modules_to_associate: moduleIds,
                    endpoints:endpoints
                });
                if(response.status===200){
                    setEndpoints([{tempId: 1, slug:null, method: null}])
                    setSelectedModules([])
                    setSuccess(<Toast>"Endpoints Created Successfully"</Toast>)
                    if(isModal && endpoints.length === 1) passAddition(response.data.id)
                    if(isModal && endpoints.length > 1) passAddition(response.data.map((each)=>each.id))
                    if(setHide) setHide(true);
                    else if(ownPage) setShowRedirect(true)
                }else if(response.errors){
                    setError(<ErrorCatcher error={response.errors} />)
                }
            }catch(ex){
                console.error(ex)
            }
        }
    }

    return (
        <div className="new-endpoints">
            {error}
            <div>
                <span>API Url</span>
                <>
                    {endpoints?.map(endpoint=>(
                        <NewEndpointRow 
                            key={`each-endpoint-${endpoint.tempId}`} 
                            endpoint={endpoint} 
                            passEndpoints={passEndpoints} 
                            removeEndpoint={removeEndpoint} 
                            validateSlug={checkForSlash} 
                        />
                    ))}
                    <Button data-cy="add-more-btn" className="outline" onClick={addNewEndpoint}>
                        <i className="far fa-plus" />
                        Add More
                    </Button>
                </>
            </div>
            {!isModal &&
                <div className="assign-modules" data-cy="assign-modules">
                    <h6>
                        Assign to Module
                    </h6>
                    <p>
                        All of the above endpoints will be assigned to the following Module(s)
                    </p>
                    {endpoints &&
                        <ModuleTypeahead multiple={true} passSelection={(selection)=>{setSelectedModules(selection)}} />
                    }
                </div>
            }
            <div className="mt-3 space-between-row">
                {!ownPage && 
                    <Button data-cy="cancel-btn" variant="danger" onClick={()=>setHide(false)}>
                        Cancel Creation
                    </Button>
                }
                <Button data-cy="save-endpoints-btn" onClick={saveEndpoints}>
                    Save Endpoints
                </Button>
            </div>
            <RedirectModal
                show={showRedirect}
                onHide={()=>setShowRedirect(false)}
                redirectOneText="Endpoint Dashboard"
                redirectOneUrl="/p/permissions/endpoints"
                redirectTwoText="Create Another Endpoint"
                redirectTwoUrl="/p/permissions/endpoints/new"
                modifiedWhat = "endpoint"
            />
            <EndpointExists
                showExists={showExists}
                setShowExists={setShowExists}
                existing={existing}
                setSuccess={setSuccess}
                setError={setError}
                removeEndpoint={removeEndpoint}
            />
        </div>
    )
}

