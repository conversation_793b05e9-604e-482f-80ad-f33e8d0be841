import React from 'react';
import {Container, Table} from 'react-bootstrap';
import { formatFileSize } from '../../../utils/cms';

//src\containers\MediaManager\MediaManagerWrappers\ResultsWrapper.js

const MEDIA_TYPES = [
    {id: 1, name: "Images"},
    {id: 4, name: "Videos"},
    {id: 5, name: "Documents"},
    {id: 7, name: "Audio"},
    {id: 8, name: "Other"}
]

const ListMedia = ({allMedia, setActiveMedia, activeMedia, multiSelect}) => {
    return (
        <Container fluid className="list-media">
            <Table className={`table`} responsive>
                <thead>
                    <tr>
                        <th>Thumb</th>
                        <th>Name</th>
                        <th className="text-right">Size</th>
                    </tr>
                </thead>
                <tbody>
                    {allMedia?.map((media)=>{
                        let _size = "";
                        if (media?.metadata){
                            if (typeof media.metadata === "string") media.metadata = JSON.parse(media.metadata);
                            if (media.metadata?.size) _size = formatFileSize(media.metadata.size);
                        }
                        return (
                            <tr 
                                key={`list-item-${media.id}`}
                                className={`${activeMedia?.id === media.id ? "active primary" : ""}`} 
                                onClick={()=>setActiveMedia(prev=>{
                                    if (multiSelect) {
                                        let _prevValue = prev;
                                        if (!_prevValue) _prevValue = [];
                                        else if (!Array.isArray(_prevValue)) _prevValue = [_prevValue];
                                        if (_prevValue.find(m=>m.id===media.id)) return _prevValue.filter(m=>m.id!==media.id);
                                        else return [..._prevValue, media];
                                    } else return media;
                                })}
                            >
                                <td className="small-thumb">
                                    {media?.media_type === 1 && media.url ? // media
                                        <img src={media.url} alt={`a thumnail for ${media.name}`}/>
                                        :
                                        <span>
                                            {media.icon}
                                        </span>  
                                    }
                                </td>
                                <td>
                                    {media.description}
                                </td>
                                <td className="text-right">
                                    {_size}
                                    {/*MEDIA_TYPES.find(a=>+a.id===+media.media_type)?.name || ""*/}
                                </td>
                            </tr>
                        );
                    })}
                </tbody>
            </Table>
        </Container>
    );
}

export default ListMedia