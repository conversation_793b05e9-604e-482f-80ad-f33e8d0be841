
@import '../../assets/css/scss/variables';

body {
    overflow-y: hidden;
    overflow-x: hidden;
}

.slaask-component {
    display: none;
}

.orders-view-container {
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    z-index: 1040;
    background: $grey-1;
    overflow: hidden;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    display: flex;
    flex-direction: column;

    .orders-view-header {
        flex: 0 0 auto;
        height: $company-kms-header-height;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding-bottom: 10px;

        .page-title {
            font-size: 1.2rem;
        }

        &.disabled {
            background-color: $company-bright-error;
        }
    }

    .register-disabled-warning {
        background-color: $company-bright-error;
        color: $company-white;
        font-weight: 600;
        width: 80%;
        text-align: center;
        align-self: stretch;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .orders-view-header-left {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
    }

    .orders-view-header-right {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        flex: 1 1 auto;

        .btn {
            margin-left: 0.5rem;
            white-space: nowrap;
        }
    }

    .btn-back {
        line-height: 22px;
        border: 0;
        font-size: 2rem;
        color: $company-black;
        background: transparent;
        margin-right:0;
    }

    .btn-back:hover {
        color: $company-primary;
    }

    .page-columns {
        // flex: 1;
        display: flex;
        flex-direction: row;
        height: calc(100vh - $company-kms-header-height - 10px);
        padding-bottom: 10px;

        .view-column {
            flex: 1 1 0;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            display: flex;
            flex-direction: column;
        }

        h2 {
            flex: 0 0 auto;
            font-size: 1.8rem;
        }

        .view-column-cards {
            flex: 1;
            overflow-y: scroll;
        }

        .scroll-to-top {
            position: sticky;
            top: 0%;
            z-index: 100;
            display: flex;
            justify-content: center;
            background: $company-white;
            padding-bottom: 2px;
        }

        .order-card {
            border: 1px solid $grey-5;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0.8rem;
        }

        .order-card:hover {
            background-color: $grey-3;
        }

        .order-card.selected {
            background-color: $company-tertiary;
        }
    }

}

.order-details-modal {
    .modal-body {
        padding: 0;
        display: flex;
        flex-direction: row;
        align-items: flex-start;

        & > div {
            padding: 1rem;
            flex: 2 1 0;
        }

        .status-buttons {
            flex: 1 1 0;
            display: flex;
            flex-direction: column;

            .btn {
                margin-bottom: 0.4rem;
                padding: 0.5rem !important;
            }
        }

        .at {
            padding-left: 0.3rem;
            padding-right: 0.3rem;
        }

        .total-price {
            font-size: 1.2rem;
            font-weight: 600;
            padding-bottom: 0.5rem;
        }
    }
}

@media (max-width: 750px) {
    .order-details-modal {
        .modal-body {
            flex-direction: column;
        }
    }
}

@media (min-width: 1px) {
    .modal-lg {
        max-width: 95%;
    }
}

@media (min-width: 1220px) {
    .modal-lg {
        max-width: 1100px;
    }
}

.orders-view-container,
.order-details-modal {
    .order-preview {
        display: grid;
        grid-template-columns: 1fr 3fr;
        column-gap: 10px;
        row-gap: 4px;
        font-size: 1rem;

        .title {
            font-weight: bold;
            font-size: 1rem;
        }

        .item-grid {
            display: grid;
            grid-template-columns: auto 1fr;
            column-gap: 15px;
        }

    }
}