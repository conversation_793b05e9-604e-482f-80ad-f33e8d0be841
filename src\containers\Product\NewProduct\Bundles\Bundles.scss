@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/mixins';

.bundle-tokens-wrapper{
    @include product-flex-row;
    margin: 0 3.5rem 1rem 3.5rem;
    padding: 20px;
    .y-n-input{
        width:200px;
        input{
            width: 30px;
            margin-right: 2rem;
        }
    }
    .token-typeahead{
        input{
            width: 500px;
        }
    }
    .product-flex-col{
        @include product-flex-col;
    }
    label, .token-label{
        @include basic-label;
    }
    .bundles-typeahead{
        margin-left: 2rem;
        width: 500px;
    }
    @media (max-width:1300px){
        @include product-flex-col;
    }
}
.bundle-service-tokens-wrapper{
    @include product-flex-col;
    margin: 0 3.5rem 0 3.5rem;
    padding: 20px;
    .product-flex-row{
        @include product-flex-row;
    }
    .product-flex-col{
        @include product-flex-col;
        margin: .75rem;
    }
    label, .token-label{
        @include basic-label;
    }
    select, input{
        @include basic-input-select;
        width: 225px;
        @media (max-width: 420px){
            width: 175px;
        }
    }
    .y-n-input{
        display: flex;
        width:200px;
        input{
            width: 30px;
            margin-right: 2rem;
        }
    }
    .token-typeahead{
        input{
            width: 500px;
        }
    }
    .qty-input input{
        width: 100px;
    }
    .token-footnote{
        display: flex;
        justify-content: center;
    }
    @media (max-width:1300px){
        .product-flex-row{
            @include product-flex-col;
        }
    }
}
.orphan-token, .no-orphan{
    @include basic-flex-column;
    align-items: center;
    padding:  0 3.5rem 0 3.5rem;
    text-align: center;
}