import React, { useState, useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Button } from 'react-bootstrap';

import ErrorCatcher from '../../../../../components/common/ErrorCatcher';
import Users from '../../../../../api/Users';
import { determineProductId } from '../../ArchiveStreamsUtils';

const StreamButton = (props) => {

    const {stream, setErrors, setSelectedProductId, selectedProductId, purchased, setPurchased} = props;
    
    const user = useSelector(state => state.auth.user);
    const mountedRef = useRef(false);
    const productId = determineProductId(stream);

    const [loading, setLoading]=useState(true);
    const [localPurchase, setLocalPurchase]=useState(false);


    useEffect(()=>{
        mountedRef.current = true
        
        return () => mountedRef.current = false
    },[]);

    useEffect(()=>{
        const isProductPurchased=async(productId)=>{
            try{
                let response = await Users.purchasedProduct({user_id: user?.profile?.id, product_id: [productId]});
                if(!response.errors && response.data[productId] && mountedRef.current) {
                    setLocalPurchase(true); 
                    if(productId === selectedProductId) setPurchased(true);
                }
                else if(!response.errors && response.data===0 && mountedRef.current) setLocalPurchase(false);
                else if(response.errors && mountedRef.current) {
                    setErrors(<ErrorCatcher error={response.errors} />);
                    setLocalPurchase(false)
                }
            }catch(ex){console.error(ex)}
            setLoading(false)
        }

        if(productId && user && mountedRef.current) {
            isProductPurchased(productId);
        };
    },[productId, setErrors, setPurchased, selectedProductId, user]);

    return (
        <>
            {!loading && productId &&
                <>
                    {localPurchase ? 
                        <Button 
                            variant="primary" 
                            className="stream-event-btn" 
                            onClick={()=>setSelectedProductId(productId)}
                        >
                            {productId===selectedProductId ? "Watching" : "Watch"}
                        </Button>
                    : 
                        <Button 
                            variant="outline-primary" 
                            className="stream-event-btn" 
                            onClick={()=>setSelectedProductId(productId)}
                        >
                            {productId===selectedProductId ? "Viewing" : "Preview"}
                        </Button>
                    }
                </>
            }
        </>
    )
}

export default StreamButton