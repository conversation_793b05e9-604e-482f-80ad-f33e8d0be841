import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { Container, Card, Breadcrumb, <PERSON><PERSON>, Modal } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';

import SubHeader from '../../components/common/SubHeader';
import { EachSubdomain, HeaderList, NewURLs, DomainDetails } from './URLComponents';
import { setErrorCatcher, setSuccessToast } from '../../utils/validation';
import { authUserHasModuleAccessMany } from '../../utils/auth';

import CMS from '../../api/Cms';
import Companies from '../../api/Companies';

import styles from './Urls.module.scss'

const VIEW_MODULE_ID = 312;
const ADD_MODULE_ID = 313;
const EDIT_MODULE_ID = 314;
const DELETE_MODULE_ID = 315;
export const Urls = ({importedCompanyId = null}) => {

    const mountedRef = useRef(false);
    const formRef = useRef({});
    const [companyId, setCompanyId]=useState(useSelector(state => state.auth.user.company_id)) //default company id is the id you're on.  SB will have options to access differently
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [websites, setWebsites]=useState([]); 
    const [localErrors, setLocalErrors]=useState([])
    const [urls, setUrls]=useState([]);
    const [activeEdit, setActiveEdit]=useState(null);
    const [themes, setThemes]=useState([]);
    const [showNewSB, setShowNewSB] = useState(false);
    const [showNew, setShowNew]=useState(false);
    const [userPermissions, setUserPermissions] = useState();

    const getWebsites = useCallback(async()=>{
        try{
            let response = await CMS.websites.get({id: companyId});
            if(response.status===200 && mountedRef.current){
                setWebsites(response.data);
            }
            else return("Problem fetching websites");
        }
        catch(ex){
            console.error(ex)
            return("Problem fetching websites");
        }
    },[companyId]);

    const getThemes =useCallback(async()=>{
        try{
            let response = await CMS.themes.get();
            if(response.status === 200 && mountedRef.current){
                setThemes(response.data)
            }
            else return("Problem fetching themes");
        }catch(ex){
            console.error(ex);
            return("Problem fetching themes");
        }
    },[]);

    const getUrls = useCallback(async()=>{
        try{
            let response = await Companies.urls({company_id: companyId});
            if(response.status === 200){
                setUrls(response.data);
            }
            else return("Problem fetching URLs")
        }catch(ex){
            console.error(ex)
            return("Problem fetching URLs")
        }
    },[companyId]);

    const checkPermissions = useCallback(async()=>{
        try{
            let response = await authUserHasModuleAccessMany([VIEW_MODULE_ID, ADD_MODULE_ID, EDIT_MODULE_ID, DELETE_MODULE_ID]);
            setUserPermissions(response);
            console.log(response)
        }catch(ex){console.error(ex)}
    },[])

    useEffect(()=>{
        if(importedCompanyId){
            setCompanyId(importedCompanyId)
        }
    },[importedCompanyId])

    useEffect(()=>{
        mountedRef.current = true

        let errors;
        const checkAllData=async()=>{
            let errorUrl = await getUrls();
            let errorTheme = await getThemes();
            let errorWebsite = await getWebsites();

            errors = errorTheme + errorUrl + errorWebsite;
        }

        checkPermissions();
        checkAllData();

        if(errors){
            setError(setErrorCatcher(errors));
        }

        return()=> mountedRef.current = false
    },[ getThemes, getUrls, getWebsites, checkPermissions, companyId]);

    useEffect(()=>{
        if(themes.length > 0 && urls.length > 0 ) setLoading(false);
    },[themes, urls]);

    const handleHide=()=>{
        setShowNewSB(false);
        setShowNew(false);
        setLocalErrors([])
    }

    const handleShowEdit=(website)=>{
        setActiveEdit(website);
        setLocalErrors([]);
    }

    const handleNewSBDomain=()=>{
        setShowNewSB(true);
        formRef.current.domain = ".siteboss.net";
    }

    const handleNewUrl=()=>{
        setShowNew(true);
        formRef.current.domain = "";
    }

    const checkRequired=(formData)=>{
        let localError = [];
        if(!formData.website_id) localError.push("Website is required.");
        if(formData.domain.includes("siteboss.net") && !formData.subdomain) localError.push("Subdomain is required.")
        if(!formData.website_theme_id) localError.push("Theme is required.")
        return localError;
    }

    const cleanUp=()=>{
        setActiveEdit(null);
        setShowNew(false);
        setShowNewSB(false);
        setLoading(true);
        getUrls();
        setError();
        setLocalErrors([]);
    }

    const handleSubmit =async(e, id=null)=>{
        e.preventDefault();
        e.stopPropagation();
        const formData = new FormData(e.target);
        let formDataObj = modifyFormData(formData, id);
        let localError = checkRequired(formDataObj);
        if(localError.length > 0) return setLocalErrors(localError);
        else{
            try{
                let response = await CMS.urls.create(formDataObj)
                if(response.status === 200){
                    cleanUp();
                    setSuccess(setSuccessToast(`URL ${id ? "edited" : "created"} successfully`))
                }
                else if (response.errors) setError(setErrorCatcher(response.errors))
            }catch(ex){
                console.error(ex)
                setError(setErrorCatcher(ex))
            }
        }
    }

    const modifyFormData=(formData, id)=>{
        let formDataObj = Object.fromEntries(formData.entries());
        if(id) formDataObj.id = id;
        formDataObj.company_id = companyId;
        //this will have auto added to indicate that it is NOT a siteboss domain and the user input should be added instead
        if(formRef.current.domain === "") delete formRef.current.domain;
        //If there IS input for the domain, it was a non SB domain being edited, as the edit for it isn't allowed, so remove the formRef domain
        if(formData.domain) delete formRef.current.domain;
        formDataObj = {...formDataObj, ...formRef.current};
        if(formDataObj.subdomain) formDataObj.subdomain = removeWhiteSpaceAndForwardPeriod(formDataObj.subdomain);
        if(formDataObj.domain) formDataObj.domain = removeWhiteSpaceAndForwardPeriod(formDataObj.domain);
        if(!formDataObj.index_page) formDataObj.index_page = "p";
        return formDataObj;
    }

    const removeWhiteSpaceAndForwardPeriod=(word)=>{
        let newWord = word.replace(/\s/g, '');
        if(newWord[0] === ".") newWord = newWord.slice(1);
        return newWord;
    }

    const handleDelete = async(id)=>{
        if(id){
            try{
                let response = await CMS.urls.delete({id: id});
                if(response.status === 200){
                    setSuccess(setSuccessToast("URL deleted successfully"));
                    cleanUp();
                }
                else if (response.errors) setError(setErrorCatcher(response.errors))
            }catch(ex){
                console.error(ex);
                setError(setErrorCatcher(ex))
            }
        }
    }

    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
        { text: "URLs"}
    ];

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} />
            <Card className="content-card">
                {success}
                {error}
                {loading && userPermissions && userPermissions[312] ? 
                    <>
                        <SkeletonTheme color="#e0e0e0">
                            <div className="mt-3 text-center">
                                <Skeleton height={28} width={200}/>
                                <Skeleton height={16} count={4} />
                            </div>
                        </SkeletonTheme>
                    </>
                :   
                    <>
                        <h4 className="section-title">Company URLs</h4>
                        <div className={styles['urls-wrapper']}>
                            <div>
                                <h5>SiteBoss URLs</h5>
                                <div className={styles["header-btns"]}>
                                    {userPermissions && userPermissions[313] &&
                                        <Button onClick={()=>handleNewSBDomain()}>
                                            + New Instant Subdomain
                                        </Button>
                                    }
                                    <div className={styles["edit"]}>
                                        {activeEdit &&
                                            <span className="cp bold" onClick={()=>handleShowEdit(null)}>
                                                Hide Edit
                                            </span>
                                        }
                                    </div>
                                </div>
                                <p>
                                    At least one SiteBoss URL is required per company.  Spaces will be removed from any domain or subdomain you input.  For example, "my site" will become "mysite".  If no index_page is selectd, it will default to portal (indicated by "p")
                                </p>
                            </div>
                            <HeaderList 
                                handleShowEdit={handleShowEdit}
                                showHideEditText={activeEdit ? true : false}
                                styles={styles}
                                showSubdomain={false}
                            />
                            {urls.filter((website)=>website.domain === "siteboss.net").map((website)=>{

                                let deleteAvailable = urls.filter((website)=>website.domain === "siteboss.net")?.length > 1 ? true : false;
                                let theme = themes.filter((theme)=>theme.id === website.website_theme_id)
                                return(
                                    <div key={`each-subdomain-${website.id}`}>
                                        <DomainDetails 
                                            website={website}
                                            websites={websites}
                                            handleShowEdit={handleShowEdit}
                                            theme={theme}
                                            styles={styles}
                                            canEdit={userPermissions && userPermissions[314]}
                                        />
                                        {activeEdit && activeEdit.id === website.id &&
                                            <Modal show={activeEdit && activeEdit.id === website.id} onHide={()=>setActiveEdit(null)}>
                                                <Modal.Header closeButton>
                                                    Edit Url
                                                </Modal.Header>
                                                <EachSubdomain
                                                    deleteAvailable={deleteAvailable}
                                                    website={website}
                                                    themes={themes}
                                                    styles={styles}
                                                    handleSubmit={handleSubmit}
                                                    companyId={companyId}
                                                    formRef={formRef}
                                                    localErrors={localErrors}
                                                    handleDelete={handleDelete}
                                                    canDelete={userPermissions && userPermissions[315]}
                                                /> 
                                            </Modal>
                                        }
                                        <hr />
                                    </div>
                                )
                            })}

                            <div>
                                <br />
                                <h5>Custom URLs</h5>
                                {userPermissions && userPermissions[313] &&
                                    <Button onClick={()=>handleNewUrl()}>
                                        + New Custom Domain
                                    </Button>
                                }
                            </div>
                            <HeaderList 
                                handleShowEdit={handleShowEdit}
                                showHideEditText={activeEdit ? true : false}
                                styles={styles}
                                showSubdomain={true}
                                actveEdit={activeEdit}
                            />
                            {urls.filter((website)=>website.domain !== "siteboss.net").map((website)=>{
                                let theme = themes.filter((theme)=>theme.id === website.website_theme_id)
                                return(
                                
                                    <div key={`each-custom-domain-${website.id}`}>
                                        <DomainDetails
                                            website={website}
                                            websites={websites}
                                            handleShowEdit={handleShowEdit}
                                            theme={theme}
                                            styles={styles}
                                            canEdit={userPermissions && userPermissions[314]}
                                        />
                                        {activeEdit && activeEdit.id === website.id &&
                                            <>
                                                <Modal show={activeEdit && activeEdit.id === website.id} onHide={()=>setActiveEdit(null)}>
                                                    <Modal.Header closeButton>
                                                        Edit URL
                                                    </Modal.Header>
                                                    <EachSubdomain
                                                        siteboss={false}
                                                        website={website}
                                                        themes={themes}
                                                        styles={styles}
                                                        handleSubmit={handleSubmit}
                                                        companyId={companyId}
                                                        formRef={formRef}
                                                        localErrors={localErrors}
                                                        handleDelete={handleDelete}
                                                        canDelete={userPermissions && userPermissions[315]}
                                                    />
                                                </Modal>
                                            </>
                                        }
                                        <hr />
                                    </div>
                                )
                            })}
                        </div>
                    </>
                }
                <Modal show={showNewSB || showNew} onHide={handleHide}>
                    <Modal.Header closeButton>
                        New URL
                    </Modal.Header>
                    <Modal.Body>
                        <div>
                            <NewURLs 
                                siteboss={showNewSB ? true : false}
                                themes={themes}
                                handleSubmit={handleSubmit}
                                formRef={formRef}
                                companyId={companyId}
                                localErrors={localErrors}
                            />
                        </div>
                    </Modal.Body>
                </Modal>
            </Card>
        </Container>
    )
}
