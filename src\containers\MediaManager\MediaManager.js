import React, { useState, useEffect, useCallback } from 'react'
import { Container, Card, Modal, Row, Col } from 'react-bootstrap';
import { Button } from 'react-bootstrap';
import Stack from '../../components/common/Stack';
import { Link } from 'react-router-dom';
import NewMedia from './NewMedia';
import SearchWrapper from './MediaManagerWrappers/SearchWrapper';
import ResultsWrapper from './MediaManagerWrappers/ResultsWrapper';
import SelectedMediaWrapper from './MediaManagerWrappers/SelectedMediaWrapper';
import { setErrorCatcher, setSuccessToast } from '../../utils/validation';
import SubHeader from '../../components/common/SubHeader';
import APITags from '../../api/Tags';
import './MediaManager.scss'

export const MediaManager = ({isModal=false, onSelection=()=>{}, multiSelect=false, }) => {
    const windowWidth = window.innerWidth;
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [hideFilters, setHideFilters]=useState(isModal);
    const [showNewMedia, setShowNewMedia]=useState(false);
    const [activeMedia, setActiveMedia]=useState();
    const [allTags, setAllTags]=useState([]);
    const [filter, setFilter]=useState({media_type: 1});
    const [refreshMedia, setRefreshMedia]=useState(false);

    const getTags=useCallback(async()=>{
        const response = await APITags.get();
        if (response?.data){
            let modifiedTags = addSelectedPlusToTag(response.data);
            setAllTags(modifiedTags);
        }
    },[]);


    const addTagHandler= async () =>{
        const tagName = prompt("Please enter a tag name");
        if (tagName){
            const response = await APITags.create({name: tagName});
            if (response?.data){
                getTags();
            }
        }
    }        

    const addSelectedPlusToTag=(tempTags)=>{
        for(let i = 0; i < tempTags.length; i++){
            tempTags[i].checked = false;
        }
        return tempTags
    }

    const closeNewMediaHandler = useCallback(()=>{
        setShowNewMedia(false);
        setRefreshMedia(true);
    },[]);

    useEffect(()=>{
        getTags();
    },[getTags]);

    useEffect(()=>{
        let results = null;
        if (multiSelect){
            results = [];
            if (activeMedia){
                let media = activeMedia;
                if (!Array.isArray(media)) media = [media];
                results = media.map(m=>({url: m.url, title: m.description || "", description: m.description || ""}));
            }
        } else if (activeMedia?.url) results = activeMedia.url;

        if (results && results?.length>0) {
            onSelection(results);
        }
    },[activeMedia, onSelection, multiSelect]);

    useEffect(()=>{
        return () => {
            setAllTags([]);
            setActiveMedia(null);
            setFilter(null);
            setRefreshMedia(false);
        }
    },[]);

    return (
        <Container fluid>
            {!isModal &&
                <SubHeader items={[
                    { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                    { text: "Media Manager" }
                ]} />
            }
            <Row className="body">
                <Col>
                    <Card className={`${isModal ? "modal-card" : "content-card"}`}>
                        <Stack direction="horizontal" gap={2}>
                            <h4 className="tm-1 section-title order-2 order-lg-1 ">
                                Media Manager
                                <div className="hide-items">
                                    <Button variant="light" size="sm" className="mt-2" onClick={()=>setHideFilters(!hideFilters)}>{!hideFilters ? "Hide Filters" : "Show Filters"}</Button>
                                </div>
                            </h4>
                            <div className="ms-sm-auto order-1 order-lg-2">
                                <Button variant="primary" onClick={()=>setShowNewMedia(true)}>Upload New Media</Button>
                            </div>
                        </Stack>
                        <hr/>

                        <Row className='media-manager-wrapper'>
                            {!hideFilters && 
                                <Col sm={12} lg={3}>
                                    <SearchWrapper 
                                        hideFilters={hideFilters}
                                        setHideFilters={setHideFilters}
                                        allTags={[...allTags]}
                                        onFilterChange={setFilter}
                                        onAddTag={addTagHandler}
                                    />
                                </Col>
                            }
                            <Col sm={12} lg={(hideFilters?9:6)+(isModal?3:0)} className="order-2 order-lg-1">
                                <ResultsWrapper 
                                    activeMedia={activeMedia} 
                                    setActiveMedia={setActiveMedia}
                                    setRefreshMedia={setRefreshMedia}
                                    refreshMedia={refreshMedia}
                                    filter={filter}
                                    multiSelect={multiSelect}
                                />
                            </Col>
                            {!isModal &&
                                <Col sm={12} lg={3} className="order-1 order-lg-2">
                                    <SelectedMediaWrapper
                                        activeMedia={activeMedia} 
                                        setActiveMedia={setActiveMedia} 
                                        allTags={[...allTags]}
                                        onAddTag={addTagHandler}
                                        setRefreshMedia={setRefreshMedia}
                                        multiSelect={multiSelect}
                                    />
                                </Col>
                            }
                        </Row>
                    </Card>
                </Col>
            </Row>

            <Modal show={showNewMedia} onHide={()=>setShowNewMedia(false)}>
                <Modal.Header closeButton />
                <Modal.Body>
                    <NewMedia isModal={true} onClose={closeNewMediaHandler}/>
                </Modal.Body>
            </Modal>
        </Container>
    );
}
