@import '../../assets/css/scss/variables';
@import '../../assets/css/scss/mixins';
@import '../../assets/css/scss/themes';

.menu-settings-btn-row{
    @include flex-row-space-around;
    @media (max-width: 400px){
        flex-direction: column-reverse;
    }
}
.menu-settings-wrapper{
    .button-group{
        .btn{
            margin-right: 5rem;
        }
    }
    .command-btns{
        width: 200px;
        @media (max-width: 350px){
            width: 150px;
        }
        @media (max-width: 280px){
            width: 100px;
        }
    }
    .btn-section:not(:last-child){
        margin-bottom: 3rem;
    }
}
.menu-item-modal{
    max-width: 80vw !important;
    @media(max-width: 575px){
        max-width: 100vw !important;
    }
}
.new-menu-item-wrapper{
    @include basic-flex-row;
    @media (max-width: 1070px){
        flex-direction: column-reverse;
    }
    .select-module{
        width: 400px;
        border: 1px solid $form-control-border-color;
        border-radius: $card-border-radius;
        overflow-x: hidden;
        overflow-y: scroll;
        height: 60vh;
        margin-right: 3rem;
        @media(max-width: 675px){
            width: 350px;
        }
        @media(max-width: 570px){
            width: 300px;
        }
    }
    #search-input{
        @include basic-input-select;
        margin-left: 5px;
        @media(max-width: 1070px){
            margin-top: 2rem;
        }
    }
    .pagination-p{
        margin-right: 40px;
    }
    input[type='checkbox']{
        @include checkbox;
        margin-right: 8px;
        margin-left: 15px;
    }
    label{
        @include basic-label;
    }
    .each-menu-item{
        border: 1px solid $primary-color;
        margin: 8px;
        padding: 4px;
        background-color: $primary-light-color;
        border-radius: $card-border-radius;
        width: 300px;
        p{
            margin: 3px;
        }
        @media(max-width: 570px){
            width: 200px;
        }
    }
    .active-item{
        border: 3px solid $tertiary-color;
    }
}
.menu-item-form{
    h5{
        margin-bottom: 1rem;
    }
    .menu-label{
        @include basic-label;
        width: 150px;
        font-size: 1rem;
        display: inline-block;
    }
    label{
        font-size: 1rem;
        width: 150px;
    }
    .inputs{
        input{
            width: 250px;
            @include basic-input-select;
            margin-bottom: 0;
            margin-top: 5px;
        }
        p{
            @include basic-flex-column;
            max-width: 280px;
        }
        .count{
            align-items: end;
        }
    }
    
}
.external-link{
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
    h4{
        margin-bottom: 1.5rem;
    }
    label{
        @include basic-label;
        margin-left: 3rem;
        width: 150px;
        font-size: 1rem;
        @media (max-width: 752px){
            margin-left: 0;
            padding-bottom: 5px;
        }
    }
    input{
        @include basic-input-select;
        width: 300px;
        @media (max-width: 460px){
            width: 200px;
        }
        @media(max-width: 320px){
            width: 150px;
        }
    }
}
.input-sets{
    @include basic-flex-column;
    align-items: center;
    p{
        margin: 0;
    }
    .character-count{
        align-self: end;
        margin-bottom: 1rem;
    }
}
.new-folder{
    max-width: 540px;
    margin-left: auto;
    margin-right: auto;
    h4{
        margin-bottom: 1.5rem;
    }
    label {
        @include basic-label;
        width:200px;
    }
    input{
        @include basic-input-select;
        width: 300px;
        @media(max-width: 380px){
            width: 200px;
        }
    }
}