import React from 'react';
import { But<PERSON> } from 'react-bootstrap'

import { CategoryTypeahead } from '../../../../components/Typeahead';

export const DefaultCategory=({
        defaultCategory=null,
        setDefaultCategory=null,
        handleCancel,
        handleSaveDefault,
    ...props})=>{

    return(
        <div>
            <label>
                    Default Category
                </label>
                <CategoryTypeahead 
                    multiple={false} 
                    passSelection={(selection)=>setDefaultCategory(selection)} 
                    initialDataIds={defaultCategory ? [defaultCategory] : null}    
                />
                <br />
                <p>
                    This is the category the register will fall back to if a category isn't selected for a certain timeframe.
                </p>
                <Button onClick={handleSaveDefault}>
                    Save Default
                </Button>
                <Button onClick={handleCancel}> 
                    Cancel
                </Button>
        </div>
    )
}

export default DefaultCategory;