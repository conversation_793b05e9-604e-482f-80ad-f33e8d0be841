@import '../../../assets/css/scss/themes.scss';

/* Filter Row */
.service-filter-row {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}


/* Week Date Picker */

.services-booking .service-selection .react-datepicker__week .react-datepicker__day--selected {
    background: $primary-color;
    border-radius: $date-picker-day-border-radius;
}
.services-booking.booking-large-screen .service-selection .react-datepicker__week:hover .react-datepicker__day {
    color: $primary-inverse-color;
    font-weight: $bold-font-weight;
    background: $primary-color;
    border-radius: $date-picker-day-border-radius;
}
.services-booking .service-filter-row .btn {
    color: $primary-color;
    padding: 5px 10px !important;
    margin: 0 0.3rem;
}
.services-booking .service-filter-row {
    margin-bottom: 1rem;
}
.services-booking .react-datepicker-popper {
    z-index: 50;
}
.services-booking .btn.datepicker-back-next {
    border-radius: 0.5rem;
    padding: 4px 9px !important;
    line-height: 18px;
}
.services-booking .btn.datepicker-back-next.back {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
}
.services-booking .btn.datepicker-back-next.next {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    /* border-left: 0; */
}
.services-booking .btn.btn.datepicker-back-next i {
    font-size: 1.3rem;
    margin-left: 0;
    margin-right: 0;
    color: $primary-color;
}
.services-booking .btn.datepicker-calendar {
    margin-left: 0;
}
.services-booking .btn.datepicker-calendar {
    margin-left: 0;
}
.services-booking .form-row.datepicker {
    justify-content: center;
    align-items: center;
}
.services-booking .datepicker .datepicker-calendar {
    border-radius: 0;
}

.services-booking .btn.btn-light:disabled:hover {
    background-color: #f8f9fa;
    border-color: #CCC;
}

.services-booking .btn.btn-light.datepicker-back-next:disabled:hover i, .services-booking .btn.btn-light.datepicker-back-next:disabled i  {
    color: #afafaf;
}
.services-booking .datepicker .btn-group {
    margin-right: 0;
}

.services-booking .datepicker .btn {
    margin-right: 0 !important;
}

.services-booking .form-label.description {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    color: $primary-font-color;
    font-weight: bold;
    padding-right: 15px;
}

.services-booking .form-label.question {
    margin-top: 0 !important;
    font-size: $secondary-font-size;
    color: $primary-font-color;
}

.services-booking .form-row {
    margin-top: 0;
    margin-bottom: 0;
}





/* Search Input */

.services-booking .search-input .form-control {
    border-right: 0;
}
.services-booking .search-input .input-group-append {
    background-color: white;
}
.services-booking .search-input i {
    color: $primary-color;
}
.services-booking .search-input .form-control:focus {
    border-color: #ced4da;
    box-shadow: none;
}
.services-booking .search-input:focus-within {
    border-color: #80bdff;
    -webkit-box-shadow: 0 0 4px 3px rgb(0 123 255 /25%);
    box-shadow: inset 0 8px 3px rgb(0 123 255 / 25%), 0 0 4px 3px rgb(0 123 255 / 25%);
    /* box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%); */
}


/* Service Listing */

.service-list-item {
    width: 100%;
    padding: 0.4rem 0.8rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid #CCC;
    font-size: 0.9rem;
    font-weight: 500;
}
.service-list-item:hover {
    background-color: $primary-color;
    border: 1px solid $primary-color;
    color: $primary-inverse-color;
    cursor: pointer;
}
.service-pagination-bar {
    width: 100%;
    display: flex;
    justify-content: center;
}
.services-booking .btn-light, .service-list-item {
    border-radius: 0.5rem;
}


/* Service Booker Tabs */

.services-booking .nav-tabs {
    width: 100%;
    border-bottom: 0;
    justify-content: center;
}
.services-booking .nav-tabs .nav-item {
    border-color: #dee2e6 !important;
    border-width: 2px;
    color: #555;
    margin: 0 0.2rem;
    font-weight: 600;
    border-radius: 0.25rem;
}
.services-booking .nav-tabs .nav-link.active {
    background: $primary-light-color;
    border-color: $primary-color;
    border-width: 2px;
    color: $primary-font-color;
    margin: 0 0.3rem 0 0;
    font-weight: $bold-font-weight;
}
.services-booking .tab-content {
    width: 100%;
}


/* Timeslots */
.services-booking .timeslot-display {
    font-weight: 500;
    font-size: 1rem;
    margin-top: 1rem;
}

.services-booking .error-text {
    color: $error-color;
}

.range-display-grid .form-check.form-radio input[type="radio"]:disabled + label,
.range-display-grid .form-check.form-radio input[type="checkbox"]:disabled + label {
    border: 1px solid rgb(158, 158, 158); 
    background-color: rgb(211, 211, 211);
    cursor: default;
}

.range-display-grid .form-check.form-radio input[type="checkbox"]:enabled + label:hover {
    border: 1px solid $secondary-color;
    background-color: $secondary-color;
    color: $primary-inverse-color;
}

.range-display-grid .form-check.form-radio {
    font-size: 0.70rem;
}


/* UL formatting */
.services-booking ul.service-description-list {
    list-style: outside none;
    margin-left: 1.3em;
}
.services-booking ul.service-description-list li {
    margin-top: 0.5rem;
}
.services-booking ul.service-description-list i {
    color: $primary-color;
    font-size: 1.2rem;
}
.services-booking .fa-li {
    left: 3rem;
    width: 1rem;
}
.services-booking .tokens-avail {
    color: $primary-color;
    font-size: 0.9rem;
    border: 1px solid $primary-color;
    padding: 0.3rem 1rem;
    border-radius: 4px;
}
.services-booking .tokens-avail.none {
    color: rgb(158, 158, 158);
    border: 1px solid rgb(158, 158, 158);
}


/* Service delete */
.delete-text {
    margin-top: $main-padding;
    margin-bottom: $main-padding;
}


.services-booking .summary-grid {
    overflow-x: visible;    // stop the grid from scrolling a tiny bit when it shouldn't be
}