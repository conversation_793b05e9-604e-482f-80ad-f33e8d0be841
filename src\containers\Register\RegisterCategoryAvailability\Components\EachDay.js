import React from 'react';
import { format, getYear, getDay } from 'date-fns'

import styles from './RegCatAvailComponents.module.scss'

//format should be day or date
export const EachDay =({day, hours, removeClick=null, handleEdit=null, format="day", ...props})=>{

    const getWeekdayForHours=(date)=>{
        let currentYear = (getYear(new Date()))
        let split = date.split("/");
        let thisYearDate = new Date(currentYear, +split[0]-1, +split[1]);
        return getDay(thisYearDate);
    }

    if(format==="date" && hours){
        let date = getWeekdayForHours(day?.date);
        let localHours = hours[date];
        day = {date: day.date, categories: day.categories}
        hours=localHours
    }

    return(
        <div className={styles["each-day-wrapper"]}>
            <div>
                <p>
                    <strong>
                        {day?.date}
                    </strong>
                    <br />
                    {hours?.closed ?
                        <strong>
                            CLOSED    
                        </strong>
                    :
                        <span>
                            {hours?.start ? 
                                <>
                                    <strong>Register Hours: {" "}</strong>
                                    <span>
                                        {hours.start} - {hours.end}
                                    </span>
                                </>
                            :
                                <strong>
                                    Open All Day
                                </strong>    
                            }
                        </span>
                    }
                </p>
            </div>
            <div>
                {day?.categories?.length ?
                    <>
                        <span>
                            Categories:
                            <br/>
                        </span>
                        {day?.categories?.map((item)=>(
                            <React.Fragment key={`each-category-${item.cat_id}`}>
                                {item?.cat_name && 
                                    <>
                                        {item?.start ?
                                            <span>
                                                {item?.start} - {item?.end}
                                            </span> 
                                            :
                                            <span>
                                                (All Day)
                                            </span>
                                        }
                                        {" "} -- {" "}
                                        <span>
                                            {item?.cat_name}
                                        </span>
                                        {" "}
                                        <i className="fas fa-times-circle error-text" onClick={()=>removeClick(item, day)} />
                                        {" "}
                                        <i className="fas fa-pencil-alt" onClick={()=>handleEdit({item, day})} />
                                        <br />
                                    </>
                                }
                            </React.Fragment>
                        ))}
                    </>    
                :
                    <p>
                        Default Category Only
                    </p>
                }   
            </div>
        </div>
    )
}

export default EachDay