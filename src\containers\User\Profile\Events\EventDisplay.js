import React, { useState } from 'react';
import { format } from 'date-fns';

import styles from './Events.module.scss';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';

export const EventDisplay =({day, canEditEvent=false, family=[], profileUser, authUserId, ...props})=>{

    const [showExtraDetails, setShowExtraDetails]=useState(null);
    const [showPaymentDetails, setShowPaymentDetails]=useState(null);

    const handleRowClick=(id)=>{
        if(showExtraDetails === id) setShowExtraDetails(null);
        else setShowExtraDetails(id);
    }

    const handlePaymentDetailsClick=(id)=>{
        if(showPaymentDetails === id) setShowPaymentDetails(null);
        else setShowPaymentDetails(id)
    }

    const findForUser=(eachEvent)=>{
        if(family.length === 0) return [profileUser];
        else{
            // let familyIds = family.map((member)=>member.user_id);
            // let theUser = eachEvent?.users?.find((each)=>familyIds.includes(each.id))
            // // if(theUser?.id === profileUser.id && authUserId === profileUser.id) return null;
            // if(theUser?.id === profileUser.id) return profileUser;
            // else return theUser;
            let eventFamily = []
            for(let i = 0; i < family.length; i++){
                let match = eachEvent.users.filter((user)=>user.id ===family[i].user_id)
                if(match.length > 0){
                    family[i].event_status_id = match[0].user_event_status_id
                    family[i].order_items = match[0].order_items
                    family[i].username = match[0].username
                    eventFamily.push(family[i])
                } 
            }
            return eventFamily
        }
    }


    return(
        <div className={`${styles["event-display-wrapper"]} ${new Date(day.endDate) < new Date() ? styles["past-event"] : null}`}>
            <h6>
                {day.dateFormat} {" "}
                {new Date(day.endDate) < new Date() && <span>(PAST DATE)</span>}
            </h6>
            <hr />
            {day?.events?.map((event)=>{
                let forUsers = findForUser(event);
                return(
                    <div className={styles["each-event"]} key={`event-display-${event.id}`}>
                        <div className={styles["main-details"]}>
                            <div className={`${styles.bold} ${styles["detail-div"]}`}>
                                <span>
                                    {format(new Date(event.start_datetime), "MMM dd (hh:mm aaa)")} - {format(new Date(event.end_datetime), "MMM dd (hh:mm aaa)")}
                                    <br />
                                    {showPaymentDetails ?
                                        <span onClick={()=>handlePaymentDetailsClick(null)} className="cp">
                                            <i className="fas fa-chevron-double-up" /> Hide Payment Details
                                        </span>
                                        :
                                        <span onClick={()=>handlePaymentDetailsClick(event.id)} className="cp">
                                            <i className="fas fa-chevron-double-down" /> Payment Details
                                        </span>
                                    }
                                </span>
                                <span>
                                    {event.name}
                                    <br />
                                    {showExtraDetails ?
                                        <span onClick={()=>{handleRowClick(null)}} className="cp">
                                            <i className="fas fa-chevron-double-up" /> Hide Event Details
                                        </span>
                                        :
                                        <span onClick={()=>{handleRowClick(event.id)}} className="cp">
                                            <i className="fas fa-chevron-double-down" /> Event Details
                                        </span>
                                    }
                                </span>
                            </div>
                        </div>
                        {showPaymentDetails === event?.id &&
                            <div className={styles["detail-div"]}>
                                {forUsers.length && forUsers?.map(user=>(
                                    <div key={`payment-details-${user.user_id}-${event.id}`}>
                                        {authUserId && authUserId === user?.user_id ?
                                            <span className={styles["bold"]}>
                                                For Me 
                                            </span>
                                            :
                                            <span className={styles["bold"]}>
                                                For {user?.first_name} {" "} {user?.last_name} ({user?.username}) 
                                            </span>
                                        }
                                        {user?.order_items?.length > 0 && user?.order_items[0]?.order_id && 
                                            user?.order_items?.map((item, i)=>{
                                                let order = event?.orders?.filter((order)=>order?.order_id === item.order_id);
                                                let variant;
                                                if(event?.variants?.length > 0 ) variant = event?.variants?.filter((variant)=>variant.id === item.product_variant_id)
                                                return(
                                                    <div className={styles["order-details"]} key={`order-details-${item.id}`}>
                                                        <span>
                                                            Order #{item?.order_id} <Link to={`/p/order/${item?.order_id}`} target="_blank">(Open Order in New Tab)</Link> 
                                                        </span>
                                                        <br />
                                                        <span className={styles["order-variant"]}>
                                                            {variant && variant.length > 0 &&
                                                                <span>
                                                                    {variant[0]?.name} (${variant[0]?.price})
                                                                </span>
                                                            }
                                                            {order && order.length > 0 &&
                                                                <span>
                                                                    ${order[0]?.payment_total?.toFixed(2) || 0.00}/${item.final_price?.toFixed(2) || 0.00}  
                                                                </span>
                                                            }
                                                        </span>
                                                        {i < user?.order_items?.length - 1 && <hr />}
                                                    </div>
                                                )
                                            })
                                        }
                                    </div>
                                ))}
                            </div>
                        }
                        {showExtraDetails===event.id &&
                            <div className={styles["event-extra-details"]}>
                                <span className={styles["head-labels"]}>
                                    Location
                                </span>
                                <p>
                                    {event?.location_name}
                                </p>
                                <span className={styles["head-labels"]}>
                                    Event Type
                                </span>
                                <p>
                                    {event?.event_type_name}
                                </p>
                                {event.description ? 
                                    <div className={styles["description-section"]}>
                                        <span className={styles["head-labels"]}>
                                            Description
                                        </span>
                                        <p dangerouslySetInnerHTML={{__html: event.description}} />
                                        {canEditEvent &&
                                            <p>
                                                <a href={`/p/events/${event.id}`}>Manage Event</a>
                                            </p>
                                        }
                                    </div>
                                :
                                    <span>
                                        No Event Description Found
                                    </span>
                                }
                            </div>
                        }
                    </div>
                )
            })}
                    
        </div>
    )
}

export default EventDisplay;