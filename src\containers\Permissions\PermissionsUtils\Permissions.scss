@import "../../../assets/css/scss/variables";
@import "../../../assets/css/scss/themes";
@import "../../../assets/css/scss/mixins";

.permissions-wrapper{
    .row-pair{
        @include basic-flex-row;
    }
    .input-col{
        @include basic-flex-column;
    }
    input:not([type="checkbox"]):not([type="radio"]), select{
        @include basic-input-select;
        margin-left: 0px !important;
        margin-right: 0px !important;
    }
    label{
        @include basic-label;
    }
}