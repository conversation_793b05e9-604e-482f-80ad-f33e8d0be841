import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';

import Transactions from '../../../api/Transactions';
import Events from '../../../api/Events';
import { authUserHasModuleAccessMany } from '../../../utils/auth';

const EVENT_PAYMENT_REPORT_MODULE_ID = 334;
const VIEW_EVENT_ATTENDEES_MODULE_ID = 77;

export const EventDownloads = ({event, stacked=false, ...props})=>{

    const [ loading, setLoading ]=useState(false);
    const [ modulePermissions, setModulePermissions]=useState(null)

    useEffect(()=>{
        const getModulePermissions=async()=>{
            setLoading(true)
            let response = await authUserHasModuleAccessMany([EVENT_PAYMENT_REPORT_MODULE_ID, VIEW_EVENT_ATTENDEES_MODULE_ID])
            setModulePermissions(response)
            setLoading(false)
        }

        getModulePermissions()

    },[])

    const getEventAttendees=async()=>{
        setLoading(true)
        try{
            let response = await Events.export_attendees({id: event?.id})
            if(response.data) {
                handleUri(response.data[0].uri);
            } else if(response.errors){
                console.error(response.errors);
            }else console.error("There was an unknown error getting the event attendees")
        }catch(ex){
            console.error(ex)
        }
        setLoading(false)
    }

    const getEventReport=async()=>{
        setLoading(true)
        try{
            let response = await Transactions.Reports.getEventPayments({event_name: event?.name})
            if(response.status === 200 && response.data){
                handleUri(response.data?.uri)
            }else if(response.errors) console.error(response.errors)
            else console.error("There was an unknown problem getting the event report")
        }catch(ex){
            console.error(ex)
        }
        setLoading(false)
    }

    const handleUri = (uri)=>{
        let download = document.createElement('a');
        download.href = uri;
        download.setAttribute('download', '');
        document.body.appendChild(download);
        download.click();
        download.parentNode.removeChild(download);
    }

    return(
        <div >
            {modulePermissions && modulePermissions[VIEW_EVENT_ATTENDEES_MODULE_ID] && 
                <Button onClick={getEventAttendees} disabled={loading}>
                    Download Attendees
                </Button>
            }
            {modulePermissions && modulePermissions[EVENT_PAYMENT_REPORT_MODULE_ID] && 
                <Button onClick={getEventReport} disabled={loading}>
                    Download Event Payments Report
                </Button>
            }
        </div>
    )
}