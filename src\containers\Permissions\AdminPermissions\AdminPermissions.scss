@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/themes';

.admin-permission-dash-wrapper{
    display: flex;
    flex-direction: column;
    .typeahead-select{
        @include basic-flex-column;
        width: 700px;
        input{
            margin-top: 1rem;
        }
    }
    .large-col{
        @include basic-flex-row;
        flex-wrap: nowrap;
        width: 700px;
        justify-content: space-between;
        margin-top: 2rem;
        button{
            @include basic-button;
            border: 2px solid transparent;
            &:active{
                background-color: $primary-color;
            }
            &:hover{
                background-color: $primary-color;
                border: 2px solid $tertiary-color;
            }
        }
        a{
            text-decoration: none;
        }
    }
    .small-col{
        width: 340px;
        padding: 1rem;
    }
    .padding{
        padding: 1rem;
    }
    .bordered{
        border: 1px solid $divider-color;
        border-radius: 5px;
    }
    .perm-row{
        @include basic-flex-row;
        div{
            min-width: 275px;
        }
    }
    .quick-links{
        @include basic-flex-row;
        p{
            @include basic-flex-column;
            &:first-child{
                margin-right: 10px;
            }
        }
    }
    .secondary-btns, .long-btns{
        button{
            min-width: 90%;
        }
    }
    .secondary-btns{
        button{
            background-color: $secondary-color;
            border: 2px solid transparent;
            &:hover{
                background-color: $secondary-color;
                border: 2px solid $tertiary-color;
            }
            &:active{
                background-color: $secondary-color;
            }
        }   
    }
    .outline-btns{
        button{
            background-color: transparent;
            border: 2px solid $primary-color;
            color: $primary-color;
            width: 163px;
            &:hover{
                border-color: $tertiary-color;
                background-color: transparent;
                color: $primary-color;
            }
        }
    }
}