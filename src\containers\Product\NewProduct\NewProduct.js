import React, {useState, useEffect, useCallback, useRef } from 'react'
import {Button, Container, Row, Col, Card} from 'react-bootstrap';
import { useParams, useHistory, Link } from 'react-router-dom';
import { formatISO } from 'date-fns';
import { useSelector } from 'react-redux';
import SubHeader from '../../../components/common/SubHeader';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';
import BasicProductDetails from './BasicProductDetails';
import Bundles from './Bundles';
import Variants from './Variants'
import DisplayDetails from './DisplayDetails';
import { AddAVariant } from './Variants/VariantTypes';
import { Tutorials } from '../../../components/Tutorials/Tutorials';

import Products from '../../../api/Products';
import Services from '../../../api/Services';

import usePrevious, { useRoleCheck } from '../../../components/common/CustomHooks';

import './NewProducts.scss'

//from product dashboard 
//src\containers\Service\CreateWizard\Payment.js
//src\containers\Permissions\Features\CreateEditFeature\CreateEditFeature.js

/**
 * can be used as a modal as well 
 * to create a new product, use @param {{}} importType with the product type(9 for tokens, 1 for service, etc)
 * to edit a product, do NOT import the type and @param {{}} importId of the product id.  It will use that instead of params to load a product
 */
export const NewProduct = ({importedType=null, importedId=null, onHide=()=>{}}) => {

    const mountedRef = useRef(false);
    //when bringing in a product to edit, need to tell the children components to chill and wait while initial props are being set up;
    //We cannot rely on considering it loaded based on these having data because some of them might not have any, depending on the product;
    const editingFirstLoad = useRef();
    const params=useParams();
    const history=useHistory();
    const company_id=useSelector(state=>state.auth.user.company_id)
    const role = useRoleCheck();

    const [ error, setError ]=useState();
    const [ success, setSuccess ]=useState();
    const [ loading, setLoading]=useState(true);
    const [ localErrors, setLocalErrors ]=useState([]);
    const [ warnings, setWarnings ]=useState([]);
    const [ editingProduct, setEditingProduct]=useState();
    const [ firstLoad, setFirstLoad]=useState(true);
    const [ confirmWarning, setConfirmWarning]=useState(false);
    const [ basicProductDetails, setBasicProductDetails ]=useState({});
    const [ basicProductReset, setBasicProductReset]=useState(false)
    const [ selectedProductType, setSelectedProductType ]=useState(importedType);
    const [ tokensForBundles, setTokensForBundles ]=useState(); //used to hold both service tokens and bundles of bundles
    const [ includeToken, setIncludeToken ] =useState(false);
    const [ createdVariants, setCreatedVariants ]=useState([]);
    const [ productTypeList, setProductTypeList ]=useState([]);
    const [ productStatuses, setProductStatuses ]=useState([]);
    const [ jointAddOnCategories, setJointAddOnCategories ]=useState([]);
    const [ editMe, setEditMe ]=useState(false);
    //eventually these can be used to save a preference to company, default to showing either of these on load for each item or hiding them.  Hiding by default for now
    const [ defaultShowMeasurements, setdefaultShowMeasurements]=useState(false);
    const [ defaultShowSKUs, setDefaultShowSKUs]=useState(false); 
    //to tell the children to dump their data, such as in the case of changing product type on a new product
    const [ resetChildren, setResetChildren ] =useState(0);
    //this state is used in two different ways.  When editing a token product, displays services associated.  When setting a bundle with a token, allows display of that token's services
    const [ associatedServices, setAssociatedServices ]=useState([]); 
    const [ orphanToken, setOrphanToken ]=useState(false);
    const [ matchToFeature, setMatchToFeature ]=useState([]); 
    //to tell this component that an image has changed:
    const [imageChanged, setImageChanged ]=useState(false);
    const previousProductType=usePrevious(selectedProductType);

//#region useCallback
    const getServices=useCallback(async(productId)=>{
        try{
            let response = await Services.get({
                product_ids:[productId]
            })
            if(response.status===200) {
                setAssociatedServices(response.data.services)
            }
        }catch(ex){console.error(ex)}
    },[])

    const getProductTypes=useCallback(async()=>{
        try{
            let response = await Products.Types.get()
            if(!response.errors && response.data && mountedRef.current){
                if(role.id===1) setProductTypeList(response.data)
                //filtering out the feature type for all but SB (role 1)
                else setProductTypeList(response.data.filter(type=>type.id!==11)) 
            }else if(response.errors){
                setError(response.errors)
            }
        }catch(ex) {console.error(ex)};
    },[role.id]);

    const getProductStatuses=useCallback(async()=>{
        try{
            let response = await Products.Status.get()
            if(!response.errors && response.data && mountedRef.current){
                setProductStatuses(response.data)
            }else if(response.errors) setError(response.errors)
        }catch(ex){console.error(ex)}
    },[]);

    const getExistingProduct=useCallback(async(id)=>{
        try{
            let response = await Products.get({id: parseInt(id)});
            if(!response.errors && response.data && mountedRef.current){
                setEditingProduct(response.data.products[0]);
                setLoading(true);
            } 
            else if(response.errors) {
                setError(<ErrorCatcher error={response.errors} />)
                setLoading(false)
            }
        }catch(ex){
            console.error(ex)
            setLoading(false)
        }
    },[]);

    const cleanUp=useCallback(()=>{
        if(selectedProductType===1) setCreatedVariants([{temp_id: 1, name: "Default", bill_interval:"m"}])
        else setCreatedVariants([{temp_id: 1, name: "Default"}]);
        setTokensForBundles();
        setResetChildren(resetChildren => resetChildren+1);
        setLocalErrors([]);
        setWarnings([]);
        setAssociatedServices([]);
        setImageChanged(false);
    },[selectedProductType]);

    const checkBoxErrorHandler=useCallback((submit)=>{
        //check that it's submitting before pushing these errors.  
        //We want to hide them when appropriate, but not create/show them until submission
        const tempWarnings=[];
        let activeVariants = createdVariants.some(variant=>{
            return variant.product_status_id===1})
        if(submit && (basicProductDetails.product_status_id !== 1) && activeVariants && createdVariants.length > 1){ //if created variants.length !> 1, it will match the product status by default
            tempWarnings.push('You have selected a product status that is not "Available" but variant statuses that are.  If you save like this, your variants will be reverted to match the parent product.')
        }
        if(submit && basicProductDetails.category_ids?.length < 1 ){ 
            tempWarnings.push("You have not selected any categories for this product.  This product will still be created but will not show up in any online stores or as products in physical registers.")
        }
        if(mountedRef.current) setWarnings(tempWarnings)
        return {warnings: tempWarnings.length}
    },[basicProductDetails, createdVariants])

    /**Creates appropriate error messages for missing fields */
    const errorHandler = useCallback(()=>{
        const tempLocalErrors=[];
        //check basic info for required fields
        if(!basicProductDetails.hasOwnProperty("name") || !basicProductDetails.name){
            tempLocalErrors.push(
                <span data-cy="err-product-name">
                    * You must name your product.
                </span>
            );
        } if(!basicProductDetails.hasOwnProperty("date_available") || !basicProductDetails.date_available){
            tempLocalErrors.push(
                <span data-cy="err-product-date-available">
                    * You must select a start date for your product.
                </span>
            )
        }
        //This shouldn't be able to be null or undefined by just in case
        if(createdVariants.length===1 
            && (!basicProductDetails.hasOwnProperty("product_status_id") 
            || !basicProductDetails.product_status_id)){
            tempLocalErrors.push(
                <span data-cy="err-no-status">
                    * You have to have a status selected for your product.
                </span>
            );
        } 
        if(selectedProductType===6 && orphanToken){
            tempLocalErrors.push(
                <span data-cy="err-orphan-token">
                    * You must select a token that is associated with an event or service.
                </span>
            )
        }

        //check variant requirements
        createdVariants.forEach((variant)=>{
            if(!variant.hasOwnProperty("name") 
            || !variant.name){
                tempLocalErrors.push(
                    <span data-cy="err-variant-name">
                        * You must add a name <span className="bold">{variant.name ? 
                            `${variant.name}` : `Variant-${variant.temp_id}`}</span>.
                    </span>);
            }
            if(!variant.hasOwnProperty("price")
            || (variant.price === null && variant.price ===undefined)){
                tempLocalErrors.push(
                    <span data-cy="err-variant-price">
                        * You must add a price to your variant, <span className="bold">{variant.name ? 
                            `${variant.name}` : `Variant-${variant.temp_id}`}</span>, even if that price is 0.
                    </span>);
            }
            if((variant.hasOwnProperty("date_available") 
            && (variant.hasOwnProperty("date_available_until")) 
            && (variant.date_available && variant.date_available_until) 
            && new Date(variant.date_available) > new Date(variant.date_available_until))){
                tempLocalErrors.push(
                    <span data-cy="err-start-data-greater-end">
                        * You cannot select a start date that's later than the end date on <span className="bold">{variant.name ? 
                            `${variant.name}` : `Variant-${variant.temp_id}`}</span>.
                    </span>
                )
            }
            if(variant.hasOwnProperty("is_shippable")) {
                if(variant.is_shippable 
                && variant.hasOwnProperty("weight") 
                && (!variant.weight 
                    || isNaN(parseFloat(variant.weight)) 
                    || parseFloat(variant.weight)===0)){
                    tempLocalErrors.push(
                        <span data-cy="err-no-weight">
                            * You must add a weight to your variant, <span className="bold">{variant.name ? 
                                `${variant.name}` : `Variant-${variant.temp_id}`}</span> because you have marked it as "Shippable".
                        </span>);
            }
            if(variant.is_shippable 
            && !variant.hasOwnProperty("length") 
            && (!variant.length 
                || isNaN(parseFloat(variant.length) 
                || parseFloat(variant.length)===0))){
                    tempLocalErrors.push(
                        <span data-cy="err-no-length">
                            * You must add a length to your variant, <span className="bold">{variant.name ? 
                                `${variant.name}` : `Variant-${variant.temp_id}`}</span> because you have marked it as "Shippable".
                        </span>);
            }
            if(variant.is_shippable 
            && !variant.hasOwnProperty("height") 
            && (!variant.height 
                || isNaN(parseFloat(variant.height) 
                || parseFloat(variant.height)===0))){
                    tempLocalErrors.push(
                        <span data-cy="err-no-height">
                            * You must add a height to your variant, <span className="bold">{variant.name ? 
                                `${variant.name}` : `Variant-${variant.temp_id}`}</span> because you have marked it as "Shippable".
                        </span>);
            }
            if(variant.is_shippable 
            && !variant.hasOwnProperty("width") 
            && (!variant.width 
                || isNaN(parseFloat(variant.width) 
                || parseFloat(variant.width)===0))){
                    tempLocalErrors.push(
                        <span data-cy="err-no-width">
                            * You must add a width to your variant, <span className="bold">{variant.name ? 
                                `${variant.name}` : `Variant-${variant.temp_id}`}</span> because you have marked it as "Shippable".
                        </span>);
            }
        }
        if(selectedProductType===1){
            if(!variant.hasOwnProperty("bill_interval")){
                tempLocalErrors.push(
                    <span data-cy="err-no-interval-quantity">
                        * You must select an bill interval for a subscription type.  It's missing on <span className="bold">{variant.name ?
                        `${variant.name}` : `Variant-${variant.temp_id}`}</span>.
                    </span>
                )
            }
            if(variant.hasOwnProperty("bill_interval") 
            && variant.bill_interval === "m" 
            && !variant.hasOwnProperty("interval_quantity")){
                tempLocalErrors.push(
                    <span data-cy="err-no-interval-quantity">
                        * If you select a monthly interval, you must specify how many months between billings you want for <span className="bold">{variant.name ?
                        `${variant.name}` : `Variant-${variant.temp_id}`}</span>.
                    </span>
                )
            }
            if(variant.hasOwnProperty("bill_interval") 
            && variant.bill_interval==="m" 
            && variant?.interval_quantity <1 && variant.interval_quantity >11){
                tempLocalErrors.push(
                    <span data-cy="err-no-interval-quantity">
                        * The interval quantity for <span className="bold">{variant.name ?
                        `${variant.name}` : `Variant-${variant.temp_id}`}</span> must be between 1 and 11.
                    </span>
                )
            }
            if(variant.hasOwnProperty("interval_quantity")
                && variant.hasOwnProperty("bill_interval") 
                && variant.bill_interval==="m" 
                && (variant.interval_quantity <1 || variant.interval_quantity >11)){
                tempLocalErrors.push(
                    <span data-cy="err-no-interval-quantity">
                        * You must select an interval quantity with a billing interval on <span className="bold">{variant.name ? 
                        `${variant.name}` : `Variant-${variant.temp_id}`}</span>.  That interval quantity must be between 1 and 11 months.
                    </span>
                )
            }
            if(!variant.hasOwnProperty("subscription_type_id")){ //it shouldn't be able to be nullable, but just in case
                tempLocalErrors.push(
                    <span data-cy="err-no-subscription-type">
                        * You must select a subscription type for <span className="bold">{variant.name ?
                        `${variant.name}` : `Variant-${variant.temp_id}`}</span>.
                    </span>
                )
            }
            if(variant.hasOwnProperty("subscription_type_id") && variant?.subscription_max_users < 1){
                tempLocalErrors.push(
                    <span data-cy="err-subscription-no-max-users">
                        * You must select a max number of users <span className="bold">{variant.name ?
                        `${variant.name}` : `Variant-${variant.temp_id}`}</span>.
                    </span>
                )
            }
        }
    });

    //Bundle check
    if(includeToken && !tokensForBundles){
        tempLocalErrors.push(
            <span data-cy="err-token-details">
                * You must select a token and quantity because you selected "yes" for bundle.
            </span>
        );
    };

    setLocalErrors(tempLocalErrors)
    return {errors:tempLocalErrors.length}
},[createdVariants, basicProductDetails, includeToken, tokensForBundles, selectedProductType, orphanToken]);

//#endregion useCallback

//#region useEffect
    //First load
    useEffect(()=>{
        mountedRef.current = true

        return()=> mountedRef.current = false;
    },[]);

    useEffect(()=>{
        if(!editMe){
            editingFirstLoad.current={
                edit: false,
                bundleDone: false,
                variantsDone: false,
                basicInfoDone: false,
            }
            setFirstLoad(true);
        }
    },[editMe])

    useEffect(()=>{
        if(((params?.id && !importedType) || importedId) && mountedRef.current && firstLoad){
            editingFirstLoad.current={
                edit:true,
                loadedVariants: 0
            }
            let id;
            //imported type needs to come first.  If param came first, it may pick up a param from a parent page
            if(importedId) {
                id = importedId; 
                setEditMe(true); //when coming from a modal, we want to bypass seeing the details
            }
            else if(params?.id) {
                id = params.id
                setEditMe(false) //we want to see the details when coming from the dashboard
            }
            getExistingProduct(id);
            setFirstLoad(false);
        } 
    },[params, getExistingProduct, firstLoad, importedType, importedId]);

    useEffect(()=>{
        if(mountedRef.current){
            getProductStatuses();
            getProductTypes();
        };
        if((!params.id && !importedId) || importedType) { //allows modal with only imported type to appear correctly
            setLoading(false);
            if(importedType) setEditMe(true)
            editingFirstLoad.current={
                edit: false,
                bundleDone: true,
                variantsDone: true,
                basicInfoDone: true
            }
        }
        if(!params.id && !importedId && !importedType){
            setLoading(false);
            setEditMe(true)
            editingFirstLoad.current={
                edit: false,
                bundleDone: true,
                variantsDone: true,
                basicInfoDone: true
            }
        }
    },[getProductStatuses, getProductTypes, params, importedId, importedType]);

    useEffect(()=>{
        if(editingProduct && editingFirstLoad.current.edit) {
            getServices(editingProduct.id);
            let bundleDone;
            if(editingProduct.product_type_id === 1 || editingProduct.product_type_id === 6) bundleDone=false;
            else bundleDone=true;
            editingFirstLoad.current={
                edit: true,
                variantsDone:false,
                basicInfoDone:false,
                bundleDone: bundleDone,
                loadedVariants: 0,
                numberOfVariants:editingProduct?.product_variants?.length
            }
            if(editingProduct.product_variants?.length ===0){
                if(selectedProductType===1) setCreatedVariants([{temp_id: 1, name: "Default", bill_interval:"m", interval_quantity: 1, subscription_type_id: 1}])
                else setCreatedVariants([{temp_id: 1, name: "Default"}]);
            }else{
                for(let i = 0; i < editingProduct?.product_variants?.length; i++){
                    if(editingProduct?.product_variants[i]?.name?.toLowerCase()==="default" 
                    || editingProduct?.product_variants[i]?.name?.toLowerCase()=== " ") {
                        editingProduct.product_variants[i].temp_id=1
                    }
                    else editingProduct.product_variants[i].temp_id = editingProduct.product_variants[i].id
                    //these fields are required for subscription products but weren't always.  
                    //Making sure that if we load an older product that doesn't have them, we give them a default
                    //This is what allows us to load and save without having to edit these variants if we don't want to, despite being old and outdated
                    if(editingProduct?.product_variants[i]?.bill_interval && editingProduct?.product_variants[i]?.bill_interval==="m" && !editingProduct?.product_variants[i]?.interval_quantity){
                        editingProduct.product_variants[i].interval_quantity=1;
                    }
                    if(editingProduct?.product_type_id === 1 && !editingProduct?.product_variants[i].subscription_type_id) editingProduct.product_variants[i].subscription_type_id = 1;
                };
                setCreatedVariants(editingProduct?.product_variants);
            }
            setTokensForBundles(editingProduct?.bundled_products);
            if(editingProduct?.bundled_products?.length > 0) setIncludeToken(true);
            let tempProduct ={
                id: editingProduct?.id,
                name: editingProduct?.name,
                description: editingProduct?.description,
                print_locations: editingProduct?.print_locations,
                is_taxable: editingProduct?.is_taxable,
                date_available_until: editingProduct?.date_available_until,
                categories: editingProduct?.categories,
                product_status_id: parseInt(editingProduct?.product_status_id),
                product_type_id: editingProduct?.product_type_id,
                sort_order: editingProduct?.sort_order,
                track_inventrory: editingProduct?.track_inventrory,
                media: editingProduct?.media
            };
            if(!editingProduct.date_available) tempProduct.date_available = new Date()
            else if(editingProduct?.date_available?.includes("1970") || editingProduct?.date_available?.includes("1969")) tempProduct.date_available = new Date()
            else tempProduct.date_available=new Date(editingProduct?.date_available);
            setBasicProductDetails(tempProduct);
            setSelectedProductType(editingProduct?.product_type_id);
            setLoading(false);
        }
    },[editingProduct, editingFirstLoad, selectedProductType, getServices, importedId, params]);

    //reset bundles/variants,etc after changing product type.
    useEffect(()=>{
        if(previousProductType !== selectedProductType && !editingProduct) cleanUp();
    // do not want to add editing product as it's a conditional, not a trigger
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[selectedProductType, previousProductType, cleanUp]);

    //if variant length goes back to one, name goes back to default
    useEffect(()=>{
        if(createdVariants.length===1 && resetChildren >0){
            let variant = createdVariants[0]
            variant.name="Default"
        };
    },[createdVariants, resetChildren]);

    useEffect(()=>{
        if(selectedProductType && basicProductDetails) checkBoxErrorHandler();
    },[basicProductDetails, checkBoxErrorHandler, selectedProductType])

//#endregion useEffects


//#region Handlers
const newVariantClickHandler=()=>{
    setWarnings([]);
    setLocalErrors([]);

    //logging the temp_ids so there are no duplicates
    let tempIds=[]
    if(createdVariants.length>0){
        createdVariants.forEach((variant)=>{
            tempIds.push(variant.temp_id)
        })
    }
    let tempVariants=[...createdVariants]
    let newTempId=createdVariants.length+1
    while(tempIds.includes(newTempId)){
        newTempId+=1
    }
    let newVariant={
        temp_id: newTempId,
        name: "New Variant"
    }
    tempVariants.push(newVariant)
    setCreatedVariants(tempVariants);
}

const removeVariantHandler=(variantTempId)=>{
    let tempVariants=[...createdVariants]
    let removedVariant = tempVariants.findIndex(variant=>{
        return variant.temp_id === variantTempId
    })
    tempVariants.splice(removedVariant, 1)
    setCreatedVariants(tempVariants);
}

//#endregion Handlers


//#region Functions

    //variable for true/false create another
    const onSubmit = useCallback((anotherProduct, e)=>{

        const uploadProductImage=async(productId, edit=false)=>{
            basicProductDetails.media.append('product_id', productId);
            try{
                let response = await Products.addImage(basicProductDetails.media)
                console.log(response)
            }catch(ex){console.error(ex)}
        }

        const createTheProduct=async(product, anotherProduct)=>{
            try{
                let response
                if(product.id) {
                    response = await Products.update(product)
                    if(basicProductDetails.media && imageChanged) uploadProductImage(product.id)
                }else response = await Products.create(product)
                
                if(!response.errors && response.status===200){
                    setSuccess("Product Created Successfully");
                    if(!product.id && response?.data?.id){
                        uploadProductImage(response?.data?.id); //handle the upload of an image on a new product
                    }

                    //pushing to the page because if a user started out editing a product, they're on the wrong page
                    if(!importedType && !importedId){
                        if(anotherProduct) history.location.pathname==="/p/products/new" ? history.go(0) : history.push('/p/products/new');
                        else if(!anotherProduct) history.push('/p/products/dashboard')
                    }else if(importedType===11){
                        let includeIds=[]
                        let variants = response.data.variants
                        for(let i=0; i < matchToFeature.length; i++){
                            if(matchToFeature[i].include){
                                let variant = variants.filter((variant)=> variant.temp_id === matchToFeature[i].temp_id)
                                if(variant) includeIds.push(variant[0].product_variant_id)
                            } 
                        }
                        onHide(true, includeIds);
                    }
                    else if(importedId){
                        onHide();
                    }
                }else if(response.errors){
                    setError(response.errors);
                }
            }catch(ex){console.error(ex)}
        };

        e.preventDefault();
        setError();
        setSuccess();

        let error = errorHandler();
        let warning = checkBoxErrorHandler(true);

        let fullStop = {...error, ...warning};

        //removing categories because we have category ids now
        delete basicProductDetails.categories

        //temporary for endpoint
        // delete basicProductDetails.date_available_until;

        if(basicProductDetails.hasOwnProperty('date_available')) basicProductDetails.date_available=formatISO(new Date(basicProductDetails.date_available));
        if(basicProductDetails.hasOwnProperty('date_available_until')) basicProductDetails.date_available_until=formatISO(new Date(basicProductDetails.date_available_until));

        let includeNewFeature = []

        createdVariants.forEach((variant)=>{
            if(variant.hasOwnProperty('date_available')){
                if(variant?.date_available?.toString().includes("1970")  || variant?.date_available?.toString().includes("1969")) variant.date_available=null
                else variant.date_available=formatISO(new Date(variant.date_available));
            } 
            if(variant.hasOwnProperty('date_available_until')){
                if(variant?.date_available_until?.toString().includes("1970")  
                || variant?.date_available_until?.toString().includes("1969")
                || !variant?.date_available_until
                ) variant.date_available_until=null
                else variant.date_available_until=formatISO(new Date(variant.date_available_until));
            }
            if(variant.hasOwnProperty('add_on_categories')) variant.add_on_categories.map(category=>category.id);
            if(editingProduct) variant.product_id = editingProduct.id;
            if(importedType===11){
                includeNewFeature.push({temp_id: variant.temp_id, include: variant.include_new })
                delete variant.include_new;
            }
        })

        setMatchToFeature(includeNewFeature);

        let product = {
            company_id: company_id,
            product_type_id: selectedProductType,
            ...basicProductDetails,
            variants: [...createdVariants],
            bundled_products: tokensForBundles
        };
        //removing the available until because it's handled by the individual variants now.  
        product.date_available_until = null;

        if(tokensForBundles?.length > 0) {
            let qty=0;
            if(tokensForBundles[0].hasOwnProperty('quantity') && parseInt(tokensForBundles[0].quantity > 0 && tokensForBundles[0].id)) {
                qty = parseInt(tokensForBundles[0].quantity);
            }
            product.bundled_products = 
            [
                {
                    id: tokensForBundles[0].id,
                    qty: qty
                }
            ]
        }
        
        if(createdVariants.length===1){
            createdVariants[0].product_status_id = basicProductDetails.product_status_id
            createdVariants[0].is_taxable = basicProductDetails.is_taxable
            createdVariants[0].name = "Default" //this should still be in tact, but just in case 
        }
        if(confirmWarning && basicProductDetails.product_status_id !== 1 && createdVariants.some(variant=>variant.product_status_id===1)){
            product.variants.forEach((variant)=>{
                variant.product_status_id = basicProductDetails.product_status_id
            })
        }

        if(fullStop.errors===0 && (fullStop.warnings === 0 || confirmWarning))createTheProduct(product, anotherProduct);
    },[tokensForBundles, createdVariants, basicProductDetails, selectedProductType, company_id, confirmWarning, errorHandler, checkBoxErrorHandler, editingProduct, history, importedType, onHide, matchToFeature, importedId]);

    const handleCancel=()=>{
        if(!importedType && !importedId) history.push("/p/products/dashboard");
        else onHide(null, null);
    }

//#endregion Functions

const subHeaderItems = [
    { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
    { linkAs: Link, linkProps: { to: "/p/products/dashboard" }, text: "Product Dashboard" },
]

if(editingProduct) subHeaderItems.push({text: editingProduct.name})
else subHeaderItems.push({text: "New Product"})

  return (
    <>
        {!importedType && !importedId &&
            <SubHeader 
            items = {subHeaderItems}
            tutorials = {[
                { tutorialSection:"Products",
                allSectionTutorial:false,
                navigation:false,
                basicInfo:false,
                subSection:["Create", "Variants", "Product Types", "Addons"]}
            ]}/>
        }
        {editMe ?
            <div className="new-product-wrapper">
                <Card>
                    {/*select the product type */}
                    <div className="new-product-header" data-cy="header-wrapper">
                        {editingProduct && !loading ? 
                            <>
                                    <div className="center-header">
                                        <h4 data-cy="header-title" className="center-header">
                                            Edit a <span className="bold">{editingProduct.product_type_name}</span> Product
                                            {" "}
                                            <i className="far fa-bags-shopping"></i>
                                        </h4>
                                        {!importedId ?
                                            <Button onClick={()=>{setEditMe(false)}}>View Product</Button>
                                            :
                                            <Button onClick={()=>{onHide()}}>Close</Button>
                                        }
                                    </div>
                            </>
                        :
                            <>
                                <div className="flex-direction-col">
                                    <h4 data-cy="header-title">
                                        Create a New Product
                                        {" "}
                                        <i className="far fa-bags-shopping"></i>
                                    </h4>
                                    {!importedType &&
                                        <>
                                            <label htmlFor="product-type-select">Select Product Type</label>
                                            <select onChange={(e)=>setSelectedProductType(+e.target.value)}  data-cy="select-product-type">
                                                <option value={-1} style={{display: "none"}}> --- </option>
                                                {productTypeList && productTypeList?.map((type)=>(
                                                    <option key={`product-type-dd-${type.id}`} value={type.id}>{type.name}</option>
                                                ))}
                                            </select>
                                        </>
                                    }
                                </div>
                            </>
                        }
                    </div>
                </Card>
                {selectedProductType && !loading &&
                    <Card>
                        <div className="product-section" data-cy="product-details-section">
                            <div className="product-add-headers" data-cy="product-details-header">
                                <h4 data-cy="product-details-title">
                                    Product Details
                                    {" "}
                                    <i className="far fa-list-alt"></i>
                                </h4>
                            </div>
                            {/* section to write in the basic details (name, description, etc) */}
                            <BasicProductDetails 
                                productStatuses={productStatuses} 
                                basicProduct={basicProductDetails}
                                setBasicProductDetails={setBasicProductDetails}
                                resetChildren={resetChildren}
                                editingFirstLoad={editingFirstLoad}
                                setJointAddOnCategories={setJointAddOnCategories}
                                setBasicProductReset={setBasicProductReset}
                                basicProductReset={basicProductReset}
                                setImageChanged={setImageChanged}
                            />
                        </div>
                
                        <>
                            <div className="product-section" data-cy="all-product-details-header">
                                <div className="product-add-headers">
                                    <h4 data-cy="variant-header-title">
                                        Variants
                                        {" "}
                                        <i className="far fa-layer-plus"></i>
                                    </h4>
                                </div>
                                {selectedProductType === 9 && editingProduct &&
                                    <div className="services-attached">
                                        <label>
                                            Services Attached to this Token:
                                        </label>
                                        <br />
                                        <p>
                                            {associatedServices.length > 0 &&
                                                <p className="small">
                                                    (click on any service to open that service in a new tab for viewing)
                                                </p>
                                            }
                                            <p className="associated-tokens">
                                                {associatedServices.length > 0 && associatedServices?.map((service, i)=>(
                                                        <>
                                                            <button className="cp fake-btn-outline" onClick={()=>window.open(`/p/services/${service.id}`, "_blank")}key={`product-services-${service.id}`}>
                                                                {service.name} 
                                                            </button>
                                                        </>
                                                    ))
                                                }
                                            </p>
                                            {associatedServices.length === 0 && 
                                                <span className="error-text">
                                                    No associated services!
                                                </span>
                                            }
                                        </p>
                                    </div>
                                }
                                <div data-cy="new-product-bundles">
                                    <Bundles 
                                        selectedProductType={selectedProductType}
                                        resetChildren={resetChildren}
                                        tokensForBundles={tokensForBundles}
                                        setTokensForBundles={setTokensForBundles} 
                                        setIncludeToken={setIncludeToken}
                                        editingFirstLoad={editingFirstLoad}
                                        associatedServices={associatedServices}
                                        setAssociatedServices={setAssociatedServices}
                                        orphanToken={orphanToken}
                                        setOrphanToken={setOrphanToken}
                                    />
                                </div>
                                <div data-cy="new-product-variants">
                                    {/* section to add variants */}
                                    {createdVariants?.map((variant)=>(
                                        <Variants 
                                            key={`variant-input-${variant.temp_id}`}
                                            propVariant={variant}
                                            selectedProductType={selectedProductType} 
                                            productStatuses={productStatuses}
                                            removeVariantHandler={removeVariantHandler}
                                            defaultShowMeasurements={defaultShowMeasurements}
                                            defaultShowSKUs={defaultShowSKUs}
                                            resetChildren={resetChildren}
                                            createdVariants={createdVariants}
                                            setCreatedVariants={setCreatedVariants}
                                            editingFirstLoad={editingFirstLoad}
                                            setJointAddOnCategories={setJointAddOnCategories}
                                            jointAddOnCategories={jointAddOnCategories}
                                            importedType={importedType}
                                        />
                                    ))}
                                </div>
                                {/* Anything but token */}
                                {selectedProductType && selectedProductType !==9 && 
                                    <div className="new-var-btn" data-cy="new-product-add-variant">
                                        <AddAVariant newVariantClickHandler={newVariantClickHandler} />
                                    </div>
                                }
                            </div>
                            {(localErrors.length > 0 || warnings.length > 0) &&
                                <>
                                    <div className="product-section">
                                        <div className="product-add-headers">
                                            <h4 data-cy="product-changes-header">
                                                Necessary Changes
                                                {" "}
                                                <i className="far fa-exclamation-circle" />
                                            </h4>
                                        </div>
                                        <div>
                                            <div className="local-errors" data-cy="new-product-local-errors">
                                                {localErrors.map((error, i)=>(
                                                    <React.Fragment key={`local-error-${i}`}>
                                                        {error}
                                                    </React.Fragment>
                                                ))}
                                            </div>
                                        </div>
                                        {warnings.length > 0 &&
                                            <fieldset className="warnings" data-cy="new-product-warnings">
                                                {warnings.map((warning,i)=>(
                                                    <span key={`product-new-warning-${i}`} data-cy="err-warning-map">
                                                        {warning}
                                                    </span>
                                                ))}
                                                <p data-cy="new-product-confirmation">
                                                    <label htmlFor='confirm-warning'>Please Confirm You've Read and Understand This Warning or have corrected it.</label>
                                                    <input 
                                                        name="confirm-warning"
                                                        type="checkbox"
                                                        value={confirmWarning}
                                                        checked={confirmWarning}
                                                        onChange={()=>setConfirmWarning(!confirmWarning)}
                                                    />
                                                </p>
                                            </fieldset>
                                        }
                                    </div>
                                </>
                            }
                            <div className="footer-btn-group" data-cy="new-product-buttons">
                                <div>
                                    <Button variant="danger" className="bordered-neutral-btn" onClick={handleCancel} data-cy="cancel-btn">Cancel</Button>
                                </div>
                                <div>
                                    {!importedType && !importedId && <Button disabled={warnings.length >0 && !confirmWarning ? true: false} onClick={(e)=>onSubmit(true, e)} data-cy="create-and-add-new-btn">{editingProduct ? "Edit Product and Add Another" : "Create and Add Another"}</Button>}
                                    <Button disabled={warnings.length >0 && !confirmWarning ? true: false} onClick={(e)=> onSubmit(false, e)} data-cy="create-and-exit-btn">{editingProduct ? "Edit and Exit" : "Create and Exit"}</Button>
                                </div>
                            </div>
                        </>
                    </Card>
                }
                {error && <ErrorCatcher error={error} data-cy="new-product-error-catcher" />}
                {success && <Toast data-cy="new-product-toast">{success}</Toast>}
            </div>
        :
            <div className="new-product-display-det-wrapper">
                
                <DisplayDetails associatedServices={associatedServices} product={editingProduct} importedBtn={<Button onClick={()=>{setEditMe(true)}} data-cy="imported-edit-btn">Edit Product</Button>}/>
                
            </div>
        }
    </>
  )
}
