import React, { useState, useEffect, useRef } from 'react'
import { Button } from 'react-bootstrap';

import { setSuccessToast, setErrorCatcher } from '../../../../utils/validation';
import Permissions from '../../../../api/Permissions';

const AddFolder = ({onClose, companyId, setSuccess, setError, activeItem=null}) => {
    
    const mountedRef = useRef(false);
    const [text, setText] = useState("");
    const [icon, setIcon] = useState("");
    const [disabled, setDisabled] = useState(false);
    const [localError, setLocalError] = useState("");

    useEffect(()=>{
        mountedRef.current = true;

        //unmount everything to prevent bleed over in reopening the modal
        return ()=>{
            mountedRef.current = false;
            setText("");
            setIcon("");
            setDisabled(false);
            setLocalError("");
        }
    },[])

    useEffect(()=>{
        if(activeItem && mountedRef.current){
            setText(activeItem.text);
            setIcon(activeItem.icon);
        }
    },[activeItem]);

    useEffect(()=>{
        let disabled = false;
        let error = "";
        if(!text){
            disabled = true;
        }else if(icon.length > 45 || text.length > 45){
            disabled = true;
            error = "You have exceeded the character limit for one or more fields"
        }else{
            disabled = false;
            error = "";
        }
        if(mountedRef.current){
            setDisabled(disabled);
            setLocalError(error);
        }
    },[text, icon]);

    const cleanUp=()=>{
        setText("");
        setIcon("");
        setDisabled(false);
        setLocalError("");
    }

    const handleClose=()=>{
        onClose(false);
        cleanUp();
    }

    const addFolder=async(e)=>{
        e.preventDefault();
        setSuccess();
        setError();

        let response; 
        let moduleItem = {
            name: `${text} - Folder`,
            module_type_id: 4,
            company_id: companyId ? companyId : null,
            default_menu_item:{
                icon: icon,
                text: text,
            }
        }
        if(activeItem) moduleItem.id = activeItem.module.id
        
        try{
            if(activeItem) response = await Permissions.Modules.update(moduleItem)
            else response = await Permissions.Modules.create(moduleItem)
            if(response.status ===200){
                cleanUp();
                setSuccess(setSuccessToast("Folder Added Successfully"))
                if(response.data) onClose(false, response.data.id)
                else onClose(true, null)
            }else if(response.errors){
                setError(setErrorCatcher(response.errors))
            }
        }catch(ex){console.error(ex)}
    }

    
    return (
        <div className="new-folder">
            <h4 className="section-title">
                Add New Folder
            </h4>
            <div className="input-sets">
                <p>
                    <label htmlFor="displayed-text">
                        Displayed Text: <span className="required-star">*</span>
                    </label>
                    <input 
                        required
                        id="displayed-text" 
                        value={text}
                        onChange={(e)=>setText(e.target.value)}
                    />
                </p>
                <p className={`character-count ${text.length > 45 ? "error-text" : ""}`}>
                    {text.length}/45
                </p>
                <p>
                    <label htmlFor="icon">
                        Icon:
                    </label>
                    <input
                        id="icon"
                        value={icon}
                        onChange={(e)=>setIcon(e.target.value)}
                    />
                </p>
                <p className={`character-count ${icon.length > 45 ? "error-text" : ""}`}>
                    {icon.length}/45
                </p>
                {localError &&
                    <p className="mb-4 error-text-ctr">
                        {localError}
                    </p>
                }
            </div>
            <p className="menu-settings-btn-row">
                <Button variant="danger" onClick={handleClose}>Cancel</Button>
                <Button onClick={addFolder} disabled={disabled}>{activeItem ? "Edit Folder" : "Add Folder"}</Button>
            </p>
        </div>
    )
}

export default AddFolder