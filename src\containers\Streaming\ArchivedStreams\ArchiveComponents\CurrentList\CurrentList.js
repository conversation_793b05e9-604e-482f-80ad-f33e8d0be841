import React, { useState } from 'react'
import { useEffect } from 'react';

import CartButton from '../../../../../components/common/CartButton';
import { filterStreamList, streamRecordings } from '../../ArchiveStreamsUtils'
import usePrevious from '../../../../../components/common/CustomHooks'
import ScheduleList from './ScheduleList';
import VideoPlayer from './VideoPlayer';

const CurrentList = (props) => {

    const { streamList, setError, selectedProductId, purchased } = props
    const selectedList = filterStreamList(streamList, selectedProductId);
    const viewableLinks = streamRecordings(selectedList[0]);
    const [selectedStream, setSelectedStream]=useState(null);
    const oldProductId = usePrevious(selectedProductId);

  return (
    <div className="current-list">
        <h5 className="sub-title">{selectedList[0]?.name}</h5>
        <h6>{selectedStream?.name}</h6>
        {!purchased &&
            <>
                {viewableLinks > 0 ? 
                    <CartButton setError={setError} productId={selectedProductId} />
                :
                    "No Recordings Available"
                }
            </>
        }
        <fieldset disabled={purchased ? false : true}>
            <VideoPlayer videoLink={selectedStream} purchased={purchased}/>
            {selectedList.length > 0 && 
                <ScheduleList selectedStreamList={selectedList} setSelectedStream={setSelectedStream} purchased={purchased} />
            }
        </fieldset>
    </div>
  )
}

export default CurrentList