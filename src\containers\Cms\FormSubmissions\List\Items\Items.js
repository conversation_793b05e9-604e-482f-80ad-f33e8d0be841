import React, {useEffect, useState} from 'react';
import { format } from 'date-fns';
import { Table } from 'react-bootstrap';

const Items = (props) => {
    const [keys, setKeys] = useState([]);
    const [fields, setFields] = useState([]);

    useEffect(() => {
        if (props.content){
            let _keys=["Date","IP Address"], _fields=[format(new Date(props.created_at), "M/d/yyyy h:mma"), props.ip_address];
            Object.keys(props.content).forEach(key => {
                if (key.toLowerCase() !== 'sb_form_options') {
                    _keys.push(key);
                    _fields.push(props.content[key]);
                }
            });
            setKeys(_keys);
            setFields(_fields);
        }
    }, [props.content, props.created_at, props.ip_address]);

    useEffect(() => {
        return () => {
            setKeys([]);
            setFields([]);
        }
    }, []);

    if (keys.length === 0 || fields.length === 0) return null;
    
	return (
        <tr>
            {keys.map((key, i) => (
                <td key={`formsubmmit-field-${i}`}>
                    {i>1 && 
                        <>
                            <span className="bold">{key}:</span><br/>
                        </>
                    }
                    {i === 0 &&
                        <>
                            {fields[i].split(' ').map((word, j) => (
                                <React.Fragment key={`word-${j}`}>
                                    {j === 0 && 
                                        <>
                                            <span className="bold">{word}</span>
                                            <br/>
                                        </>
                                    }
                                    {j > 0 && word.toLowerCase()}
                                </React.Fragment>
                            ))}
                        </>
                    }
                    {i > 0 && (Array.isArray(fields[i]) ? fields[i].join(', ') : fields[i])}
                </td>
            ))}
        </tr>
	);
}

export default Items;