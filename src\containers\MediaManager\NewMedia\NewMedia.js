import React, { useState, useCallback } from 'react';
import { Card, Container, Row, Col, Form, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import SubHeader from '../../../components/common/SubHeader';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import { useUploadProgress } from '../../../contexts/UploadProgressContext';
import CharacterCounterInput from '../../../components/common/CharacterCounter/CharacterCounterInput';
import DocMedia from '../MediaManagerComponents/MediaTypes/DocMedia';

import ImageMedia from '../MediaManagerComponents/MediaTypes/ImageMedia';
import AudioMedia from '../MediaManagerComponents/MediaTypes/AudioMedia';

import '../MediaManager.scss'
import APIUsers from '../../../api/Users';
import ProductVariantTypeahead from '../../../components/Typeahead/ProductVariantTypeahead';
import Products from '../../../api/Products';

const MEDIA_TYPES = [
    {id: 1, name: "Images"},
    {id: 4, name: "Videos"},
    {id: 5, name: "Documents"},
    {id: 7, name: "Audio"},
    {id: 8, name: "Other"}
]

//src\containers\MediaManager\MediaManager.js

export const NewMedia = ({isModal = false, onClose}) => {
    const {updateUploadProgress} = useUploadProgress();
  
    const [selectedMediaType, setSelectedMediaType] = useState(0);
    const [description, setDescription] = useState("");
    const [errors, setErrors] = useState();
    const [mediaData, setMediaData]=useState();
    const [productId, setProductId]=useState(null);

    const handleData=(data, save=false)=>{
        setMediaData(data);
        if(save) submitHandler();
    }

    const submitHandler = useCallback(async () => {
        let response;
        if(mediaData && productId && selectedMediaType===1){
            mediaData.append('product_id', productId?.id)
            mediaData.append('description', `Product image for product ${productId?.name}`)
            try {
                response = await Products.addImage(mediaData)
            }catch(ex){
                console.error(ex)
            }
        }
        else if(mediaData){
            //data.append('user_id', null);
            mediaData.append('type', selectedMediaType);
            mediaData.append('description', description);
            
            try{
                console.log([...mediaData])
                // response = await APIUsers.upload(mediaData, (progress) => updateUploadProgress(progress));
            } catch(ex){
                setErrors([ex.message]);
                updateUploadProgress(0);
            }
        }
        if (response.errors && response.status !== 200){
            setErrors(<ErrorCatcher error={response.errors} />)
            updateUploadProgress(0);
        } else {
            updateUploadProgress(0);
            onClose();
        }
    }, [selectedMediaType, description, onClose, updateUploadProgress, mediaData, productId]);

    return (
        <Container fluid>
            {!isModal &&
                <SubHeader items={[
                    { linkAs: Link, linkProps: { to: "/p/" }, text: "Home" },
                    { linkAs: Link, linkProps: { to: "/p/mediamanager" }, text: "Media Manager" },
                    { text: "New Media" }
                ]} />
            }

            <Card className={`${isModal ? "modal-card" : "content-card"}`}>
                {!isModal &&
                    <>
                        <h4 className="tm-1 section-title">New Media</h4>
                        <hr/>
                    </>
                }
                <Row>
                    <Col sm={12}>
                        <Form.Group controlId="type">
                            <Form.Label>Media Type</Form.Label>
                            <Form.Control as="select" custom onChange={(e)=>setSelectedMediaType(+e.target.value)} value={selectedMediaType}>
                                <option value={0}>Select Media Type</option>
                                {MEDIA_TYPES.map(mediaType => (
                                    <option key={`select-media-dd-${mediaType.id}`} value={mediaType.id}>{mediaType.name}</option>
                                ))}
                            </Form.Control>
                        </Form.Group>
                    </Col>
                    {selectedMediaType !== 0 &&
                        <Col sm={12}>
                            <CharacterCounterInput 
                                characterLimit={255}
                                limitedVariable={description}
                                label="Description"
                                required={true}
                                name="media-description"
                                placeholder="Media Description..."
                                onChange={(value)=>setDescription(value)}
                                columnRow='column'
                            />
                        </Col>
                    }
                    {errors}
                    {selectedMediaType === 1 &&
                        <Col sm={12}>
                            <label>
                                Associate with a Product?
                            </label>
                            <ProductVariantTypeahead 
                                passSelection={(selection)=>setProductId(selection[0])}
                                multiple={false}
                                async={true}
                            />
                            <br />
                            <ImageMedia 
                                isNew={true}
                                upload={handleData}
                            />
                        </Col>
                    }
                    {/* {selectedMediaType === 4 &&
                        <VideoMedia 
                            upload={uploadHandler}

                        />
                    } */}
                    {selectedMediaType === 5 &&
                        <Col sm={12}>
                            <DocMedia 
                                isNew={true}
                                upload={handleData}
                            />
                        </Col>
                    }
                    {selectedMediaType === 7 &&
                        <Col sm={12}>
                            <AudioMedia 
                                upload={handleData}
                            />
                        </Col>
                    }
                    {/* {selectedMediaType === 8 && 
                        <OtherMedia />
                    }*/}
                </Row>
                {selectedMediaType !== 0 &&
                    <Row className="mt-3">
                        <Button onClick={submitHandler} disabled={!mediaData}>
                            Save Media
                        </Button>
                    </Row>
                }
            </Card>
        </Container>
    );
}