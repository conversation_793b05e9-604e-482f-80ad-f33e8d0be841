import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, Container, <PERSON><PERSON><PERSON>rum<PERSON>, But<PERSON> } from 'react-bootstrap';
import { useLocation, Link } from 'react-router-dom';
import SubHeader from '../../../components/common/SubHeader';

import ViewEndpoints from './ViewEndpoints';
import CreateEndpoints from './CreateEndpoints';

import { getEndpoints } from '../PermissionsUtils/PermissionUtils';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';

import './Endpoints.scss';
import Tutorials from '../../../components/Tutorials';

export const Endpoints = () => {

    const mountedRef = useRef(false);
    const location = useLocation();
    const [ endpoints, setEndpoints ] =useState([]);
    const [ reset, setReset ]=useState(false);
    const [ error, setError ]=useState(null);
    const [ success, setSuccess ]=useState(null);
    const [ showCreate, setShowCreate ] = useState(false);
    const adminDash = JSON.parse(localStorage.getItem("adminDash"))
      
    const getEndpointsGet=useCallback(async()=>{
        let endpointResponse = await getEndpoints({includeModules: true});
        if(endpointResponse) setEndpoints(endpointResponse.data);
        else setError(<ErrorCatcher error={endpointResponse.errors} />)
    },[])

    useEffect(()=>{
        mountedRef.current = true

        if(mountedRef.current) getEndpointsGet();
        if(adminDash && location.search === "?new=true" && mountedRef.current) setShowCreate(true); 
        
        return ()=> mountedRef.current = false
    //putting adminDash in the dependency array causes an infinite loop
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[getEndpointsGet]);

    useEffect(()=>{
        if(reset && mountedRef.current) {
            getEndpointsGet();
            setReset(false);
            setError(null)
        }
    },[reset, getEndpointsGet]);

    const handleHide=(success)=>{
        setShowCreate(false);
        if(success) setSuccess(<Toast>Endpoint(s) Created Successfully</Toast>)
    }

    // create the breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" }
    ];

    // if adminDash is true, then we are in the admin dashboard
    if(adminDash) breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" });
    
    // if showCreate is true, then we are creating endpoints
    if(showCreate) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/endpoints" }, text: "View Endpoints" });
        breadcrumbs.push({ text: "Create Endpoints" });
    }   
    else breadcrumbs.push({ text: "Endpoints" });

    // create tutorials array
    const tutorials = [
        {
            tutorialSection: "Endpoints",
            allSectionTutorial:false,
            basicInfo: true,
            navigation: false
        }
    ]

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} tutorials={tutorials} />
            
            <Card className="content-card endpoint-wrapper">
                {error}
                {success}
                <h4 className="section-title space-between-row">
                    {showCreate ? 
                            "Create Endpoints" 
                    : 
                        <>
                            View Endpoints 
                            <Button onClick={()=>setShowCreate(true)}>
                                Create Endpoints
                            </Button>
                        </>
                    }
                </h4>
                {showCreate ? 
                    <CreateEndpoints 
                        allEndpoints={endpoints}
                        setHide={handleHide}
                        setSuccess={setSuccess}
                    />
                :
                    <>
                        {endpoints?.length > 0 &&
                            <ViewEndpoints 
                                initialEndpoints={endpoints} 
                                setError={setError} 
                                setSuccess={setSuccess}
                                setReset={setReset}
                            />
                        }
                    </>
                }
            </Card>
        </Container>
    )
}
