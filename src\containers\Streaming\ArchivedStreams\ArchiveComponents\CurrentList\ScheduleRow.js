import React from 'react'
import { gameHasRecordings } from '../../ArchiveStreamsUtils'
import { format } from 'date-fns'

const ScheduleRow = ({streamName, streamDate, setSelectedStream, stream, purchased, dataType="list", ...props}) => {

    const streamRecordings = gameHasRecordings(stream)

    const handleClick=()=>{
        if(purchased) setSelectedStream(stream);
    }

  return (
    <fieldset 
        disabled={streamRecordings ? false : true} 
        className={`schedule-row ${streamRecordings && purchased ? "cp" : ""}`}
        onClick={handleClick}
    >
        <p className="main-row">
            <span className="name">
                {streamName}
            </span>
            <span className={`status ${streamRecordings ? "stream-recording" : "stream-unavailable"}`}>
                {streamRecordings ? "Recording" : "Unavailable"}
            </span>
            <span className="date">
                {format(new Date(streamDate), "MM/dd/yyyy")}
            </span>
        </p>
        <div className="descriptor">
            {stream.descriptor && dataType === "list" &&
                <span>
                    {stream.descriptor}
                </span>
            }
            {dataType==="block" &&
                <>
                    <ul>
                        {stream?.games?.map((game)=>(
                            <li>
                                {game.name ? 
                                    <>
                                        - {game?.name} || {game?.descriptor} 
                                    </>
                                : 
                                    <>
                                        - {game.descriptor}
                                    </>
                                }
                            </li>
                        ))}
                    </ul>
                </>
            }
        </div>
    </fieldset>
  )
}

export default ScheduleRow