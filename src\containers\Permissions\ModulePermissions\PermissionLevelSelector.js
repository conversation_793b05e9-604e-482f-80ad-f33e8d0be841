import React, { useState, useEffect } from 'react';
import { Button } from 'react-bootstrap';

import Checkbox from '../../../components/common/Checkbox';

import '../Permissions.scss';

/*
 *  This component displays a list of checkboxes for each available permission level
 *  It will work as a controlled component, so the checked array is required to be passed in and updated
 */

// permissionLevels must be passed in
// availablePermissionLevels is an array of integers [1, 2] or null - if null, all permission levels are available
// checked is an array of integers [1, 2] or null - if null, no permission levels are checked
// onSelect is a callback function that will be called when a permission level is selected with the array of permission levels as the first argument
export const PermissionLevelSelector = ({ checkedPermissionLevels, allPermissionLevels, id, availablePermissionLevels=null, onSelect=()=>{}, onError=(e)=>{} }) => {

    console.log(availablePermissionLevels);

    const filteredPermissionLevels = () => {
        if (allPermissionLevels && availablePermissionLevels) {
            return allPermissionLevels.filter((permissionItem) => availablePermissionLevels.includes(permissionItem.id));
        } else if (allPermissionLevels) {
            return allPermissionLevels;
        }
        return null;
    }

    const handleChange = (checkedId, isChecked) => {
        let newCheckedPermissionLevels = [...checkedPermissionLevels];
        if (isChecked) { // if the checkbox is checked, add the permission level to the array
            newCheckedPermissionLevels.push(parseInt(checkedId));
        } else { // if the checkbox is unchecked, remove the permission level from the array
            newCheckedPermissionLevels = newCheckedPermissionLevels.filter((permissionLevel) => parseInt(permissionLevel) !== parseInt(checkedId));
        }
        console.log("onSelect", checkedId, newCheckedPermissionLevels);
        // send the result back up to the parent
        onSelect(id, newCheckedPermissionLevels);
    }

    const handleClearAll = (checkedId, isChecked) => {
        // clicking on the all button will clear all the permission levels - only if there were checked items before
        if (checkedPermissionLevels.length > 0) {
            onSelect(id, []);
        }
    }

    return (
        <>
            <div key={`${id}-permission-all`} className="permission-level-row">
                <Checkbox
                    id="all"
                    checked={checkedPermissionLevels && checkedPermissionLevels.length === 0}
                    onChange={handleClearAll}
                    label="All"
                />
            </div>
            {filteredPermissionLevels()?.map(permissionLevel => (
                <div key={`${id}-permission-${permissionLevel.id}`} className="permission-level-row">
                    <Checkbox
                        id={permissionLevel.id}
                        checked={checkedPermissionLevels && checkedPermissionLevels.includes(permissionLevel.id)}
                        onChange={handleChange}
                        label={permissionLevel.name}
                    />
                </div>
            ))}
        </>
    )
}