import React from 'react';
import { Button } from 'react-bootstrap';
import { format, isBefore, isAfter } from 'date-fns';

export const BlockView = ({streamEvent, currentTime, openModal}) => {
  return (
    <div className="block-streams">
        {streamEvent?.blocks?.map((block, i)=>(
            <div className="each-block" key={`stream-block-${i}`}>
                <div className="title-info">
                    <h5>{block.name}</h5>
                    <h6>{format(new Date(block?.start_datetime), "MM/dd/yyyy h:mm aaaa")} - {format(new Date(block?.end_datetime), "h:mm aaaa")}</h6>
                    <Button
                        disabled={!block.link || (isBefore(currentTime, new Date(block?.start_datetime)) || isAfter(currentTime, new Date(block?.end_datetime)))}
                        onClick={()=> openModal(block.name, block.link)}
                    >
                        {block?.link ?
                            <span>
                                Watch Now
                            </span>
                            :
                            <span>
                                Unavailable 
                            </span>
                        }
                    </Button>
                    <p className="small">
                        Schedule and teams subject to change.
                    </p>
                </div>
                <div className="game-info">
                    {/* <p>These are the following games scheduled for {block?.name}. </p> */}
                    {block.games?.map((game, j)=>(
                        <div key={`stream-game-${j}`} className="mb-1">
                            <span className="game-name">
                                {game.name}
                            </span>
                            <br />
                            <span className="game-descriptor">
                                {game.descriptor}
                            </span>
                        </div>
                    ))}
                </div>
            </div>
        ))}

    </div>
  )
}
