import React, { useState, useEffect, useRef, useCallback, Suspense } from 'react';
import { useHistory } from "react-router-dom";
import { useDispatch, useSelector } from 'react-redux';
import Container from 'react-bootstrap/Container';
import { Form } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { formatISO, addMinutes, subMinutes, isBefore, addMonths } from 'date-fns';

import * as actions from '../../../store/actions';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import MultiStep from '../../../components/common/MultiStep';
import Selection from './Selection';
import Timeslots from './Timeslots';
import Summary from './Summary';
import Confirmation from './Confirmation';
import ConfirmationTokens from './ConfirmationTokens';
import { selectTimeSlot, selectTokensForCurrentBooking } from '../../../store/selectors';
import { addToCart, reloadAvailableTokens, unsetServiceBooking } from '../../../utils/thunks';
import { saveLogging } from '../../../utils/thunks';

import Services from '../../../api/Services';
import { default as PosAPI } from '../../../api/Pos';
import { default as LocationsAPI } from '../../../api/Locations';
import { default as Products } from '../../../api/Products';

import './Booking.scss';

const dayNames = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
const default_cart_timeout_minutes = 15;
const PATRON_CART_CATEGORY_ID = 62;
const timeframes = [
    { minutes: 1, text: 'minutes' },
    { minutes: 60, text: 'hours' },
    { minutes: 1440, text: 'days' }
];

// max size of screen to display the smaller two-day grid; larger screen sizes show a full week grid
// this only gets checked when component is first loaded, will not adjust to screen resizing
const smallScreenMaxWidth = 740;

let defaultBooking = {
    // start_date_range: startOfISOWeek(new Date()),
    // end_date_range: endOfISOWeek(new Date()),
    selected_date: new Date(),
    service: null,
    selected_location: null,
    selected_slots: [],
    dates_to_query: [],
    min_grid_hour: 6,
    max_grid_hour: 22,
    conflict_events: null,
    small_screen: false,
    small_screen_num_days: 2
};

let defaultSearchParams = {
    itemsPerPage: 10,
    page: 1,
    sortColumn: "name",
    sortOrder: "ASC",
    search: "",
    start_date: new Date(),
    end_date: addMonths(new Date(), 12),
};

let defaultSearchResults = {
    services: [],
    totalItems: 0,
    pages: [],
};

// serviceId = id of the service that we want to jump straight to booking
const Booking = ({ userID, serviceId }) => {
    let history = useHistory();
    const dispatch = useDispatch();
    const serviceIdRef = useRef();

    const defaultPatronRegisterId = useSelector(state => state.pos.register)
    const currentBooking = useSelector(state => state.serviceBooking.current_booking);
    const errors = useSelector(state => state.serviceBooking.errors);
    const selectedTimeslot = useSelector(selectTimeSlot);
    const userTokens = useSelector(state => state.pos.available_tokens);
    const tokensForBooking = useSelector(selectTokensForCurrentBooking);
    
    const [loading,setLoading]=useState(true);
    const [pagePart, setPagePart] = useState();
    const [submitting, setSubmitting] = useState(false);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [validated, setValidated] = useState();

    // For navigating to a step and saving the step name

    const [goToName, setGoToName] = useState(null);
    const [stepName, setStepName] = useState("");
    const [firstLoad, setFirstLoad] = useState(true);

    const goTo = useCallback(name => {
        // if the button is clicked to go back to the Selection screen and there's a serviceId in the url
        // then we need to reset the URL to allow viewing all services
        if (name === "Selection" && serviceId) {
            history.push("/p/my/services/book");
        } else {
            setGoToName(name);
            setTimeout(() => { setGoToName(null); }, 1000);
        }
    }, [serviceId, history]);

    const saveStepName = (name) => {
        setStepName(name);
    }    

    const checkIfBookingExists = useCallback(()=> {
        if (currentBooking?.hasOwnProperty('event_id') && currentBooking.event_id!==null) {
            goTo("Confirmation");
            return true;
        }
        return false;
    }, [currentBooking, goTo]);

    const validate = useCallback((pageName, saveErrors=true) => {
        checkIfBookingExists();

        // do validation stuff on the form data for the current step
        // this is also called before submit, going through all the options
        let err = {};
        let savedata = {};

        switch(pageName) {
            case "Selection":
                if (!currentBooking.service) {
                    err = {...err, ...{selection: `Error: No service selected.`}};
                }
                break;
            case "Timeslots":
                if (currentBooking.selected_slots.length < currentBooking.service.min_timeslots) {
                    err = {...err, ...{timeslots: `Please select at least ${currentBooking.service.min_timeslots} timeslots.`}};
                }
                if (currentBooking.selected_slots?.length % currentBooking.service.timeslots_for_token === 0) {
                    err = {...err, ...{timeslots: `Number of selected blocks must be a multiple of ${currentBooking.service.timeslots_for_token}`}};
                }
                break;
            default:
                break;
        }
        if(Object.keys(err).length>0) { // fails validation
            dispatch(actions.setServiceBookingErrors(err));
            return false;
        }
        return true;
    }, [currentBooking, dispatch, checkIfBookingExists]);

    const showErrors = (keys) => {
        return (
            <div className="err">
                {keys.forEach(key => {
                    return errors[key] ? errors[key] : "";
                })}
            </div>
        );
    }

    // //   On page load
    useEffect(() => {
        let mounted = true;

        dispatch(actions.selectCustomer(JSON.parse(localStorage.getItem("user")).profile, defaultPatronRegisterId));

        // if a booking already exists in local storage, don't overwrite it with the defaultBooking
        let defaults = checkIfBookingExists() ? {} : defaultBooking;

        // check screen size
        const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
        dispatch(actions.setServiceBooking({...defaults, small_screen: screenWidth<=smallScreenMaxWidth }));
        dispatch(actions.setServiceBookingSearchParams(defaultSearchParams));
        dispatch(actions.setServiceBookingSearchResults(defaultSearchResults));

        let locationNames = {};
        // get all location names
        LocationsAPI.get()
        .then(response => {
            if(mounted && response.data) {
                response.data.forEach(location => {
                    locationNames[location.id] = location.name;
                });
                dispatch(actions.setServiceBooking({location_names: locationNames }));
            }
        }).catch(e => console.error(e));

        return () => {
            mounted = false;
        }
    // Adding 'checkIfBookingExists' to the dependency array causes an infinite loop - this is meant to run on load, not be triggered
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[dispatch]);

    useEffect(() => {
        let mounted = true;

        if (userID) {
            dispatch(reloadAvailableTokens(defaultPatronRegisterId));
        }

        return () => { mounted = false; }
    },[userID, dispatch, defaultPatronRegisterId]);

    useEffect(() => {
        // check to make sure end date is after start date
        // if (currentBooking.end_date && isBefore(new Date(currentBooking.end_date), new Date(currentBooking.start_date))) {
        //     dispatch(actions.setServiceBooking({end_date: currentBooking.start_date}));
        // }
    },[currentBooking, stepName]);

    useEffect(() => {
        // save to local storage
        localStorage.setItem('service-booking', JSON.stringify(currentBooking));
    },[currentBooking]);


    useEffect(() => {
        // TODO: only check this if this id is different from the previous
        if (currentBooking?.service?.id && currentBooking.service.id!==serviceIdRef.current) {
            serviceIdRef.current = currentBooking.service.id;
            Products.getFiltered({
                service_id: [ currentBooking.service.id ],
                categories: [ PATRON_CART_CATEGORY_ID ],   // exists in patron cart
                product_type_id: 6, // is a bundle
            }).then( response => {
                if (response && !response.errors && response.status===200 && response.data.products) {
                    dispatch(actions.setServiceBooking({ bundles_available_for_purchase: response.data.products }));
                }
            })
        }
    },[currentBooking, dispatch]);


    // These are only used for the confirmation page after token redemption when redux is emptied out
    const [currentBookingCopy, setCurrentBookingCopy] = useState(null);
    const [selectedTimeslotCopy, setSelectedTimeslotCopy] = useState(null);
    const [tokensForBookingCopy, setTokensForBookingCopy] = useState(null);
    const [deleteServiceBooking, setDeleteServiceBooking] = useState(false);

    // What happens when all of deleteBookingAndShowConfirmation is done
    useEffect(() => {
        if (deleteServiceBooking) {
            if (currentBookingCopy && selectedTimeslotCopy && tokensForBookingCopy) {
                dispatch(unsetServiceBooking());
                goTo("ConfirmationTokens");
            }
        }
    },[currentBookingCopy, selectedTimeslotCopy, tokensForBookingCopy, deleteServiceBooking, dispatch, goTo]);

    useEffect(() => {

        const getDataCopies = () => {
            return {
                currentBooking: currentBookingCopy,
                selectedTimeslot: selectedTimeslotCopy,
                tokensForBooking: tokensForBookingCopy
            }
        }

        const deleteBookingAndShowConfirmation = () => {
            setDeleteServiceBooking(true);
            setCurrentBookingCopy({...currentBooking});
            setSelectedTimeslotCopy({...selectedTimeslot});
            setTokensForBookingCopy({...tokensForBooking});
        }

        const onSubmit = async () => {

            if(isAllValidated() && (tokensForBooking.toBuy>0 || tokensForBooking.toRedeem>0)) {

                setSubmitting(true);
                // format object for API call
                let formattedObj = formatBooking();
                console.log(formattedObj)

                dispatch(saveLogging(`Booking Object (Booking 278) - formatted obj ${formattedObj}`))

                if (tokensForBooking.toBuy > 0 && parseFloat(currentBooking.service.default_price) > 0) {
                    // SEND TO CART

                    let response = await Services.createBooking(formattedObj).catch(e => console.error(e));

                    try {
                        if (response && !response.errors && response.status===200 && response.data.product) {
                            setValidated(false);

                            dispatch(actions.setServiceBooking({event_id: response.data.id }));

                            // calculate the book-by date
                            let book_by_datetime = subMinutes(new Date(selectedTimeslot.start), currentBooking.service.min_booking_notice);
                            let default_timeout_datetime = addMinutes(new Date(), default_cart_timeout_minutes);
                            let valid_until = default_timeout_datetime;
                            let cart_timeout = {
                                default_timeout_minutes: default_cart_timeout_minutes,
                                book_by_datetime: null,
                            }
                            // if book_by_time is before the valid_until time, do stuff
                            if (isBefore(book_by_datetime, default_timeout_datetime)) {
                                valid_until = book_by_datetime;
                                cart_timeout.book_by_datetime = book_by_datetime;
                            }
                            // the default_timeout_minutes and book_by_datetime are added to redux just so the proper message can be displayed on Confirmation
                            dispatch(actions.setServiceBooking(cart_timeout));

                            let products = [];
                            for (let i=0; i<tokensForBooking.toBuy; i++) {
                                let item = {...response.data.product,
                                    qty: 1,
                                    valid_until: formatISO(valid_until),
                                    event: {
                                        event_id: response.data.id,
                                        for_user_id: userID
                                    }
                                };
                                products.push(item);
                            }
                            dispatch(addToCart(products));

                            goTo("Confirmation");
                        } else { // api returned other kind of errors
                            setSubmitting(false);
                            setError(<ErrorCatcher error={response.errors || `${response.message} Status: ${response.status}`} />);
                        } 
                    }
                    catch(e) { //no response at all
                        setSubmitting(false);
                        setError(<ErrorCatcher error={e} />);
                    }

                } else {
                    // SEND TO REDEEM TOKENS ENDPOINT
                    let response = await PosAPI.payment.service_tokens(formattedObj).catch(e => console.error(e));
                    try {
                        if (response && !response.errors && response.status===200) {
                            setSubmitting(true);
                            deleteBookingAndShowConfirmation();
                        } else { // api returned other kind of errors
                            setSubmitting(false);
                            setError(<ErrorCatcher error={response.errors || `${response.message} Status: ${response.status}`} />);
                        } 
                    }
                    catch(e) { //no response at all
                        setSubmitting(false);
                        setError(<ErrorCatcher error={e} />);
                    }
                }
            }
        };

        // The steps are shown in the order listed
        const steps = [
            {name: 'Selection', component: <Selection serviceId={serviceId} goTo={goTo} />},
            {name: 'Timeslots', component: <Timeslots goTo={goTo} />},
            {name: 'Summary', component: <Summary goTo={goTo} onSubmit={onSubmit} submitting={submitting} />},
            {name: 'Confirmation', component: <Confirmation goTo={goTo} />},
            {name: 'ConfirmationTokens', component: <ConfirmationTokens goTo={goTo} getDataCopies={getDataCopies} />},
        ];

        const isAllValidated = (saveErrors=true) => {
            // check all form validation for current step list
            let validated = true;
            steps.forEach(name => {
                if (!validate(name, saveErrors)) validated = false;
            });
            return validated;
        }

        // format the data for sending to the API
        const formatBooking = () => {
            let formattedBooking = {
                service_id: currentBooking.service.id,
                location_id: currentBooking.selected_location,
                user_id: userID,
                start_datetime: formatISO(selectedTimeslot.start),
                end_datetime: formatISO(selectedTimeslot.end),
                token_quantity: currentBooking.selected_slots.length / currentBooking.service.timeslots_for_token
            };
            return formattedBooking;
        }
    
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                <MultiStep
                    showNavigation={false}
                    goTo={goToName}
                    steps={steps}
                    onSubmit={onSubmit}
                    onChangeStep={validate}
                    saveStep={saveStepName}
                    hideButtons={true}
                />
            </Suspense>
        );

    },[goToName, currentBooking, userID, selectedTimeslot, tokensForBooking, submitting, currentBookingCopy, selectedTimeslotCopy, tokensForBookingCopy, dispatch, validate, goTo, serviceId]);

    return (
        <Container fluid className={`wizard services-booking ${currentBooking.small_screen ? 'booking-small-screen' : 'booking-large-screen'}`}>
            {success}
            <Form>
                {pagePart}
            </Form>
            {error}
        </Container>
    );
}

export default Booking;