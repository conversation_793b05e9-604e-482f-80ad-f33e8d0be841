import React, { useState, useEffect, useRef } from 'react'
import { useSelector } from 'react-redux';

import usePrevious from '../../../../components/common/CustomHooks';

const positiveClass = "positive-row";
const negativeClass = "negative-row";

export const EachRow = ({feature, sendCheckUp, currentCheck, ...props}) => {

    const mountedRef = useRef(false);
    const permYes = <i className="far fa-check" />
    const featuresAvailable = useSelector(state => state.permissions?.features)
    const [ checked, setChecked ] = useState(null);
    const [ colorClassName, setColorClassName ] = useState(positiveClass);
    const [ firstLoad, setFirstLoad ]=useState(true)
    const oldChecked=usePrevious(checked);

    const setCheckedValue = (event) => {
        let value = null;
        if (event.target.value === "0" || event.target.value === 0) value = false;
        else if (event.target.value === "1" || event.target.value === 1) value = true;
        else if (event.target.value === "remove") value = null;
        setChecked(value);
    }

    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current = false
    },[])

    useEffect(()=>{
        if(oldChecked !== checked && !firstLoad) sendCheckUp(feature.id, checked)
    },[checked, oldChecked, feature.id, sendCheckUp, firstLoad])

    useEffect(()=>{
        if(feature && mountedRef.current && featuresAvailable[feature?.id]){
            let green;
            if (currentCheck===null) {
                if((feature.default_is_enabled || featuresAvailable[feature?.id]?.purchased)) green = true;
                else green = false;
            } else if (currentCheck === true) {
                green = true;
            } else if (currentCheck === false) {
                green = false;
            }

            if(green) setColorClassName(positiveClass)
            else setColorClassName(negativeClass)
            if(firstLoad) {
                setChecked(currentCheck);
                setFirstLoad(false);
            }
        }
    },[feature, currentCheck, firstLoad, featuresAvailable]);

    return (
        <tr 
            key={`each-feature-${feature.id}`}
            className={colorClassName}
        >
            <td>
                {feature.name}
            </td>
            <td className="check-td">
                {feature.default_is_enabled ? permYes : null}
            </td>
            <td className="check-td">
                {featuresAvailable[feature?.id].purchased ? permYes : null}
            </td>
            <td className="override-checkbox">
                <input 
                    type="radio"
                    id={`checkbox-${feature.id}-null`}
                    name={`checkbox-${feature.id}`}
                    value="remove"
                    onChange={setCheckedValue}
                    checked={checked===null}
                    className={`disabled`}
                />
                <label htmlFor={`checkbox-${feature.id}-null`}>
                    Remove Override
                </label>
                <input 
                    type="radio"
                    id={`checkbox-${feature.id}-0`}
                    name={`checkbox-${feature.id}`}
                    value={0}
                    onChange={setCheckedValue}
                    checked={checked===false}
                    className="markthis"
                />
                <label htmlFor={`checkbox-${feature.id}-0`}>
                    <span className="override-checkmark">{permYes}</span>
                    Deny Permission
                </label>
                <input 
                    type="radio"
                    id={`checkbox-${feature.id}-1`}
                    name={`checkbox-${feature.id}`}
                    value={1}
                    onChange={setCheckedValue}
                    checked={checked===true}
                    className="markthis"
                />
                <label htmlFor={`checkbox-${feature.id}-1`}>
                    <span className="override-checkmark">{permYes}</span>
                    Grant Permission
                </label>
            </td>
            {/* <td className="label">
                <span htmlFor={`checkbox-${feature.id}`}>
                {(feature.default_is_enabled && !checked) || (feature.purchased && !checked) 
                || (!feature.default_is_enabled && !feature.purchased && checked)
                    ? "Remove Permission" : ""
                }
                {((!feature.default_is_enabled && !feature.purchased && !checked) 
                    || (feature.default_is_enabled && checked)) 
                    ? "Grant Permission" : ""
                }
                </span>
            </td> */}
            <td className="y-n">
                {(positiveClass === colorClassName) ? "YES" : "NO"}
            </td>
        </tr>   
    )
}
