import React, { useState, useCallback, useEffect, createRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, <PERSON><PERSON>, Card } from 'react-bootstrap';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import SingleUpload from '../../../../components/Uploader';

import List from './List';

import * as actions from '../../../../store/actions';
import { selectCurrentEvent } from '../../../../store/selectors';
import {randomUUID} from '../../../../utils/cms';

import cardStyles from './List/ImageCard/ImageCard.module.scss';
import styles from './Image.module.scss';
const Image = (props) => {
    const dispatch = useDispatch();

    const currentEvent = useSelector(selectCurrentEvent);
    const errors = useSelector(state => state.eventwizard.errors);
    const [images, setImages] = useState(currentEvent?.images || []);
    const [preview, setPreview] = useState();

    // handle button outside dropzone
    const pictureRef = createRef();

    // updates the profile image after upload
    const uploadPicHandler = useCallback(formData =>{
        if (formData){
            formData.append('type', 1); // image
            formData.append('description', "");
            formData.append('id', randomUUID());

            let data = {};
            for (let [key, value] of formData.entries()) data[key] = value;
            if (data?.file) data['preview_url'] = URL.createObjectURL(data.file);
            setImages(prev=>[...prev, data]);
        }
    }, []);

    const updatePicHandler = useCallback(images => {
        setImages(images);
    }, []);

    useEffect(() => {
        dispatch(actions.setCurrentEventWizard({ images: images }));
    }, [dispatch, images]);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Event Images</span>
                <Row>
                    <Col>
                        <DndProvider backend={HTML5Backend}>
                            {currentEvent?.images?.length>0 &&
                                <List 
                                    images={currentEvent.images} 
                                    update={updatePicHandler}
                                    setPreview={setPreview}
                                />
                            }
                        </DndProvider>
                        <SingleUpload 
                            DzRef={pictureRef}
                            multiple
                            disableCrop
                            disablePreview
                            type="image/*"
                            size="1024"
                            previewHeight={0}
                            backgroundSize="contain"
                            previewSrc={null}
                            onSend={uploadPicHandler}>
                                <div className={`${cardStyles.card} ${cardStyles.droparea}`}>
                                    Drop a picture here or click...
                                </div>
                        </SingleUpload>
                    </Col>
                    {currentEvent?.images?.length>0 &&
                        <Col>
                            {preview &&
                                <div className={styles["preview-container"]}>
                                    <Button className={`btn rounded ${styles["btn-close"]}`} variant="outline-light" onClick={e=>setPreview(null)} ><i className="far fa-times m-0"/></Button>
                                    <img src={preview} className={styles["preview-img"]} alt="Preview" />
                                </div>
                            }
                        </Col>
                    }
                </Row>
                <Row>
                    <Col>
                    </Col>
                </Row>
                <Row>
                    <Col sm={12} className="wizard">
                        <div className={`err ${!!errors.images ? "" : "hidden"}`}>
                            {errors.images}
                        </div>
                    </Col>
                </Row>
            </Col>            
        </Row>
    );
}

export default Image;