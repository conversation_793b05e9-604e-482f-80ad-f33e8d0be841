import React, { useState, useEffect, useRef } from 'react';
import { Container, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import NewUserTypeahead from '../../../components/Typeahead/NewUserTypeahead';
import GroupTypeahead from '../../../components/Typeahead/GroupTypeahead';
import SubHeader from '../../../components/common/SubHeader';
import './OwnerPermissionDash.scss'

export const OwnerPermissionDash = () => {

    const mountedRef = useRef(false);
    const companyId = useSelector(state => state.auth.user.company_id);
    const [ selectedGroup, setSelectedGroup ] = useState([]);
    const [ selectedUser, setSelectedUser ] = useState([]);
    const adminDash = useRef(JSON.parse(localStorage.getItem('adminDash')));
    const ownerDash = useRef(JSON.parse(localStorage.getItem('ownerDash')));

    useEffect(()=>{
        mountedRef.current = true

        localStorage.setItem('ownerDash', JSON.stringify({
            selectedCompany: [{id: companyId}]
        }))

        window.addEventListener("beforeunload", ()=>{
            localStorage.removeItem("ownerDash");
        })

        return()=> {
            mountedRef.current = false
            localStorage.removeItem("ownerDash");
        }

    //eslint-disable-next-line
    },[])

    // create breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/" }, text: "Home" } 
    ];

    // if ownerDash.current is true, add Owner Settings Dashboard breadcrumbs
    if(ownerDash.current){
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/owner" }, text: "Settings Dashboard" })
    }

    // if adminDash.current is true, add Permission Dashboard breadcrumbs
    if(adminDash.current){
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" })
    }

    // add Owner Settings Dashboard breadcrumbs
    breadcrumbs.push({ text: "Owner Settings Dashboard", active: true })

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} />
            <Card className="content-card">
                <h4 className="section-title">
                    Owner Settings Dashboard
                </h4>
                <div className='owner-permission-dashboard-wrapper'>
                    <div className="long-row">
                        <h6>
                            <i className="far fa-key-skeleton" /> {" "} Permissions
                        </h6>
                        <div className="row">
                            <div className="col">
                                <Link to={`/p/module/assign/roles/${companyId}`} target="_blank">
                                    <button>
                                        Assign Modules to Roles
                                    </button>
                                </Link>
                            </div>
                            <p>
                                Modules are essentially add-ons and features that are broken down into smaller, manageable components.
                                A module can consist of a single page or a specific functionality within a page.
                                These modules are responsible for controlling the menu items and links that appear in the side bar. 
                                Here, modules can be assigned in a general fashion to each role, such as staff or patrons,
                                to ensure that they have access to the appropriate features and functionalities.
                            </p>
                        </div>
                        <div className="row">
                            <div className="col">
                                <fieldset disabled={!selectedGroup.length}>
                                    <Link to={`/p/module/assign/group/${selectedGroup[0]?.id}`} target="_blank">
                                        <button>
                                            Assign Modules to Groups
                                        </button>
                                    </Link>
                                </fieldset>
                                <GroupTypeahead passSelection={(selection)=>setSelectedGroup(selection)} />
                            </div>
                            <p>
                                Like assigning modules to roles, module permissions can be assigned to specific groups.      
                            </p>
                        </div>
                        <div className="row">
                            <div className="col">
                                <fieldset disabled={!selectedUser.length}>
                                    <Link to={`/p/module/assign/user/${selectedUser[0]?.id}`} target="_blank">
                                        <button>
                                            Assign Modules to Users
                                        </button>
                                    </Link>
                                </fieldset>
                                <NewUserTypeahead passSelection={(selection)=>setSelectedUser(selection)} />
                            </div>
                            <p>
                                There will always be exceptions to certain rules.  
                                Here, you can allow a user access to a module that may be above what their role allows.  
                            </p>
                        </div>
                    </div>
                    <div className="long-row">
                        <h6>
                            <i className="far fa-bars" /> {" "} Menu
                        </h6>
                        <div className="row">
                            <div className="col">
                                <Link to="/p/menu" target="_blank">
                                    <button>
                                        Edit Site Menu
                                    </button>
                                </Link>
                            </div>
                            <p>
                                Through the site menu, you can manage the sorting and nesting of your side menu navigation. 
                                You can also establish connections between modules and new menu items, defining their name 
                                and icon to ensure your users can easily identify them. 
                            </p>
                        </div>
                    </div>
                    <div className="long-row">
                        <h6>
                            <i className="far fa-wrench" /> {" "} Config
                        </h6>
                        <div className="row">
                            <div className="col">
                                <Link to ="/p/config/owner" target="_blank">
                                    <button>
                                        Edit Company Configs
                                    </button>
                                </Link>
                            </div>
                            <p>
                                Your configuration settings allow you to customize your email and text notifications, 
                                as well as other miscellaneous preferences.
                            </p>
                        </div>
                    </div>
                    {/* <div className="long-row">
                        <h6>
                            <i className="far fa-sparkles" /> {" "} Purchase Features
                        </h6>
                        <div className="row">
                            <div className="col">
                                <button>
                                    Feature Shop
                                </button>
                            </div>
                            <p>
                                If you're looking to enhance the functionality of your website, you can visit our feature shop to purchase additional addons and features.
                            </p>
                        </div>
                    </div> */}
                </div>
            </Card>
        </Container>
    )
}
