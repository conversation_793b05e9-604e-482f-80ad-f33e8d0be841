import React, {useState, useEffect, useCallback} from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Container from 'react-bootstrap/Container';
import Row from 'react-bootstrap/Row';
import Col from 'react-bootstrap/Col';
import Image from 'react-bootstrap/Image';
import Button from 'react-bootstrap/Button';
import { Card } from 'react-bootstrap';import {  differenceInMinutes, parseISO, formatISO} from 'date-fns';
import BookForOthers from '../Service/BookForOthers';


export const Gladius = () => (
    <Container fluid>
        <Row className="mb-4">
            <Col className="text-right">
                <Button variant="light" size="sm"><i className="far fa-arrow-to-bottom"></i>Export</Button>
                <Button variant="light" size="sm"><i className="far fa-share-alt"></i>Share</Button>
            </Col>
        </Row>
        <Row>
            <Col>
                <div className="card">
                    <h1>Scientia Et Gladius</h1>
                    <p>
                        I'm baby blog kogi prism bicycle rights subway tile helvetica selvage. Thundercats cray la croix, plaid seitan food truck chartreuse street art. Jean shorts brooklyn bicycle rights disrupt stumptown farm-to-table enamel pin gastropub unicorn prism. Artisan ennui cray photo booth. Pinterest fixie taxidermy lumbersexual ennui, vexillologist beard listicle. Blog ramps offal, twee jean shorts narwhal PBR&B neutra affogato XOXO synth normcore.                
                    </p>
                    <p>
                        Migas biodiesel pickled listicle wayfarers. Offal mixtape coloring book, church-key art party plaid lomo wolf shaman cred master cleanse taxidermy irony whatever hoodie. Marfa twee neutra taxidermy 90's sustainable, celiac vegan vape next level tumblr selvage. Yr leggings vegan, franzen kogi raw denim hot chicken shoreditch 8-bit gochujang selfies affogato biodiesel ennui. Venmo narwhal cloud bread sartorial craft beer. IPhone butcher hoodie tote bag poke fashion axe wayfarers adaptogen vice kickstarter activated charcoal coloring book sartorial thundercats succulents.
                    </p>
                </div>

            </Col>
        </Row>
    </Container>
);

export const WizPig = () => (
    <Container fluid>
        <Row>
            <Col className="text-left">
                <h1 className="pb-0">Test it!</h1>
                {/* <Image src={require("peep.png").default} fluid /> */}
            </Col>
        </Row>
    </Container>
);


const data=[
			{
				"Test":{
					"props":{
						"text1": "This is not a",
						"text2": "This is a"
					}
				}
			},
            {
                "div":{
                    "props":{
                        "className":"card"
                    },
                    "content":[
                        {
                            "span":{
                                "innerText":"This is a span"
                            }
                        },
                        {
                            "contentBlock":{
                                "id":2
                            }
                        }            
                    ]
                }
            }
		]


const Test = (props) => {

    return (
        <Card className="content-card">
            <BookForOthers />
        </Card>
    );
};

export default Test;