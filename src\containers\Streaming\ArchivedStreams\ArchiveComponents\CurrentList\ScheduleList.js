import React from 'react'
import ScheduleRow from './ScheduleRow'

/**
 * @param {{object}} selectedStreamList
 */
const ScheduleList = (props) => {

    const { selectedStreamList, setSelectedStream, purchased } = props

    return (
        <div className="schedule-list">
            {selectedStreamList && (!selectedStreamList[0].format || selectedStreamList[0].format === "list") &&
                <>
                    {selectedStreamList[0].days?.map((day, i)=>(
                        <div key={`stream-days-${day}-${i}`}>
                            {day?.schedule?.map((stream, j)=>(
                                <React.Fragment key={`stream-details-${day}-${j}`}>
                                    <ScheduleRow 
                                        dataType={"list"}
                                        streamName={stream.name} 
                                        streamDate={day.date} 
                                        setSelectedStream={setSelectedStream}
                                        stream={stream}
                                        purchased={purchased}
                                    />
                                </React.Fragment>
                            ))}
                        </div>
                    ))}
                </>
            }
            {selectedStreamList && selectedStreamList[0]?.format === "block" &&
                <>
                    {selectedStreamList[0].blocks.map((block, i)=>(
                        <React.Fragment key={`stream-block-${block}-${i}`}>
                            <ScheduleRow 
                                dataType={"block"}
                                streamName={block.name} 
                                streamDate={block.start_datetime} 
                                setSelectedStream={setSelectedStream}
                                stream={block}
                                purchased={purchased}
                            />
                        </React.Fragment>
                    ))}
                </>
            }
        </div>
    )
}

export default ScheduleList