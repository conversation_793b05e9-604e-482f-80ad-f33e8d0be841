import React, { useState, useEffect, useRef } from 'react';
import { Container, Card } from 'react-bootstrap';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import SubHeader from '../../../components/common/SubHeader';
import { CompanyTypeahead } from '../../../components/Typeahead/CompanyTypeahead';
import NewUserTypeahead from '../../../components/Typeahead/NewUserTypeahead';
import GroupTypeahead from '../../../components/Typeahead/GroupTypeahead';
import './AdminPermissions.scss'

export const AdminPermissions = () => {

    const mountedRef = useRef(false);
    //current is the one you're logged into, active is the one selected in a typeahead, needed for the menu as you can only edit what you're logged in as
    const currentCompany = useSelector(state=> state.company)
    const [activeCompany, setActiveCompany]=useState([]);
    const [selectedGroup, setSelectedGroup]=useState([]);
    const [selectedUser, setSelectedUser]=useState([]);

    useEffect(()=>{
        mountedRef.current = true

        localStorage.setItem("adminDash", JSON.stringify({
            selectedCompany: [],
            selectedGroup: [],
            selectedUser: [],
        }));
        
        window.addEventListener("beforeunload", (event)=>{ //removes the storage when the tab is closed (even removes it from different tabs)
            localStorage.removeItem("adminDash");
        })
        
        return()=> {
            mountedRef.current = false
            localStorage.removeItem("adminDash") //removes the storage when navigating away from the page
        }
    },[]);

    useEffect(()=>{
        localStorage.setItem("adminDash", JSON.stringify({
            selectedCompany: currentCompany,
            selectedGroup: selectedGroup,
            selectedUser: selectedUser,
        }));
    },[activeCompany, selectedGroup, selectedUser, currentCompany]);

    return (
        <Container fluid>
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Permission Dashboard" }
            ]} />
            <Card className="content-card">
                <h4 className="section-title">
                    Siteboss Admin Permission Dashboard
                </h4>
                <div className='admin-permission-dash-wrapper'>
                    <div className="typeahead-select">
                        <h5>
                            {activeCompany?.length > 0 ?
                                `Currently Selected Company : ${activeCompany[0].name}`
                                :
                                'Select a Company'
                            }
                        </h5>
                        <p>
                            Please note, users and groups can only be selected for the company you are currently LOGGED in to.  
                            You still need to select a company first because some aspects of permissions also require it.  
                        </p>
                        <CompanyTypeahead 
                            passSelection={(selection)=>setActiveCompany(selection)}
                        />
                        {activeCompany?.length > 0 &&
                            <>
                                <GroupTypeahead 
                                    multiple={false}
                                    passSelection={(selection)=>setSelectedGroup(selection)}
                                />
                                <NewUserTypeahead 
                                    async={true}
                                    placeholder={"Enter a user name"}
                                    multiple={false}
                                    passSelection={(selection)=>setSelectedUser(selection)}
                                />
                            </>
                        }
                    </div>
                    <div className="large-col bordered padding">
                        <div>
                            <h5>
                                <i className="far fa-layer-plus" /> Features
                            </h5>
                            <div className="perm-row">
                                <div>
                                    <Link to={{
                                            pathname: "/p/features/dashboard"
                                        }}  
                                        target="_blank"    
                                    >
                                        <button>
                                            Features Dashboard
                                        </button>
                                    </Link>
                                    <div className="quick-links outline-btns">
                                        <p>
                                            Quick Links:
                                        </p>
                                        <p>
                                            <Link to={{
                                                    pathname: "/p/features/new",
                                                }}
                                                target="_blank"        
                                            >
                                                <button>
                                                    Create Features
                                                </button>
                                            </Link>
                                            <Link to={{
                                                    pathname: "/p/features/sort"
                                                }}
                                                target="_blank"
                                            >
                                                <button>
                                                    Sort Features
                                                </button>
                                            </Link>
                                        </p>
                                    </div>
                                </div>
                                <div className="mx-3">
                                    <Link to={{
                                            pathname: "/p/features/assign/defaults"
                                        }}
                                        target="_blank"    
                                    >
                                        <button className="long-btns">
                                            Assign Default Feature Permissions
                                        </button>
                                    </Link>
                                    <fieldset disabled={activeCompany.length === 0} >
                                        <p className="secondary-btns">
                                            <Link to={{
                                                    pathname: "/p/features/assign/company"
                                                }}
                                                target="_blank"    
                                            > 
                                                <button>
                                                    {activeCompany.length === 0 ?
                                                        "Select a Company"
                                                        :
                                                        `View & Edit Permissions for ${activeCompany[0].name}`
                                                    }
                                                </button>
                                            </Link>
                                        </p>
                                    </fieldset>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="large-col bordered padding">
                        <div>
                            <h5>
                                <i className="far fa-puzzle-piece" /> Modules
                            </h5>
                            <div className="perm-row">
                                <div>
                                    <Link to={{
                                            pathname: "/p/module/dashboard"
                                        }}
                                        target="_blank"
                                    >
                                        <button>
                                            Modules Dashboard
                                        </button>
                                    </Link>
                                    <div className="quick-links outline-btns">
                                        <p>
                                            Quick Links:
                                        </p>
                                        <p>
                                            <Link to={{
                                                    pathname:"/p/module/new"
                                                }}
                                                target="_blank"        
                                            >
                                                <button>
                                                    Create Module
                                                </button>
                                            </Link>
                                            <Link to={{
                                                    pathname: "/p/module/sort"
                                                }}
                                                target="_blank"
                                            >
                                                <button>
                                                    Sort Modules
                                                </button>
                                            </Link>
                                        </p>
                                    </div>
                                </div>
                                <div className="mx-3">
                                    <Link to={{
                                            pathname: "/p/module/assign/default"
                                        }}
                                        target="_blank"
                                    >
                                        <button className="long-btns">
                                            Assign Default Module Permissions
                                        </button>
                                    </Link>
                                    <div className="secondary-btns">
                                        <fieldset disabled={activeCompany.length === 0}>
                                            <Link to={{
                                                pathname: `/p/module/assign/roles/${activeCompany[0]?.id}`
                                            }}
                                            target="_blank"
                                            >
                                                <button>
                                                    {activeCompany.length === 0 ?
                                                        "Select a Company First"
                                                        :
                                                        `Assign Modules to Roles for ${activeCompany[0]?.name}`
                                                    }
                                                </button>
                                            </Link>
                                        </fieldset>
                                        <fieldset disabled={selectedGroup.length === 0}>
                                            <Link to={{
                                                    pathname: `/p/module/assign/group/${selectedGroup[0]?.id}`
                                                }}
                                                target="_blank"
                                            >
                                                <button>
                                                    {activeCompany.length === 0 ? 
                                                        "Select a Company First" 
                                                    :    
                                                        <>
                                                            {selectedGroup.length === 0 ?
                                                                "Select a Group"
                                                                :
                                                                `Assign Modules to Groups for ${selectedGroup[0]?.name}`
                                                            }
                                                        </>
                                                    }
                                                </button>
                                            </Link>
                                        </fieldset>
                                        <fieldset disabled ={selectedUser.length === 0}>
                                            <Link to={{
                                                    pathname: `/p/module/assign/user/${selectedUser[0]?.id}`
                                                }}
                                                target="_blank"
                                            >
                                                <button>
                                                    {activeCompany.length === 0 ? 
                                                        "Select a Company First"
                                                    :
                                                        <>
                                                            {selectedUser.length === 0 ?
                                                                "Select a User"
                                                            :
                                                                `Assign Modules to Users for ${selectedUser[0]?.first_name} ${selectedUser[0]?.last_name}`
                                                            }
                                                        </>
                                                    }
                                                </button>
                                            </Link>   
                                        </fieldset>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="large-col">
                        <div className="small-col bordered">
                            <h5>
                                <i className="far fa-network-wired" /> Endpoints
                            </h5>
                            <Link to={{
                                    pathname: "/p/endpoints"
                                }}
                                target="_blank"    
                            >
                                <button>
                                    Endpoints Dashboard
                                </button>
                            </Link>
                            <div className="quick-links outline-btns">
                                <p>
                                    Quick Link:
                                </p>
                                <p>
                                    <Link to={{
                                            pathname: "/p/endpoints",
                                            search:"?new=true"
                                        }}
                                        target="_blank"
                                    >
                                        <button>
                                            Create Endpoints
                                        </button>
                                    </Link>
                                </p>
                            </div>
                        </div>
                        <div className="small-col bordered">
                            <h5>
                                <i className="far fa-bars" /> Menu
                            </h5>
                            <Link to={{
                                    pathname: "/p/menu/default"
                                }}
                                target="_blank"
                            >
                                <button className="long-btns">
                                    Edit the Default Menu
                                </button>
                            </Link>
                            <fieldset >
                                <p className="secondary-btns">
                                    <Link to={{
                                            pathname: "/p/menu"
                                        }}
                                        target="_blank"
                                    >
                                        <button>
                                            Edit Company Menu
                                        </button>
                                    </Link>
                                </p>
                            </fieldset>
                            <span>
                                **Note** You can only edit the menu for the company you're currently logged in with
                            </span>
                        </div>
                    </div>
                    <div className="large-col">
                        <div className="small-col bordered">
                            <h5>
                               <i className="far fa-store-alt" /> Companies
                            </h5>
                            <Link to={{
                                    pathname: "/p/companies/dashboard"
                                }}
                                target="_blank"
                            >
                                <button>
                                    Companies Dashboard
                                </button>
                            </Link>
                            <div className="quick-links outline-btns">
                                <p>
                                    Quick Links: 
                                </p>
                                <p>
                                    <Link to={{
                                            pathname:"/p/companies/new"
                                        }}
                                        target="_blank"
                                    >
                                        <button>
                                            Create Company
                                        </button>
                                    </Link>
                                </p>
                            </div>
                        </div>
                        <div className="small-col bordered">
                            <h5>
                                Config
                            </h5>
                            <Link to={{
                                pathname: "/p/config/admin"
                            }}
                                target="_blank"
                            >
                                <button>
                                    Edit Default Company Config
                                </button>
                            </Link>
                            <fieldset disabled={activeCompany.length === 0}>
                                <p className="secondary-btns">
                                    <Link to={{
                                        pathname: "/p/config/owner",
                                        search: `?company=${activeCompany[0]?.id}`
                                    }}
                                        target="_blank"
                                    >
                                        <button>
                                            {activeCompany.length === 0 ?
                                                "Select a Company"
                                                :
                                                `Edit Config for ${activeCompany[0].name}`
                                            }
                                        </button>
                                    </Link>
                                </p>
                            </fieldset>
                        </div>
                    </div>
                </div>
            </Card>
        </Container>
    )
}
