import { getEndpoints } from "../PermissionsUtils/PermissionUtils";

/**Checks the slug for a starting slash and adds one if needed.  Removes trailing slash if present */
export const checkForSlash=(slug)=>{
    if(slug.charAt(slug.length -1) === "/") slug = slug.slice(0, -1);
    if(slug[0]!=="/") slug = `/${slug}`
    return slug;
}

/**Queries the DB for any slug that matches the one entered, if there's an exact match, returns that match */
export const checkForUniqueness=async(slug, tempId)=>{
    let response = await getEndpointList(slug);
    if(!response.errors){
        for(let i = 0; i < response?.length; i++){
            if(response[i]?.slug===slug) return {new: {slug: slug, tempId: tempId}, existing: response[i]};
        }
    }
}

/** Does the simple check to see if endpoint has both a method and slug */
export const checkEndpoints=(endpoints)=>{
    let good = true;
    endpoints.forEach((endpoint)=>{
        if(!endpoint.slug || !endpoint.method) good=false;
    })
    return good;
}

const getEndpointList = async(slug)=>{
    let response = await getEndpoints({slug: slug, includeModules: true});
    if(response.data) return response.data
    else if(response.errors) return response.errors
}