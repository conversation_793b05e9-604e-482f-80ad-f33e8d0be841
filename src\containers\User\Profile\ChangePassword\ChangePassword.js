import React, { useState, useEffect } from 'react';
import { useHistory } from "react-router-dom";
import Container from 'react-bootstrap/Container';
import Col from 'react-bootstrap/Col';
import Form from 'react-bootstrap/Form';
import Button from 'react-bootstrap/Button';
import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import Toast from '../../../../components/Toast';
import { authCheck } from "../../../../utils/auth";

import Users from '../../../../api/Users';
import { indepthPasswordValidationError, passwordStatement, validatePasswordPattern } from '../../../../utils/validation';

export const ChangePassword = (props) => {
    let history = useHistory();

    let user;
    if (props.user) user=props.user;
    else {
        user = authCheck(history);
    }

    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

    const checkValidation = (passOne, passTwo)=>{
        let error = "";
        if(passOne && passTwo && passOne !== passTwo) error = "Your passwords don't match!";
        else if(!validatePasswordPattern(passOne)) error = indepthPasswordValidationError(passOne);
        return error
    }

    // form submission
    const submitHandler = async (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        let formDataObj;
        const form = e.currentTarget;
        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            formData.append("user_id", props.user_id);
            formDataObj = Object.fromEntries(formData.entries());
            if(formDataObj.password && formDataObj.password_confirmation) setValidated(true);
        }

        let error = checkValidation(formDataObj.password, formDataObj.password_confirmation);
        if(error){
            setError(<ErrorCatcher error={error} />);
            setSubmitting(false);
            return;
        }else{
            await Users.update(formDataObj)
            .then( response => {    
                if (!response.errors) {
                    setSubmitting(false);
                    setValidated(false);
                    setSuccess(<Toast>Password saved successfully!</Toast>);
                    history.push(props.referer || "/p/profile"); // pushes to profile again to avoid resubmission
                } else {
                    setSubmitting(false);
                    setError(<ErrorCatcher error={response.errors} onClose={()=>setError(null)}/>);
                } 
            }).catch( e => {
                setSubmitting(false);
                setError(<ErrorCatcher error={e} onClose={()=>setError(null)} />);
            });
            
        }
    };


    return (
        <Container fluid>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Form.Row>
                    <Col sm="12">
                        <Form.Group controlId="password">
                            <Form.Label>New Password</Form.Label>
                            <Form.Control type="password" name="password"/>
                            <Form.Text>{passwordStatement}</Form.Text>
                        </Form.Group>
                    </Col>
                    <Col sm="12">
                        <Form.Group controlId="password_confirmation">
                            <Form.Label>Confirm Password</Form.Label>
                            <Form.Control type="password" name="password_confirmation"  />
                        </Form.Group>
                    </Col>
                </Form.Row>
                <Form.Row>
                    <Col sm="12" lg="4" className="my-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={`${submitting?" submitting":""}`}>Change Password</Button>
                    </Col>
                </Form.Row>
            </Form>
            {error}
        </Container>
    );
}