/*eslint-disable*/

/// <reference types="cypress" />

import Preview from './index.js';
import './Preview.scss';
import store from '../../../redux-store';

describe('It will mount the Preview component and check data rendering', {scrollBehavior: "center"}, ()=>{
    let orderBasic;
    let orderAddons;
    let orderVariants;
    let orderGiftCard;
    let orderSubscription;
    let orderUserDiscount;

    const dispatches =(order)=>{
        store.dispatch({
            type: 'POS_ORDER_ALL',
            order: order,
            register_id: 1
        });
        store.dispatch({
            type: 'POS_ORDER',
            order: order.id,
            register_id: 1
        });
        store.dispatch({
            type: 'POS_ADD_ITEM',
            items: order.items[0],
            register_id: 1
        })
    };

    const totalChecks=(subTotalValue, taxValue, totalValue)=>{
        cy.get('[data-cy="order-subtotal"]')
            .should('exist')
            .invoke('text')
            .should('include', subTotalValue)
        
        cy.get('[data-cy="order-tax"]')
            .should('exist')
            .invoke('text')
            .should('include', taxValue)

        cy.get('[data-cy="order-total"]')
            .should('exist')
            .invoke('text')
            .should('include', totalValue)
    }

    const basicChecks =(productName, productPrice)=>{
        cy.get('[data-cy="product-name"]')
            .should('exist')
            .invoke('text')
            .should('include', productName)
        cy.get('[data-cy="preview-item"] > .item-price')
            .should('exist')
            .invoke('text')
            .should('include', productPrice)
        cy.get('.product-buttons')
            .should('exist')
    }
    
    before(()=>{
        cy.fixture('/Order/order.json').then((data)=>{
            orderBasic = data.orderBasic.data[0];
            orderAddons = data.orderAddons.data[0];
            orderVariants = data.orderVariant.data[0];
            orderGiftCard = data.orderGiftCard.data[0];
            orderSubscription = data.orderSubscription.data[0];
            orderUserDiscount = data.orderUserDiscount.data[0];
        });
        store.dispatch({
            type: 'SET_COMPANY_CONFIG',
            config: {
                guest_user_id: 7
            }
        });
    })
    
    beforeEach(()=>{
        cy.viewport(500, 1000);
    });

    it('will mount and check and order a simple item', (()=>{
        cy.wait(1000)
        cy.log(orderBasic)
        dispatches(orderBasic)
        cy.intercept('PUT', "api/order/update", {status: 200}).as('orderUpdate');
        cy.mount(<Preview register_id={1} />);

        basicChecks('36 Tenders', '324.00')
        totalChecks(324, 0, 336.96);
        cy.get('[data-cy="original-price"]')
            .should('not.exist')
        cy.get('.small')
            .should('not.be.visible')
        cy.get('*[class^="addon-dot"]')
            .should('not.exist')
    }));

    it('will mount and check an order with addons',(()=>{
        cy.log(orderAddons)
        store.dispatch({
            type: 'POS_RESET_ITEMS',
            register_id: 1
        });
        dispatches(orderAddons)
        cy.intercept('PUT', "api/order/update", {status: 200}).as('orderUpdate');
        cy.mount(<Preview register_id={1} />);
        
        basicChecks('Add Nutella', '0.00')
        totalChecks(.93, .07, 1.04);
        cy.get('[data-cy="original-price"]')
            .should('not.exist')
        cy.get('.small')
            .should('not.be.visible')
        cy.get('*[class^="addon-dot"]') //make sure the addons are denoted
            .children()
            .its('length')
            .should('eq', 4) 
        cy.get('.product-buttons > [data-cy="delete-prod-btn"]') //make sure there are enough delete buttons for addons
            .children()
            .its('length')
            .should('eq', 5)
        cy.get('.product-buttons > [data-cy="copy-prod-btn"]') //There should only be one copy button, for the parent product
            .children()
            .its('length')
            .should('eq', 1)
        cy.get('.item-name')
            .contains('Avocado')
            .parent()
            .parent()
            .parent() //tripple parent to go back up to the row
            .within(()=>{
                cy.get('.item-price')
                    .invoke('text')
                    .should('include', '$0.93')
            })

    }));

    it('will mount and check a variant order', (()=>{
        cy.log(orderVariants);
        store.dispatch({
            type: 'POS_RESET_ITEMS',
            register_id: 1
        });
        dispatches(orderVariants)
        cy.intercept('PUT', "api/order/update", {status: 200}).as('orderUpdate');
        cy.mount(<Preview register_id={1} />);
        
        basicChecks('Coffee Drinks', '3.74');
        totalChecks(3.74, .26, 4.16);
        cy.get('[data-cy="original-price"]')
            .should('not.exist')
        cy.get('.small')
            .should('not.be.visible');
        cy.get('*[class^="addon-dot"]')
            .should('not.exist')
    }));

    it('will mount and check a gift card order', (()=>{
        cy.log(orderGiftCard);
        store.dispatch({
            type: 'POS_RESET_ITEMS',
            register_id: 1
        });
        dispatches(orderGiftCard);
        cy.intercept('PUT', "api/order/update", {status: 200}).as('orderUpdate');
        cy.mount(<Preview register_id={1} />);
        
        basicChecks('Gift Card', '10.00')
        totalChecks(10, .7, 11.13);
        cy.get('[data-cy="variant-name"]')
            .should('be.visible')
            .invoke('text')
            .should('include', '$10 Value')

        cy.get('[data-cy="original-price"]')
            .should('not.exist')
        cy.get('.small')
            .should('be.visible')
            .invoke('text')
            .should('include', 'Deliver on')
        cy.get('*[class^="addon-dot"]')
            .should('not.exist')
    }));

    it('will mount and check a subscription order', (()=>{
        cy.log(orderSubscription);
        store.dispatch({
            type: 'POS_RESET_ITEMS',
            register_id: 1
        });
        dispatches(orderSubscription);
        cy.intercept('PUT', "api/order/update", {status: 200}).as('orderUpdate');
        cy.mount(<Preview register_id={1} />);
        
        basicChecks('Active Senior Membership', '35.00')
        totalChecks(35, 0, 36.4)
        cy.get('[data-cy="original-price"]')
            .should('not.exist');
        cy.get('.small')
            .should('be.visible')
            .invoke('text')
            .should('include', 'Billed every 1 month')
        cy.get('*[class^="addon-dot"]')
            .should('not.exist')
    }));

    it('will mount and check a user discount',(()=>{
        cy.log(orderUserDiscount);
        store.dispatch({
            type: 'POS_COUPONS_SELECTED', 
            register_id: 1,
            coupons_selected: [7]
        });
        store.dispatch({
            type: 'POS_RESET_ITEMS',
            register_id: 1
        });
        dispatches(orderUserDiscount);
        cy.intercept('PUT', "api/order/update", {status: 200}).as('orderUpdate');
        cy.mount(<Preview register_id={1} />);
        
        basicChecks('Beef Jerky', '3.18');
        totalChecks(3.18, .22, 3.40);

        cy.get('[data-cy="original-price"]')
            .should('exist')
            .invoke('text')
            .should('include', '3.74');
         cy.get('*[class^="addon-dot"]')
            .should('not.exist')
    }));

})