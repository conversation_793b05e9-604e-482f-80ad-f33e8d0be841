@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/themes.scss';


.range-display-grid {
    display: grid;
    overflow-x: auto;
    grid-template-columns: repeat(7, minmax(75px, 1fr));
    grid-template-rows: minmax(10px, auto);
    /* grid-auto-rows: minmax(10px, auto); */
    column-gap: 10px;
    row-gap: 4px;
    margin-top: 0.5rem;
}
.range-display-grid div {
    width: 100%;
    text-align: center;
}
.range-display-grid .header-date {
    color:rgb(158, 158, 158);
}
.range-display-grid .header-day-of-week {
    display: block;
    font-size: 1.0rem;
    color: $primary-color;
    margin-top: 0.5rem;
}
.range-display-grid .header-month-name {
    display: block;
    font-size: 0.7rem;
    text-transform: uppercase;
}
.range-display-grid .header-date-num {
    display: block;
    font-size: 1.6rem;
    line-height: 1.8rem;
    margin-bottom: 0.5rem;
}

.wizard .range-display-grid .form-check.form-radio.grid {
    width: 100%;
    height: 100%;
    margin: 0;
}
.wizard .range-display-grid .form-check.form-radio.grid label {
    border: 1px solid rgb(158, 158, 158); 
    background-color: rgb(236, 236, 236);
    border-radius: 0.5rem;
    width: 100%;
}

.range-display-grid .cell-block {
    z-index: 20;
}

.range-display-grid .cell-gridlines {
    border-top: 1px solid rgb(214, 214, 214);
    z-index: 5;
}
.range-display-grid .cell-hourname {
    color: rgb(165, 165, 165);
    font-size: 0.7rem;
    text-align: left;
}
.range-display-grid .cell-past-day {
    z-index: 25;
    background-color: rgba(165, 165, 165, 0.4);
}
.services-booking .none-available {
    width: 100%;
    text-align: center;
    margin: 1rem 0;
    font-size: 1.1rem;
}
