import React from 'react';
import { useSelector } from 'react-redux';
import { Form, ToggleButtonGroup, ToggleButton } from 'react-bootstrap';
import { CompanyTypeahead } from '../../../components/Typeahead/CompanyTypeahead'
import MediaTypesTypeahead from '../../../components/Typeahead/MediaTypesTypeahead'

const MEDIA_TYPES = [
    {id: 1, name: "Images"},
    {id: 4, name: "Videos"},
    {id: 5, name: "Documents"},
    {id: 7, name: "Audio"},
    {id: 8, name: "Other"}
]

const Filter = ({setSelectCompany, setSelectMediaTypes, selectedMediaTypes, search, setSearch}) => {
    const user = useSelector(state => state.auth.user);
        
    const passCompanySelection = selection => setSelectCompany(selection.map(selection=>selection.id));
    const passMediaTypeSelection = selection => setSelectMediaTypes(selection.map(selection=>selection.id));

    return (
        <div>
            <ToggleButtonGroup 
                aria-label="Media types" 
                type="radio" 
                size="sm" 
                name="media-type" 
                value={selectedMediaTypes[0]} 
                style={{marginLeft:"auto"}} 
                className="flex-wrap mb-3" 
                onChange={(value) => setSelectMediaTypes([value])}
            >
                {MEDIA_TYPES.map(type => (
                    <ToggleButton
                        key={`media-manager-media-type-filter-${type.id}`}
                        variant="light"
                        value={type.id}
                        className="mb-1 me-1"
                    >
                        {type.name}
                    </ToggleButton>
                ))}
            </ToggleButtonGroup>

            {user.roles.some(a => +a.id === 1 || +a.id === 2) &&            
                <CompanyTypeahead 
                    passSelection={(selection)=>passCompanySelection(selection)}
                    multiple={false}
                    async={false}
                    paginated={false}
                    placeholder={"Search for a company..."}
                />
            }
            {/*
            <MediaTypesTypeahead 
                passSelection={(selection)=>passMediaTypeSelection(selection)}
                multiple={false}
                async={false}
                paginated={false}
                placeholder={"Search for a media type..."}
            />
            */}

            <Form.Group controlId="name">
                <Form.Control 
                    type="text" 
                    name="search" 
                    aria-label="Media Search Input"
                    placeholder="Enter a search term..."
                    value={search}
                    onChange={(e)=>setSearch(e.target.value)}
                />
            </Form.Group>
        </div>
    );
}

export default Filter