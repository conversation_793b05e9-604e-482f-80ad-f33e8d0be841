import React, { useState, useEffect, useCallback, Suspense, useRef } from 'react';
import { useHistory } from "react-router-dom";
import { useDispatch, useSelector } from 'react-redux';
import { Form, InputGroup, Spinner, Button } from 'react-bootstrap';
import { formatISO, startOfISOWeek, differenceInDays } from 'date-fns';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import Pagination from '../../../components/common/Pagination';

import * as actions from '../../../store/actions';
import { selectRangeStart, selectRangeEnd } from '../../../store/selectors';
import Services from '../../../api/Services';
import { generateServiceBlockTimeslots } from '../../../utils/dates';
import ErrorCatcher from '../../../components/common/ErrorCatcher';

import "react-datepicker/dist/react-datepicker.css";
import './Booking.scss';

const Selection = ({ serviceId=null, onChangeInput=()=>{}, goTo }) => {
    const dispatch = useDispatch();
    const mountedRef = useRef(false);
    const history = useHistory();

    const currentBooking = useSelector(state => state.serviceBooking.current_booking);
    const searchParams = useSelector(state => state.serviceBooking.search_params);
    const searchResults = useSelector(state => state.serviceBooking.search_results);

    const [pagePartDatePicker, setPagePartDatePicker] = useState();
    const [pagePartResults, setPagePartResults] = useState();
    const [errors, setErrors] = useState(null);

    //#region FILTERS

    const minDate = startOfISOWeek(new Date());

    const changeSearchParam = useCallback((name, value) => {
        dispatch(actions.setServiceBookingSearchParams({ [name]: value }));
    }, [dispatch]);

    useEffect(() => {
        // don't try to load the datepicker unless a valid date has been loaded
        if (searchParams.start_date && searchParams.end_date) {
            setPagePartDatePicker(
                <Suspense fallback={             
                    <SkeletonTheme color="#e0e0e0">
                        <Skeleton height={30} style={{marginBottom:"1rem"}} />
                        <Skeleton height={12} count={5} />
                    </SkeletonTheme>
                }>
                    <div className="service-filter-row">
                        <div className="form-row left center m-0">
                            {/* <div>Date Range:</div>
                            <DatePicker 
                                dateFormat="MM/dd/yyyy"
                                minDate={new Date()}
                                maxDate={new Date(new Date().getFullYear()+1,12,31)}
                                showMonthDropdown
                                showYearDropdown
                                selected={searchParams.start_date}
                                onChange={(e) => { changeSearchParam('start_date', e) }}
                                customInput={
                                    <Button variant="light" className="datepicker-calendar" type="button">
                                        {format(searchParams.start_date, "MM/dd/yyyy")}
                                    </Button>
                                }
                            />
                            <div>to</div>
                            <DatePicker 
                                dateFormat="MM/dd/yyyy"
                                minDate={searchParams.start_date}
                                maxDate={new Date(new Date().getFullYear()+1,12,31)}
                                showMonthDropdown
                                showYearDropdown
                                selected={searchParams.end_date}
                                onChange={(e) => { changeSearchParam('end_date', e) }}
                                customInput={
                                    <Button variant="light" className="datepicker-calendar" type="button">
                                        {format(searchParams.end_date, "MM/dd/yyyy")}
                                    </Button>
                                }
                            /> */}
                        </div>

                        <div>
                            <InputGroup className="search-input">
                                <Form.Control
                                    id="search_input"
                                    placeholder={`Search...`}
                                    aria-label={`Search Services`}
                                    value={searchParams.search}
                                    onChange={e => changeSearchParam('search', e.target.value)}
                                />
                                <InputGroup.Append>
                                    <InputGroup.Text>
                                        <i className="far fa-search"></i>
                                    </InputGroup.Text>
                                </InputGroup.Append>
                            </InputGroup>
                        </div>
                    </div>
                </Suspense>                                                                                                                                                                                 
            );
        }
    },[searchParams, changeSearchParam]);

    //#endregion Filters

    //#region SHOW RESULTS

    const [loading, setLoading] = useState(false);

    const onClickService = useCallback(event => {
        // replace the old blocks with the new blocks with timeslots
        let new_service = {
            ...event,
            blocks: generateServiceBlockTimeslots(event),
            selected_location: event.location_ids[0],
        }
        dispatch(actions.setServiceBooking({ service: new_service }));
        goTo("Timeslots");
    }, [dispatch, goTo]);

    const gotoPage = (page) => {
        if (page>0) changeSearchParam('page', page);
    }

    // on page load, if there's a serviceId, load the service
    useEffect(() => {
        if (serviceId) {
            setLoading(true);
            Services.get({ id: serviceId })
            .then(response => {
                if (mountedRef.current && response.data) {
                    if(response.data.services?.length>0) {
                        onClickService(response.data.services[0]);
                    }
                }
                else if (response.errors) {
                    setErrors(<ErrorCatcher error={response.errors} />);
                }
                setLoading(false)
            }).catch(e => console.error(e));
        }
    }, [serviceId, onClickService, dispatch]);

    useEffect(() => {
        mountedRef.current = true;

        // cancel stuff when component unmounts
        return () => {
            mountedRef.current = false;
            setLoading(false);
            dispatch(actions.setServiceBookingSearchResults({
                services: [],
                totalItems: 0,
                pages: []
            }));
        }        
    },[dispatch]);

    useEffect(() => {
        if (!serviceId && searchParams.end_date && searchParams.start_date) {
            setLoading(true);

            let searchTerms = {
                search_words: searchParams.search?.length>2 ? searchParams.search : null,
                max_records: searchParams.itemsPerPage,
                page_no: searchParams.page,
                sort_col: searchParams.sortColumn,
                sort_direction: searchParams.sortOrder,
                start_date: formatISO(searchParams.start_date),
                end_date: formatISO(searchParams.end_date),
                num_days: differenceInDays(searchParams.end_date, searchParams.start_date),
            }
            Services.get(searchTerms)
            .then(response => {
                if (mountedRef.current && response.data) {
                    const total_pages = Math.ceil(response.data.total_record_count/searchParams.itemsPerPage);
                    let tmp_pages = [];
                    for(let i=0; i<total_pages; i++){
                        tmp_pages.push(i);
                    }
                    dispatch(actions.setServiceBookingSearchResults({
                        services: response.data.services,
                        totalItems: response.data.total_record_count,
                        pages: tmp_pages
                    }));
                }
                else if (response.errors) {
                    setErrors(<ErrorCatcher error={response.errors} />);
                }
                setLoading(false)
            }).catch(e => console.error(e));
        }
    },[searchParams, currentBooking.service, dispatch, serviceId]);
    

    useEffect(() => {
        if (searchResults.services?.length>0) {
            setPagePartResults(
                <>
                    {searchResults.services.map(service => (
                        <div key={`service-booking-${service.id}`} className="service-list-item" onClick={() => onClickService(service)} data-cy="booking-rows">
                            {service.name}
                        </div>
                    ))}
                    {/* page numbers */}
                    {searchResults.pages.length>1?
                        <div className="service-pagination-bar">
                            <Pagination
                                itemsCount={searchResults.totalItems}
                                itemsPerPage={searchParams.itemsPerPage}
                                currentPage={searchParams.page}
                                setCurrentPage={(e) => changeSearchParam('page', e)}
                                alwaysShown={false}
                            />
                        </div>
                    : null}
                </>                                                                                                                                                                              
            );
        } else {
            if (serviceId) {
                setPagePartResults(
                    <div>
                        Service not found.
                        {serviceId &&
                            <div className="my-3 ">
                                <Button onClick={() => history.push("/p/my/services/book")}>Search All Services</Button>
                            </div>
                        }
                    </div>
                );
            } else {
                setPagePartResults(
                    <div>
                        No services found. Try changing the search filters.
                    </div>
                );
            }
        }
    },[searchParams, searchResults, changeSearchParam, onClickService, serviceId, history]);

    //#endRegion ShowResults

    return (
        <div>
            <Form.Row>
                {pagePartDatePicker}
            </Form.Row>
            <Form.Row>
                {loading && 
                    <div className="text-center py-3" style={{width: "100%"}}>
                        <Spinner animation="border" variant="secondary" />
                    </div>
                }
                {!loading &&
                    pagePartResults
                }
            </Form.Row>
            {errors}
        </div>
    );
}

export default Selection;