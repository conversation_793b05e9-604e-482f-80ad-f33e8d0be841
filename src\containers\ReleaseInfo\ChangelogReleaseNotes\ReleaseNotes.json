[{"date": "08/25/2025", "version": "1.28.01", "section": [{"title": "Refa<PERSON>", "notes": ["Added a new option for checkins to use the same banner that's being used for online order.  Made that the same height as the 'checkout' button on the preview pane.", "Utilized the register definition to provide values for tips on registers, defaulted to a default range of values if not.", "Added props that were previously hard coded on the backend.  Did so in such a way that previous registers that don't have the props can have a different new prop passed in to bypass needing them.  Did add the props to the register definition and rearranged the register definition inputs.", "Disabled the 'Scan Card' button if there's no terminal device id in the register definition.  Left a message on hover if it wasn't available.  ", "Reintroduced version checking/cache dumping.", "Fixed scroll on tables for form responses being unable to scroll horizontally if the screen was too big", "Added permissions to hide IP addresses on that table unless a user has permission for it", "Added emails to receipts based on register definition"]}, {"title": "Bug Fixes", "notes": ["Adjusted Detail Subpage to load a hash if it isn't already present so that the history stack was preserved.", "Print dialogue is showing in error on some registers", "Adjustments to the new UI for register creation - fixed the props on edited registers (buttons and tip values) and the types ids for all types being added to the register definition", "Added max size to image on the group page so that larger logos wouldn't break the page.", "POS Payment buttons are cut off at tablet sizes", "Fixed link to event details from agenda view indicating that it opened in a new tab when it didn't", "Various instances of the guest user being hardcoded were removed and made dynamic in accordance with the company configs (as many locations in the codebase already were)", "Email text editor was crashing in certain situations", "Fixed calendar timeline not loading properly"]}]}, {"date": "06/12/2025", "version": "1.28.00", "section": [{"title": "Features", "notes": ["Show, on the user profile, the orders associated with any event, who it's for, and link to said order page for easy access", "On a smaller size of screen, such as a tablet, the buttons at the top header will transform into a hamburger menu for easier access", "The POS has had scroll pagination added, to cut down on load times", "Added the ability to search for items in the POS", "Added the ability for managers to be able to override category or status for a register if permission allows it.  This could be helpful in a one off or selling a floor model of an otherwise unavailable item or making an exception to pay for an item at an otherwise incorrect register.", "Added UI elements for register definition to increase the amount of people who are able to make changes to a register and to defind the parameters in which they're able to do so", "Added 'Product Purchased' report", "Added killswitch for registers.  Will be most beneficial for online ordering, but can be utilized for any register.", "Allowed media manager to also create product images", "Added forms for training requests to the live site for youth basketball, youth volleyball, and pickleball.", "Created a component for loading skeletons to take the shape of cards rather than just tables.  Will be useful in making loading on product pages look better", "Added the title of the active form when viewing the form responses", "Added the ability to add phone numbers when completing an online order, which will be imported from the account when available, and will appear on the internal receipts for the kitchen"]}, {"title": "Refa<PERSON>", "notes": ["Added subpage common component to aspects of the services so they could be better organized and properly load hashes.", "Added the ability to see category ids on the category dashboard", "When there is no fee on an event, the event management paid will not show the tabs for 'paid/unpaid'", "Added giftcard balance onto the giftcard dashboard.  The page has permissions so can be hidden as need/wanted", "Checkin histories were getting long and unwieldy.  Added a common component for an accordion and utilized it on checkin histories", "Changed product categories on the product dashboard into a typeahead", "Allowed for the selection of product variants if the register definition allows for it allowing for events and variants to be handled similarly to front facing page", "Adjusted product creation/editing old subscription products being imported without data that new subscriptions have and being unable to be saved without editing the products.", "Adjusted product creation/editing to make add on products more clear.  Added helper text and added a checkbox to add products into an addon category vs a pos category."]}, {"title": "Bug Fixes", "notes": ["Ensured that a user is required for purchases such as livestreams, membersgips, etc", "Ensured that accounts flagged as 'is_guest' also cannot be used for items such as above.", "A flashing text of mistakenly not being able to view an event while it was loading has been adjusted", "Adjusted when you can click on a product's image to zoom in the POS; will not zoom when clicking to add the item if you click the picture", "If a POS/Page was reloaded too soon before an order was cleared, it didn't clear that order on reload.  Fixed and made sure completed orders cannot be loaded into the POS.", "Made sure that pre and trailing white spaces cannot interfere with the creation of a new account/user name.  Removes the white spaces appropriately", "In the cart checkout, would sometimes look like there was still an order after a successful purchase.", "Fixed console key errors on form submissions", "Fixed odd intermittent behavior on the typeaheads.  Should be clearing back to no search query on delete every time now.  Should be showing all matches provided by the backend on searches.", "Fixed visibility of answers to custom fields for events.", "Ensured that white space cannot be used as a way to circumvent required fields on event custom fields.", "Ensured that the banner that allows for online order management works properly and selects the proper orders each time."]}, {"title": "Tests", "notes": ["Added component tests on 4 common components (CSV Converter, CharacterCounterInput, DetailSubpage, and TableWithDropdown)", "Updated tests to use more streamlined variables.", "Fixed a number of broken tests."]}]}, {"date": "02/03/2025", "version": "1.27.00", "section": [{"title": "Features", "notes": ["A user specific order can be renewed via the POS", "Portions of the POS can be collapsed to provide more screen real estate, making things like a mobile POS on a tablet more feasible.", "Added a new help/support capabaility for admin users to access tech support if they need.  Takes the place of the old bottom left icon and service and adds a new one.", "UI was added to update the tax value for a location for select users with permission", "Added ability to choose categories for different times per register", "Added the ability to upload images for products.  Seeing those images on the register as a whole vs just when you click on products is a setting in register definitions."]}, {"title": "Bug Fixes", "notes": ["Online order 'pickup' button was reverted back to text 'order'", "Service Wizard at small sizes has no indication of what step you were on - added icons", "Ensured the name of the selected token was shown on the service summary", "Restructured the gift card page in order to allow for the breadcrumbs to work and for going back to the dashboard after competion.", "Zero dollar transactions no long lock up the POS with an incomplete (but complete) order.", "Fixed a crash that would occur if a tax value was right clicked on when it was already at zero", "Records available no longer disappears when there's only one page of results on the product page.", "Minor adjustments on the layout of the fields on the transactions page", "Fixed the tip on full paged invoices was not formatted like the rest of information", "Fixed an issue where the proper tax value for a location wasn't displayed (was utilized in the the calculations, just wrong display previous to fix) if was any value other than 7", "Added class to fix spacing on form-response page so it matches all the other pages", "Fixed issue between fix of the category typeaheads and the usage of the typeaheads", "Added a dispatch to make sure when a register definition is changed that the register is able to reload again", "When an event that doesn't require payment is registered for, added a modal for information", "Added conditionals just in case in the POS in terms of loading categories", "Created a long term fix for the category typeaheads on the product creation page not always getting every category", " - also applied this fix to the add-ons typeaheads", "Hot Fix: Fixed an issue where selecting certain subscriptions in the patron cart would cause the page to crash"]}, {"title": "Refa<PERSON>", "notes": ["Allowed for upcoming events to take up more screen real estate on desktop.  Adapted layout slightly to compensate", "Moved where the cart was stickied for the online register", "Changed wording on searching orders since it isn't just the last three of the order id but can be the full order id", "Adjusted product cards slightly for readability", "Changed the table on the form responses page to a responsive table for vertical scroll when there's too much information to fit", "Changed text when there are addons to say to add them instead of 'add to cart' to avoid potential confusion on online orders", "Added text to make it apparent when the guest user will be used for an order", "Streamlined event registration from the portal:", "- removed success colors when payment is necessary", "- added a button to go directly to the cart instead of rerouting back home", "- modal cannot be dismissed by clicking anywhere on the page - it must be addressed and a button must be clicked.", "- adjusted texts to reflect these changes as necessary"]}]}, {"date": "11/19/2024", "version": "1.26.00", "section": [{"title": "Features", "notes": ["The number of attendees is visible for each event in different statuses.  The different statuses also allow for filtering options so you could see just paid, just pending, etc.", "A tutorial page was added.  Tutorials will be added over time, but a few for starters, especially for staff, have been provided", "An alert system was added to notify a user when they have an order ready for pickup"]}, {"title": "Bug Fixes", "notes": ["With the addition of online orders, some orders were being unrefundable because of the order status.  Changed to include order statuses related to online pickup", "Fixed an issue with removing a user from an event and ensured the user list refreshed after doing so", "Added a delay before changing order status when doing online pickup to give the order a chance to print"]}, {"title": "Refa<PERSON>", "notes": ["Adjusted order page to be able to filter by all of the statuses available for online ordering.", "Adjusted the pickup management page to default to today when loading", "Adjusted orders to sort by 'uptdate' date rather than start and/or end."]}]}, {"date": "10/09/2024", "version": "1.25.00", "section": [{"title": "Features", "notes": ["Can download the displayed transactions as a CSV file from the transaction page", "Ability to download checkin history from the checkin history page", "Created a page to manage the hours associated with a register.", "Created a display that registers switch to when it is outside it's hours of operation.", "Created a page where orders with the statuses 'ordered', 'in process', and 'ready for pickup' can be managed outside of the pos", "Added a common component to create a banner across the bottom of the POS", "Utilized above common component on the POS to manage orders in the status related to order pickups", "A new layout for POSes was added to work well on mobile for mobile pickup.  Also maintains its own cart to not be confused with events or anything else", "Permission modules can have aspects copied to make creating permission modules easier", "Report was added for event payments - added in both the report page and accessible via the event page."]}, {"title": "Bug Fixes", "notes": ["Change wored on the menu edit page for administrators.", "Fixed the event typeahead on the discount page"]}, {"title": "Refa<PERSON>", "notes": ["Created a common component for turning selected data into a CSV file.", "Added props to the report page to also be able to utilize it and select reports in other locations based on module id.", "Event Management was restored to a proper page", "A common component was added to make all the pages like events standardized", "Can access event wizard via modal or page.", "Can download event attendees via 'manage users' or the 'download docs' page", "Event images were added to their own subpage for events that utilize images for instructions or other internal means"]}]}, {"date": "08/27/2024", "version": "1.24.02", "section": [{"title": "Bug Fixes", "notes": ["Allow registration for free events in the CMS Wizard"]}]}, {"date": "08/07/2024", "version": "1.24.01", "section": [{"title": "Bug Fixes", "notes": ["CMS reset password", "CMS auth logout"]}]}, {"date": "07/11/2024", "version": "1.24.0", "section": [{"title": "Features", "notes": ["The checkin scanner page can switch between front and back camera on mobile devices"]}, {"title": "Bug Fixes", "notes": ["Manager Discount is no longer shown in the 'by amount' or 'by payments' refund options", "More text clarity was added for the types of refunds", "Fixed where the text appeared when there is no order", "Collect JS fields should no longer be disappearing in the patron cart on some loads", "Gift card summary in the POS should reflect that it's balance remaining and not change issued when using a giftcard to checkout"]}, {"title": "Refa<PERSON>", "notes": ["Event registration in the portal no longer has the X on the wrong side"]}]}, {"date": "06/24/2024", "version": "1.23.3", "section": [{"title": "Bug Fixes", "notes": ["Adjusted terminal tips"]}]}, {"date": "06/19/2024", "version": "1.23.2", "section": [{"title": "Bug Fixes", "notes": ["Fixed some users in some companies being unable to update their DOB."]}]}, {"date": "06/18/2024", "version": "1.23.1", "section": [{"title": "Bug Fixes", "notes": ["Focus on the gift card field was reapplied after a giftcard was input"]}]}, {"date": "06/17/2024", "version": "1.23.0", "section": [{"title": "Features", "notes": ["Event types can no longer switch between a single event type and a meta event type as they are inherently very different.", "Can add custom text to receipts based on the register, register group, or company.", "Events can now show one of their images in the Portal", "When a new company is created, registers should be created automatically along with them.", "CMS button type event"]}, {"title": "Bug Fixes", "notes": ["One of the buttons on the report page was missing its text, has been corrected", "Grill printer should no longer be printing the tickets from the weekend out on Monday", "Invite email for events should be properly sending", "CMS event page does not open properly when providing the event hash", "Event hash is no longer being applied to all endpoints on the portal event page", "Prices on the order page should have a consistent number of decimal places and formatting", "Minor bug fixes with the POS, creating users, and the event wizard", "Added an alert when switching between certain payment methods in the POS", "Load event by hash id and/or name", "POS terminal failure"]}, {"title": "Refa<PERSON>", "notes": ["Changed check marks on the all orders page to a typeahead to both streamline styles and to accomodate more order types in the future.", "<PERSON><PERSON>ly refactored the way the report buttons get their names to make it easier to add more report types in the future."]}]}, {"date": "05/28/2024", "version": "1.22.2", "section": [{"title": "Bug Fixes", "notes": ["Pos receipt updates: CC last 4 and tip added to receipts"]}]}, {"date": "05/09/2024", "version": "1.22.1", "section": [{"title": "Bug Fixes", "notes": ["UI changes related to recurring events", "Adjusted event fees when you're creating a new event", "Adjusted styling on the children inputs upcoming event display to prevent overlap", "Pos cards (such as /shop and 'Upcoming Events' were misaligned and have been corrected", "Fixed product dashboard error when switching taxability state back to 'both' from either 'no taxable' or 'only taxable'.", "Fixed the module edit page, so that it is always the default menu item information that is displayed with the module and not any local menu items", "Created the ability to convert JSON to CSV", "Added transaction details to the CSV to be downloadable", "Added checkin history to be able to be downloaded as a CSV", "Fixed URL being created throwing errors on the /url page and made subdomains not required", "Attempted a fix for long names on QR code receipts being printed on multiple sheets", "Fixed url display on the /cms page not rendering and showing what's being clicked on", "Added limitation to portal upcoming events in accordance with the age ranges that can be added to events.", "Tips revert back to 0 in the POS when closing the checkout screen so there aren't any surprise charges when reopening", "Added space between payment types to make it easier on touch screens and fast paced use", "Allowed for discounts with no user", "Auto focus was added to checkout options in the POS", "Fixed editing an event with no images"]}]}, {"date": "04/01/2024", "version": "1.22.0", "section": [{"title": "Features", "notes": ["A users's orders can now be easily accessed through their profile page in the same manner as transactions", "Tables now have the option to have an additional row of information", "Users with the proper admin permissions are now able to instantly add or invite a user to a group from that user's profile"]}, {"title": "Refa<PERSON>", "notes": ["Media manager types are now buttons", "Media manager defaults to images now", "Tags on the media manager are now sorted alphabetically"]}, {"title": "Bug Fixes", "notes": ["Document media in the media manager was adjusted", "CMS slider z-index was corrected.  Adjustments to other misc z-indexes", "Addressed typos for some error handlers, adjusted error handler to better handle quotes", "Breadcrumbs on the upcoming events and shop page were not working properly", "Removed the notification to log in on a 401 (not logged in error) when that 401 is on the signout page on in regards to putting in improper credentials", "Fixed the layout of the cart page when there are no items in the cart", "Links to groups at the bottom left of a user profile were improperly navigating"]}]}, {"date": "03/12/2024", "version": "1.21.0", "section": [{"title": "Features", "notes": ["Gift cards are available for purchase, delivery to email, and for use", "Dashboard associated with giftcards is available for use with proper permissions", "A page has been added for people with the associated permissions to manage site URLs", "CMS video component", "Features available to a company are saved locally to cut down calls and to allow for new functionality in the future, including, but not limited to, customized home pages", "Editor has been created for creating/editing basic emails", "Site admins can now easily create default email templates", "In addition to categories in the POS, types have been added for more organization and classification", "Transactions now have more details available", "HTML widget was added to the cms"]}, {"title": "Refa<PERSON>", "notes": ["The methodology in which the code works for the payments and all associated systems has been refactored to work better together, be more versatile, and be more efficient", "CMS utilizes the new checkout functionality", "Split payments are easier to complete for the user d/t refactor of the checking out system", "Refunds have been refactored into a wizard, allowing for a more step-by step process and allowing more versatility in options"]}, {"title": "Bug Fixes", "notes": ["Deleting elements on the CMS", "Some places in the service wizard would refresh the page if 'enter' was tapped. It no longer does so", "Notes were not refreshing when adding or deleting one", "Event times are hidden with meta events", "Added visual tendered amount/change in various places", "Breadcrubs were incorrect on the product page with the new breadcrumb rework", "If a login token has expired, the user should correctly be kicked back to the login screen", "Error codes have been made consistent"]}]}, {"date": "01/09/2024", "version": "1.20.3", "section": [{"title": "Bug Fixes", "notes": ["Added the version of 'requires registration' from the company config to the event wizard", "Updated the description preview on the event wizard", "Adjusted the format of dates on the event view", "Minor adjustments on the event view for meta events"]}]}, {"date": "12-27-2023", "version": "1.20.2", "section": [{"title": "Bug Fixes", "notes": ["Hotfixed an addition to the dates on the products that prevented proper creation/editing of the products"]}]}, {"date": "12-20-2023", "version": "1.20.1", "section": [{"title": "Refa<PERSON>", "notes": ["CSS adjustments to uniformity in fontawesome icon usage", "Enhanced tooltip visual appeal and functionality", "Agenda view has been updated to include more features and functionality, including more filters and more date ranges available", "Removed some UI elements that could easily cause mistakes, such as the clear all on the managers typeahead for services", "Removed printers from the location typeaheads", "Added another method to access a user's QR code on the user's profile.", "Redesigned the profile events schedule to filter between user and family and improved look", "Changed the appearance of the locations available, providing a hierarchical view for easy viewing", "Refactored the code for breadcrumbs to ensure they function the same way and are consistent everywhere they're used"]}, {"title": "Bug Fixes", "notes": ["CSS adjustment to CMS imports", "Fixed a bug where no guest user in the config would prevent the usage of the patron cart", "Fixed an issue in the POS user search bar that would not always show the properly searched users"]}]}, {"date": "11-16-2023", "version": "1.20.0", "section": [{"title": "Features", "notes": ["New POS features", "Show 'for user', payment plan, and custom fields on POS", "Gallery component updates and media manager multi selection"]}, {"title": "Bug Fixes", "notes": ["P<PERSON> W<PERSON>th", "POS event registration fixes", "Removed manual event registration from CMS event registration component", "Pos change user and event items on cart", "Invoice and receipt printing styles, POS card size", "Transaction history html bugs", "CMS property duplication", "CMS undo bug, drag and drop bug", "Transaction page error with no transaction status", "Editing user would error without password as it's a shared component with new user", "Added down carat to transaction dashboard page", "Can properly assign a single user to a module's permission"]}]}, {"date": "11-16-2023", "version": "1.19.0", "section": [{"title": "Features", "notes": ["Show custom fields and payment plan in cart", "Added POS product type filter"]}, {"title": "Bug Fixes", "notes": ["CMS custom field validation", "CC validation on cms event registration"]}]}, {"date": "11/15/2023 ", "version": "1.18.1", "section": [{"title": "Features", "notes": ["Event Registration with Invalid CC", "Update CMS Event Registration Step"]}, {"title": "Bug Fixes", "notes": ["Event Wizard custom fields"]}]}, {"date": "11/13/2023", "version": "1.18.0", "section": [{"title": "Features", "notes": ["Event Change Status", "<PERSON><PERSON>t Redesign"]}, {"title": "Bug Fixes", "notes": ["Event Wizard Bugs", "Event Dashboard event status change button"]}]}, {"date": "11/13/2023", "version": "1.17.0", "section": [{"title": "Features", "notes": ["Event Change status, Patron cart redesign"]}, {"title": "Bug Fixes", "notes": ["Event Dashboard event status change button", "Public icon and title"]}]}, {"date": "11/10/2023", "version": "1.16.0", "section": [{"title": "Features", "notes": ["Replaced old product purchased check with new that allows for checking a specific user with a specific item", "When on a single day view in the calendar, day of the week shows with the date", "When a new user is created, an optional randomly generated password can be used instead of a selected one", "Can see the type of subscription on the POS when there are subscriptions without variants", "Added ability to access the company configs via the company dashboard", "Added attendees list", "Added short description in CMS events card view", "Added print button to CMS checkout component and and event history stack to registration modal", "Removed hardcoded guest account and added to company config", "Installments in CMS event registration widget, edit buttons on event creation summary"]}, {"title": "Refa<PERSON>", "notes": ["Transaction page and order page are now two different things, display different information, and linked to one another when related", "Addon details are now viewable on aspects of the old order page and receipts"]}, {"title": "Bug Fixes", "notes": ["Remove page_id from css injection", "Cart widget fixes event registration wizard widget, company store, bug fixes", "Fixed display bug where an error in removing a user from an event would still show as a success", "Fixed CMS localstorage crash on big pages", "Fix event registration add/edit custom fields, cms event wizard bugs", "CMS registration mltiple person payment fixed.", "Fixed empty coupon in CMS cart component", "Temporarily disable the addition of credit card to profile", "Fixed remove item/apply coupon in CMS cart component", "Fixed CMS checkout component, cart icon component, and checkout component", "Removed admin from user registration in CMS event component", "Fixed custom fields when editing an event, wizard component breakdown", "Fix print reciept admin fee", "POS cash payment fixes", "Fixed preventing double clicking the save button when creating an event", "Fixed empty variant in event registration wizard", "Fixes to CollectJS", "Fixes to subscriptions", "Fixed back button issue on CMS event creation wizard"]}]}, {"date": "10/20/2023", "version": "1.15.3", "section": [{"title": "Bug Fixes", "notes": ["Print Page has the proper props after checking out in the cart", "Conditionals were added to avoid future errors", "If somehow an order was completed and didn't load the props properly, the order will still be cleared from local storage so it can't be checked out again"]}]}, {"date": "10/10/2023", "version": "1.15.2", "section": [{"title": "Bug Fixes", "notes": ["Remove try catch from the event wizard to circumvent 409 response"]}]}, {"date": "10/6/2023", "version": "1.15.2", "section": [{"title": "Bug Fixes", "notes": ["CMS page access by group validations"]}]}, {"date": "10/5/2023", "version": "1.15.1", "section": [{"title": "Bug Fixes", "notes": ["Old CMS List component error", "CMS correctly remove all typeahead values when nothing is selected"]}]}, {"date": "09/25/2023", "version": "1.15.0", "section": [{"title": "Features", "notes": ["Event Wizard Updates"]}, {"title": "Bug Fixes", "notes": ["Ensured that Siteboss users can edit company configs regardless of where they're logged in from", "Fixes to the sort order of configs"]}]}, {"date": "09/22/2023", "version": "1.14.0", "section": [{"title": "Features", "notes": ["Added tip report", "Added the ability to edit config types.  Kept a view/edit page seperate and added warnings because editing config types can have unintented consequences", "If DOB is not required by the company config, will hide the references to requiring it", "Added the ability to click on a bundled product in the product description to be able to go to that bundled product", "Editing old configs will add any new parameters on that config type"]}, {"title": "Bug Fixes", "notes": ["Fixed conditional error on archived stream page causing it to error screen", "Added strikethough on original price on the old receipts printed from the order page", "Multiple small fixes for editing configs - fixed data types, typos, error when editing existing params", "Fixed ID when editing a feature so the feature edited properly on module edits", "Added star to form when DOB is required", "Made sure DOB from the congigs could look for a string OR a bool"]}]}, {"date": "09/15/2023", "version": "1.13.1", "section": [{"title": "Bug Fixes", "notes": ["Change new tab links"]}]}, {"date": "09/12/2023", "version": "1.13.0", "section": [{"title": "Features", "notes": ["Added permission widget for being able to view the status of an order"]}, {"title": "Bug Fixes", "notes": ["Signup Wizard Fixes", "Return the company config for the secret key based on sort order so if there are multiple configs, it grabs the proper one "]}]}, {"date": "08/23/2023", "version": "1.12.1", "section": [{"title": "Features", "notes": ["Able to utilize an amount refund rather than doing it by items. Allows for a simpler refund option", "Restricted Access", "Media Manager for uploading different types of data, such as images.", "Added payment method and change to receipts when applicable", "Added split payment indication on receipts and invoices printed after the fact", "Activated the ability for permissions via widgets, meaning small sections of functionality within a page can be controlled by permissions", "Removed flat fee from services as it isn't compatible with the new adidtion of multiple tokens per service."]}, {"title": "Refa<PERSON>", "notes": ["Menu CSS", "URLS/names of inconsistent routes - such as using the plural of a word in some cases and singular in others", "Added a different close button when editing a token product when creating a service so they do not overlap."]}, {"title": "Bug Fixes", "notes": ["Login reirect URL in the CMS", "Double = sign in parser", "Page access for company owner and above roles", "Fixed undefined register in refunds", "Page history content", "Ghost times in the service creator when cancelling a service and creating a new one", "Fixed a section of the service creator's summary that took the user back to the incorrect step", "Fixed the module data not always loading on edit with the input counter.  Ensured it can be used in both ways it is intended.", "When searching modules, it correctly goes back to page 1 when a new search is initiated"]}]}, {"date": "08/14/2023", "version": "1.11.6", "section": [{"title": "Bug Fixes", "notes": ["Hotfix: Potential fix for flickering products on the /shop page"]}]}, {"date": "08/11/2023", "version": "1.11.5", "section": [{"title": "Bug Fixes", "notes": ["Hotfix: Fixed the products not reflecting what printer was shown", "Hotfix: Patrons should be able to properly see their subscriptions on the homepage", "Hotfix: Patrons should no longer see the services dashboard"]}]}, {"date": "08/08/2023", "version": "1.11.4", "section": [{"title": "Features", "notes": ["Split payments activated on all registers for card/cash split", "Split payments activated on all registers for multiple users with a single parent order"]}, {"title": "Bug Fixes", "notes": ["Amount of tokens should no longer be saying 0 when tokens are present", "Menu items are filtering properly on menu edit page", "Staff should be able to successfully add a user to group", "Improvements and fixes were made to the module creation page", "Potential fix for error loading routes, alternate instructions/help when it fails", "Fixed success showing when there was actually an error inviting someone to a group", "Logo on mobile should show when the small logo loads properly", "Changes to CMS Header, Menu, and Form Components", "Changes to CMS Menu", "CMS Signup"]}]}, {"date": "06/27/23", "version": "1.11.3", "section": [{"title": "Bug Fixes", "notes": ["Api call added to retrieve form submissions", "Service event listing now shows manager without a link", "Service events now returning for service booking pages", "Transaction pages fixed and restored"]}]}, {"date": "06/26/23", "version": "1.11.2", "section": [{"title": "Bug Fixes", "notes": ["Fixed - On reset password, the link back to home doesn't give a page not found error", "Event attendee list now loading from the backend", "User info is now showing in the event attendee info modal"]}]}, {"date": "06/25/2023", "version": "1.11.1", "section": [{"title": "Bug Fixes", "notes": ["Fixed spacing on manager's list in viewing upcoming services"]}]}, {"date": "06/24/2023", "version": "1.11.0", "section": [{"title": "Features", "notes": ["added fontawesome typeahead to menu component", "added widgets for viewing and setting user roles and deactivating user accounts", "Can search the module deashboard in a paginated fashion", "CMS slider and mult-item, lists", "Family/Group subscriptions", "Open AI integration", "New permission Dashboard", "New owner config page", "Registration notes", "Onboarding update"]}, {"title": "Bug Fixes", "notes": ["Features - Company Overrides", "For meta events, the backend finds the company's default location now", "IDs to permission calls, style fixes", "Improved mobile styling", "Multiple tokens check all available", "No orphan folders are left in the menu", "Permission 'save changes' modal now center aligned with better spacing", "Permission Fixes", "Product defails are always loading in each place the products are loaded and for each type of product", "Sidebar is rerendered on undo/redo", "Selecting no bundles properly removes the selected tokens and hides related items", "Split aspects of the register data into those for normal POS and patron available things (like the checkout)", "Fixed 'unable to create new account' error", "Services can be better viewed from the product dashboard", "layout section, form widget", "fixed slider bugs", "Error on User Profile: Event Schedule", "fixed event registration credit card validation", "fixes to the event wizard family sign up", "CMS - undo/redo", "CMS - copy/paste bug", "fix double slash on login", "fixed empty index page bug", "fixed error message in event registration"]}]}, {"date": "05/08/2023", "version": "1.10.1", "section": [{"title": "Bug Fixes", "notes": ["error message on event registration fixed", "event wizard family sign up"]}]}, {"date": "05/13/2023", "version": "1.10.0", "section": [{"title": "Features", "notes": ["Registration Notes"]}, {"title": "Bug Fixes", "notes": ["Event Registration credit card validation", "Fix slider bugs"]}]}, {"date": "05/13/2023", "version": "1.9.4", "section": [{"title": "Bug Fixes", "notes": ["Overwrite redux's activeRegister with value from company config"]}]}, {"date": "05/12/2023", "version": "1.9.3", "section": [{"title": "Bug Fixes", "notes": ["Get the default register ID from config", "retrieve default patron cart from company config"]}]}, {"date": "05/10/2023", "version": "1.9.2", "section": [{"title": "Bug Fixes", "notes": ["User secret key instead of api key from configs for collectjs"]}]}, {"date": "05/08/2023", "version": "1.9.1", "section": [{"title": "Bug Fixes", "notes": ["Multiple tokens check all available", "QR code now opening correctly from user profile"]}]}, {"date": "04/26/2023", "version": "1.9.0", "section": [{"title": "Features", "notes": ["Created widgets for the upcoming permission system", "Added ability to print small receipts from the orders page rather than just from the POS"]}, {"title": "Bug Fixes", "notes": ["Camps correctly show on HBBCamp event calendar", "Ensured company details were being properly loading on receipts (address, logo, etc)", "Removed double down arrow from some dropdowns", "Fixed more locations where multiple tokens needed adjustments", "Product edit and add is no longer being mistaken as a modal because of ability to edit products from the services page", "Event-register from the homepage is properly redirection from the portal", "Creating a new account is properly redirecting to the right page", "POS no longer tries to find the old sidebar navigation to hide it on the POS", "CMS: Form elements weren't allowing drops", "CMS: Fixed load issues, history, and multple editors in browser"]}]}, {"date": "04/24/2023", "version": "1.8.1", "section": [{"title": "Bug Fixes", "notes": ["Multi-token services adjusted to make sure that multiple tokens are accepted by the booking system.  Tested with multiple types of tokens in the wallet"]}]}, {"date": "04/20/2023", "version": "1.8.0", "section": [{"title": "Features", "notes": ["Multiple tokens can be chosen to associate with a service", "Change role selection for a user from a typeahead with multiple selections to a single dropdown with only one selection", "Memberships sorted by type in the user sidebar in the POS", "Features added to the CMS system"]}, {"title": "Bug Fixes", "notes": ["At screen sizes smaller than 992px, menuy will be toggled to hidden by default", "Eliminated potential spill over of family data from login to the next on shared devices", "Fixed URLs that don't include /p for routes, redirections, and breadcrumbs.", "Logout token is is properly being cleared from local storage", "Password reset link adjusted", "Eliminated extra network call on orders page when there were no transactions to call", "Added gap to the my/group cards", "Ensured that when no items were in an order that 'simple refund' button is disabled along with the advanced.", "Made sure the button in advanced refunds reflects the proper amount to be refunded", "Fixed loops on accept invitation and location mamangement", "Fixed tabs in location management not clicking to other tabs", "Fixed issues with creating a new item in menu items", "Fixes for the CMS system"]}]}, {"date": "04/12/2023", "version": "1.7.2", "section": [{"title": "Bug Fixes", "notes": ["Changes to Permission UIs"]}]}, {"date": "04/04/2023", "version": "1.7.1", "section": [{"title": "Bug Fixes", "notes": ["Hide sidebar completely when not logged in", "<PERSON><PERSON> working properly"]}]}, {"date": "04/04/2023", "version": "1.7.0", "section": [{"title": "Features", "notes": ["Added UI to be able to sort and modify the menu based on modules", "Further improvements and quality of life additions to the CMS system"]}, {"title": "Refa<PERSON>", "notes": ["New side navigation menu"]}, {"title": "Bug Fixes", "notes": ["Customer is being recorded properly when items is added to the POS", "Menu fixed to screen height with separate scrolling", "Product categories properly showing all categories", "Servoce booking dates on mobile screens showing properly"]}]}, {"date": "03/31/2023", "version": "1.6.4", "section": [{"title": "Bug Fixes", "notes": ["Include the printer url in logged in routes as well as logged out routes"]}]}, {"date": "03/30/2023", "version": "1.6.3", "section": [{"title": "Bug Fixes", "notes": ["<PERSON><PERSON> auth shows on all orders regardless of being from transaction or order page for staff members", "Buttons aren't working on some permission pages", "Login button is disbaled when no username or password are entered", "New services weren't showing as bookable", "Prevent POS from being able to attempt to checkout with an order that has no items", "<PERSON><PERSON> was resolved that would cause some orders to attempt to resolve with a total of 0.00"]}, {"title": "Tests", "notes": ["Re-enabled the test that previously broke the build"]}]}, {"date": "03/28/2023", "version": "1.6.2", "section": [{"title": "Bug Fixes", "notes": ["Api error on typeahead preventing easy use on the service booking page"]}, {"title": "Tests", "notes": ["Removed test preventing build"]}]}, {"date": "03/22/2023", "version": "1.6.0", "section": [{"title": "Features", "notes": ["Create Popup for specific permission levels", "Default features and company features added to permission matrix"]}, {"title": "Refa<PERSON>", "notes": ["Retrieve Permissions for module creation dynamically"]}, {"title": "Bug Fixes", "notes": ["Reset Password 404 fix", "Disable return to default buttons if all settings are on default"]}]}, {"date": "03/16/2023", "version": "1.5.0", "section": [{"title": "Features", "notes": ["Add option to print location to split up receipts", "Add tutorials to permission pages", "Create urls to go directly to the service booking modal", "New style of release notes"]}, {"title": "Bug Fixes", "notes": ["Adjustments made to modules and features", "Adjustments to product creation and product display for features", "Changed report calendar time picker", "Changes to modules", "Convert styles for receipts to inline so they won't get lost on some prints", "Adjusted date available, default to today on new, close new category modal, and removed add ons in category typeahead for products", "Product price 0 allowed to be saved, 1970 error on some date fields in products addressed", "Refresh group page after successfully removing a group member"]}]}, {"date": "03/06/2023", "version": "1.4.1", "section": [{"title": "Bug Fixes", "notes": ["change livestream archives url back", "footer is showing in POS causing scrolling", "URL for event-register?event= "]}]}, {"date": "03/06/2023", "version": "1.4.0", "section": [{"title": "Features", "notes": ["add bill_num_times to the recurring subscriptions", "Add is_taxable to the Bulk Edit screen for Products", "Add saving to cookies for print location ", "added the ability for the api to determine timeouts ", "an orphan token cannot be applied to a bundle ", "Can change profile image from the homepage ", "Coupon Reports Available for Use ", "Endpoints UI, new Modules UI ", "Features UI ", "<PERSON><PERSON><PERSON> tutorial ", "select categories for reports", "Single user cash/card split", "Implementation of CMS feature", "CMS features: templates, pages, content blocks, forms, custom css, custom js, articles, blog posts, and knowledge bases ", "CMS style customization", "CMS elements: layouts, headings, lists, buttons, images, divider, and more"]}, {"title": "Code Style", "notes": ["Remove the eslint warnings (0d56e66), closes #8669nf534"]}, {"title": "Bug Fixes", "notes": ["Added company id to POS waiver", "Adding categories no longer updating the category list ", "broken links/urls ", "Cart refresh when removing last item ", "Correct url for event-register", "create new user typeahead ", "Display a message on small screen sizes for permissions page", "Is Taxable button on Product Edit now working ", "Login and direct links", "login redirecting to /p/p/home", "Maybe a fix for the 0.00 error ", "Prevent 'token expired, please log in' on incorrect username or password", "Prevent undefined .length error ", "Required field on location form", "Set event value to event.id instead of index", "Tooltip notifications not appearing on some icons ", "use /p in all history.push urls so redirect doesn't interfere with back button ", "user/update loop"]}]}, {"date": "02/12/2023", "version": "1.3.0", "section": [{"title": "Features", "notes": ["Add saving to cookides for print location"]}]}, {"date": "02/10/2023", "version": "1.2.0", "section": [{"title": "Features", "notes": ["Add is_taxable to the Bulk Edit screen for Products"]}, {"title": "Bug Fixes", "notes": ["'Is Taxable' button on product edit now working"]}]}, {"date": "02/02/23", "version": "1.1.0", "section": [{"title": "Features", "notes": ["Add email validation to register user and edit profile", "No longer require user login for headless print page"]}, {"title": "Bug Fixes", "notes": ["Bundle product wasn't saving correct token quantity", "Company add/edit will now save the api key properly", "Refund tax fields, refactoring, dependency fixes"]}]}, {"date": "01/31/2023", "version": "1.0.2", "section": [{"title": "Bug Fixes", "notes": ["move sla<PERSON> back to left side to not interfere with POS", "Printer margins on main restaurant receipt", "Remove 0 from in front of time on order receipt"]}]}, {"date": "02/02/2023", "version": "1.0.3", "section": [{"title": "Bug Fixes", "notes": ["Bundle product wasn't saving correct token quantity"]}]}, {"date": "01/30/2023", "version": "1.0.1", "section": [{"title": "Refa<PERSON>", "notes": ["Reserved events are able to use the link to privately invite users"]}]}, {"date": "01/30/2023", "version": "1.0.0", "section": [{"title": "Features", "notes": ["Add Print Width and Printer Type to Location Edit form ", "Added additional support for recurring subscriptions ", "Archived Streams ", "Create way to run tests through credit card payment ", "Different printer types including QR printer ", "Order status added to the order table ", "Remove Special Events from Home ", "Retrieve company address from Redux", "upgrade Node to 18.13 ", "User will be logged out if their auth token expires ", "Users are prompted to refresh upon new version number"]}, {"title": "Bug Fixes", "notes": ["Add node version to git actions ", "Add padding on selected menu subitems ", "add_on_only flag defaults to 0 instead of undefined ", "Adjust how event datetime is handled and converted to local time ", "Adjusted the category dashboard for increased usability and clarity for the end user ", "Bug fixes for Dev ", "category create/edit not passing parent_id to api ", "check props.category not null before accessing values ", "Clear Error Catcher after report is submitted ", "Correct merge conflict error ", "css, excessive calls to /user/menu ", "custom field form validation ", "Cypress Tests ", "Disabled copy button in the preview in the cart in the header ", "disallow submitting payment before paying ", "Edit family member, theme items missing ", "Error Catcher/Warning on registration ", "event pricing product link, variant tree date format ", "event type, status, and location not saving on edit ", "Event Wizard fixed to everything shows properly ", "Events saving with parent_id=0 ", "Fix build warnings ", "is_taxable set to null instead of 0 ", "lint warning ", "Module endpoint error ", "Modules not loading ", "More small fixes from QA ", "Product with no variants now can be edited ", "QA bugs ", "Receipt font and alignment is incorrect on regular order receipts ", "Receipt printer font sizes ", "Register definition will save in db correctly ", "Register Edit page not loading Typeaheads ", "Register json could be 0/1 or false/true ", "registration password match check triggering too early ", "Remove assets from semantic-release/github ", "removed ability to 'esc' out of record payment modal in the POS so that scan card won't be interrupted ", "Resolved eslint warnings ", "return Dockerfile to Node 13, add Node 18 to actions ", "send full object skeleton for new custom fields due to api quirk ", "Service Booking Style Fixes ", "Theme now resetting upon logout ", "Trigger a rebuild and new release number ", "Update release notes ", "Update styling on service booking", "Updated the module type dropdown to the new Module Types", "Require non-guest user for digital product types", "Typos, spacing, 202 status check"]}]}, {"date": "01/09/2023", "version": "0.0.99", "section": [{"title": "Features", "notes": ["New Archived Stream Page"]}]}, {"date": "01/06/2023", "version": "0.0.98", "section": [{"title": "Features", "notes": ["upgrade Node 18.13"]}, {"title": "Bug Fixes", "notes": ["add_on_only flag defaults to 0 instead of undefined", "category create/edit not passing parent_id to api", "check props.category not null before accessing values", "css, excessive calls to /user/menu", "custom field form validation", "disallow submitting payment before paying", "event pricing product link, variant tree date format", "event type, status, and location not saving on edit ", "Events saving with parent_id=0 ", "is_taxable set to null instead of 0 ", "registration password match check triggering too early", "send full object skeleton for new custom fields due to api quirk", "Add node version to git actions", "Remove assets from semantic-release/github", "return Dockerfile to Node 13, add Node 18 to actions", "Trigger a rebuild and new release number"]}, {"title": "<PERSON><PERSON>", "notes": ["Fixed 'parsing error' blocking build", "fix pos only getting on category if child categories are present"]}]}]