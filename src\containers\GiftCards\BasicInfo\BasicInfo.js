import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from "react-router-dom";
import { Container, Row, Col, Form, InputGroup, Button } from 'react-bootstrap';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Toast from '../../../components/Toast';

import APIGiftCards from '../../../api/GiftCards';


//Not sure why it has the id in here to try to pull the id.  Editing the giftcard does not currently have a proper process
//Adding balance should be done via a transaction
//Changing receipient and stuff (in case of a typo or to resend the code) needs a job to follow it up to actually send so we don't have the processes in place
export const BasicInfo = (props) => {
    let history = useHistory();

    const [giftcard, setGiftcard] = useState(null);
    const [cardCode, setCardCode] = useState("");
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();

    //because we shouldn't be editing, we don't ever need to get the giftcard, currently
	// useEffect(() => {
    //     const getGiftCard = async () => {
    //         try {
    //             const res = await APIGiftCards.get({id:props.giftcard_id});
    //             if (res?.data?.giftcards?.data?.[0]) {
    //                 setGiftcard(res.data.giftcards.data[0]);
    //             } else if (res.errors){
    //                 console.error(res.errors);
    //             }
    //         } catch (error){
    //             console.error(error);
    //         }
    //     }

    //     if (props?.giftcard_id) {
    //         getGiftCard();
    //     }
	// }, [props.giftcard_id]);

    // create a random code
    const generateCodeHandler = useCallback(() => {
        const characters = 'ACEFGHJKLMNPQRSTUVWXYZ2345679';
        const charactersLength = characters.length;
        let gcString = '';
        for (let i = 0; i < 6; i++) {
            gcString += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        setCardCode(gcString);
    },[]);

    // check that all the additional values are correct and filled out
    const validateForm = useCallback((form) => {
        let valid = true;
        let errorsDisplay = [];

        if (form.checkValidity() === false) {
            errorsDisplay.push("Please fill out all required fields");
            valid = false;
        }

        setError(<>{errorsDisplay.map((err, i) => <div key={`err-${i}`} className="error-text">{err}</div>)}</>);
        return valid;
    },[]);

    // form submission
    const submitHandler = useCallback(async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setError();
        setSuccess(null);

        if (validateForm(form)) {

            const formData = new FormData(e.target);
            if (props.giftcard_id) formData.append("id", props.giftcard_id);
            const formDataObj = Object.fromEntries(formData.entries());

            let response;
            if (props.giftcard_id) response = await APIGiftCards.update(formDataObj).catch(e => console.error(e))
            else response = await APIGiftCards.create(formDataObj).catch(e => console.error(e))
            try {
                if (response.status===200 && !response?.errors) {
                    setSubmitting(false);
                    setValidated(false);
                    setSuccess(<Toast>Gift Card {props.giftcard_id ? "edited" : "created"} successfully!</Toast>);
                } else { // api returned errors
                    setSubmitting(false);
                    setError(<ErrorCatcher error={response.errors} />);
                } 
            }
            catch(e) { //no response at all
                setSubmitting(false);
                setError(<ErrorCatcher error={e} />);
            }
        } else setSubmitting(false);
    },[validateForm, props.giftcard_id]);

    return (
        <Container fluid>
            <h4 className="section-title">{props.giftcard_id?"Edit":"New"} Gift Card</h4>
            <hr/>
            {success}
            <Form noValidate validated={validated} onSubmit={submitHandler}>
                <Row>
                    <Col sm="12" lg={4}>
                        <Form.Group controlId="card_code">
                            <Form.Label>
                                Code
                                <span className="required-star">
                                    {" "}*
                                </span>
                            </Form.Label>
                            <InputGroup>
                                <Form.Control required type="text" max="36" name="card_code" readOnly value={cardCode || giftcard?.card_code  || ""} />
                                <InputGroup.Append>
                                    <Button variant="outline-secondary" onClick={generateCodeHandler}>New</Button>
                                </InputGroup.Append>
                            </InputGroup>
                        </Form.Group>
                    </Col>
                </Row>
                <Row>
                    <Col sm="12" lg={6}>
                        <Form.Group controlId="recipient_full_name">
                            <Form.Label>
                                Recipient Full Name
                                <span className="required-star">
                                    {" "}*
                                </span>
                            </Form.Label>
                            <Form.Control required type="text" name="recipient_full_name" defaultValue={giftcard?.recipient_full_name || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg={6}>
                        <Form.Group controlId="recipient_email">
                            <Form.Label>
                                Recipient Email
                                <span className="required-star">
                                    {" "}*
                                </span>
                            </Form.Label>
                            <Form.Control required type="email" name="recipient_email" defaultValue={giftcard?.recipient_email || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg={8}>
                        <Form.Group controlId="recipient_email">
                            <Form.Label>
                                Recipient Message
                            </Form.Label>
                            <Form.Control type="text" name="recipient_message" defaultValue={giftcard?.recipient_message || ""} />
                        </Form.Group>
                    </Col>
                    <Col sm="12" lg={4}>
                        <Form.Group controlId="current_balance">
                            <Form.Label>Amount</Form.Label>
                            <InputGroup>
                                <InputGroup.Prepend>
                                    <InputGroup.Text>$</InputGroup.Text>
                                </InputGroup.Prepend>
                                <Form.Control type="number" name="current_balance" defaultValue={giftcard?.current_balance || 0.00} />
                            </InputGroup>
                        </Form.Group>
                    </Col>
                </Row>
                <Form.Row>
                    <Col sm="12" lg="4" className="mt-4 mb-3">
                        <Button variant="primary" type="submit" disabled={submitting} className={`${submitting?" submitting":""}`}>Save</Button>
                    </Col>
                </Form.Row>
            </Form>
            {error}
        </Container>
    );
}