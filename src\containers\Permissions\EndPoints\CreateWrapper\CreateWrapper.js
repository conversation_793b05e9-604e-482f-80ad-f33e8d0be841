import React, { useState, useEffect, useRef } from 'react'
import { Container, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import SubHeader from '../../../../components/common/SubHeader';
import { setErrorCatcher, setSuccessToast } from '../../../../utils/validation';
import CreateEndpoints from '../CreateEndpoints';

export const CreateWrapper = () => {

    const mountedRef = useRef(false);
    const [success, setSuccess] = useState();

    useEffect(()=>{
        mountedRef.current = true

        return()=> mountedRef.current = false
    },[])

    return (
        <Container fluid>
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/endpoints" }, text: "Permissions" },
                { text: "New Endpoints" }
            ]} />
            <Card className="content-card">
                <h4 className="section-title">
                    Create New Endpoints
                </h4>
                <div>
                    {success}
                    <CreateEndpoints setSuccess={setSuccess}/>
                </div>
            </Card>
        </Container>
    )
}