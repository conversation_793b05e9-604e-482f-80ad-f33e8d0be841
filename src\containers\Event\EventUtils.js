export const selectAcceptableColors=(event_type_amounts)=>{
    const colorsByType=[]
    const acceptableColorArray=["#5E46E7","#DFCC24","#1A57B0","#197F18","#E628BB","#2196f3","#DE8736","#76D34B","#7F1DB4","#93BE38","#C72346","#1AB077"]
    //this will work for up to 24 different event types.  If a company needs more colors, more conditionals or math can be added later.
    let index = 0
    if(index > acceptableColorArray.length && index < acceptableColorArray.length * 2){
        index = index - acceptableColorArray.length;
    }
    for(let i = 1; i < event_type_amounts; i++){
        colorsByType.push({
            event_type_id: i,
            color: acceptableColorArray[index]
        })
        index++
    }
    return colorsByType;
}