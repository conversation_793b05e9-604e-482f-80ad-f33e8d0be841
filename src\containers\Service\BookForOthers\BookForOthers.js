import React, { useState, useCallback} from 'react';
import { Card } from 'react-bootstrap';
import { useDispatch } from 'react-redux';
import { parseISO, differenceInMinutes, formatISO } from 'date-fns';

import { SelectUser, SelectService, SelectBookingTime, SelectTokens, ErrorDiv } from './components';
import MultiStep from '../../../components/common/MultiStep';
import { generateServiceBlockTimeslots } from '../../../utils/dates';
import { addToCart } from '../../../utils/thunks';
import POS from '../../../api/Pos';
import Services from '../../../api/Services';

export const BookForOthers = ({
    ...props
})=>{

    const dispatch = useDispatch();

    const [errors, setErrors]=useState({users: [], service: [], booking: [], tokens: [], general: []})
    const [services, setServices] = useState([]);
    const [activeService, setActiveService]=useState(null);
    const [dateWeek, setDateWeek]=useState(new Date());
    const [selectedUser, setSelectedUser]=useState(null);
    const [userTokens, setUserTokens]=useState([]);
    const [selectedBookings, setSelectedBookings]=useState([]);
    const [selectedLocation, setSelectedLocation]=useState(null);
    const [matchedTokens, setMatchedTokens]=useState({text: <></>, matched: []});
    const [conflictEvents, setConflictEvents]=useState(null);
    const [selectedProduct, setSelectedProduct]=useState([]);
    const [goToName, setGoToName]=useState(null);
    const [stepName, setStepName]=useState("");

    const goTo = useCallback(name =>{
        setGoToName(name);
        setTimeout(() => { setGoToName(null); }, 1000);
    },[])

    const handleError=(errorType, error)=>{
        setErrors(prev=>{
            let newErrors = {...prev};
            newErrors[errorType] = error;
            return newErrors;
        })
    }

    const validate = (pageName)=>{
        let error={users: [], service: [], booking: [], tokens: []};
        let validated = true;
        switch(pageName){
            case'SelectUser':
                if(!selectedUser) {
                    error.users.push("You must select a user.");
                }
                handleError("users", error.users)
                break;
            case'SelectService':
                if(!activeService){
                    error.service.push("You must select a service.");
                }
                handleError("service", error.service)
                break;
            case'SelectBookingTime':
                if(!selectedLocation){
                    error.booking.push("Please make sure a location is selected!");
                }
                if(selectedBookings.length < activeService.min_timeslots){
                    error.booking.push(`Please select at least ${activeService.min_timeslots} timeslots.`);
                }
                handleError("booking", error.booking)
                break;
            case'SelectTokens':
                if(matchedTokens.matched.length < selectedBookings.length && !selectedProduct){
                    error.tokens.push("If using tokens a user already has, please make sure they have enough.");
                }else if(!selectedProduct){
                    error.tokens.push("Please select a token to purchase.");
                }
                handleError("tokens", error.tokens)
                break;
            default:
                error={users: [], service: [], booking: [], tokens: []}
                break;
        }
        if(error.users.length > 0 || error.service.length > 0 || error.booking.length > 0 || error.tokens.length > 0) validated = false;
        return validated;
    }

    const setBlocks = (service)=>{
        let newBlocks = generateServiceBlockTimeslots(service);
        service.blocks = newBlocks;
    }

    const adaptTimeSlots=async ()=>{
        let obj = { start: null, end: null, duration: 0 };
        selectedBookings.forEach(slot=>{
            let start = parseISO(slot.start);
            let end = parseISO(slot.end);
            if (obj.start===null || start < obj.start) {
                obj.start = start;
            }
            if (obj.end===null || end > obj.end) {
                obj.end = end;
            }
            obj.duration = differenceInMinutes(obj.end, obj.start);
            return slot;
        });
        return obj;
    }

    const handleFormatBooking = async (token)=>{
        let adaptedTimeSlot = await adaptTimeSlots();
        const bookingObject ={
            service_id: activeService?.id,
            location_id:activeService?.location_ids[0], 
            user_id: selectedUser?.id,
            start_datetime: formatISO(adaptedTimeSlot?.start),
            end_datetime: formatISO(adaptedTimeSlot?.end),
            token_quantity: selectedBookings?.length,
            product_variant_id: token.variant_id
        }
        return bookingObject;
    }

    const handleBooking=async(token)=>{
        
        let bookingObject = await handleFormatBooking(token);
        try{
            let response = await POS.payment.service_tokens(bookingObject);
            console.log(response)
        }catch(ex){
            console.error(ex)
            handleError(ex)
        }
    }

    const makeBooking = async ()=>{
        let boookingObject = await handleFormatBooking(selectedProduct);
        try{
            let response = await Services.createBooking(boookingObject);
            if(response.status === 200) return response.data;
            else if(response.errors) handleError(response.errors)
            else handleError("Unknown error creating booking")
        }catch(ex){
            console.error(ex)
            handleError(ex)
        }
    }

    const handleAddToCart = async()=>{
        let data = await makeBooking();
        let tokensToBuy = selectedBookings?.length - matchedTokens.matched?.length || 0;
        let products = [];
        for(let i=0; tokensToBuy > i; i++){
            let item = {
                product_name: selectedProduct.name,
                product_id: selectedProduct.id,
                variant_id: selectedProduct.variants[0].id,
                variant_name: selectedProduct.variants[0].name,
                qty: 1,
                event:{
                    event_id: data.id,
                    for_user_id: selectedUser?.id
                }
            }
            console.log(selectedProduct)
            products.push(item)
        }
        dispatch(addToCart(products, false, 6, selectedUser.id));
        //order update it getting a 500 from the backend
    }

    const checkServiceAndUserTokens=(userTokens, tokensNeeded, slots)=>{   
        if(userTokens && tokensNeeded && slots){
            let returned ={
                text: <></>,
                matched: []
            }
    
            tokensNeeded.forEach(token =>{
                userTokens.forEach((userToken)=>{
                    if(token?.id === userToken?.id) returned.matched.push(userToken);
                })
            })
    
            setMatchedTokens(returned);
        }
    }

    const saveStepName = (name)=>{
        setStepName(name)
    }

    const stepOrder = [
        'SelectUser',
        'SelectService',
        'SelectBookingTime',
        'SelectTokens'
    ]

    const steps = [
        {
            name: 'SelectUser', 
            icon: "far fa-user",
            component: 
                <SelectUser 
                    setSelectedUser={setSelectedUser}
                    selectedUser={selectedUser}
                    checkServiceAndUserTokens={checkServiceAndUserTokens}
                    activeService={activeService}
                    userTokens={userTokens}
                    setUserTokens={setUserTokens}
                    selectedBookings={selectedBookings}
                    handleError={handleError}
                    errors={errors.users}
                />
        },
        {
            name: 'SelectService', 
            icon: "far fa-mountains",
            component: 
                <SelectService 
                    setServices={setServices}
                    services={services}
                    activeService={activeService}
                    handleError={handleError} 
                    setBlocks={setBlocks}
                    dateWeek={dateWeek}
                    setConflictEvents={setConflictEvents}
                    setSelectedLocation={setSelectedLocation}
                    setActiveService={setActiveService}
                    checkServiceAndUserTokens={checkServiceAndUserTokens}
                    userTokens={userTokens}
                    selectedBookings={selectedBookings}
                    errors={errors.service}
                />
            
        },
        {
            name: 'SelectBookingTime', 
            icon: "far fa-calendar-star",
            component: 
                <SelectBookingTime 
                    setSelectedBookings={setSelectedBookings}
                    checkServiceAndUserTokens={checkServiceAndUserTokens}
                    userTokens={userTokens}
                    activeService={activeService}
                    setDateWeek={setDateWeek}
                    dateWeek={dateWeek}
                    setSelectedLocation={setSelectedLocation}
                    selectedUser={selectedUser}
                    selectedLocation={selectedLocation}
                    conflictEvents={conflictEvents}
                    handleError={handleError}
                    errors={errors.booking}
                />
        },
        {
            name: 'SelectTokens', 
            icon: "far fa-coins",
            component: 
                <SelectTokens 
                    checkServiceAndUserTokens={checkServiceAndUserTokens}
                    userTokens={userTokens}
                    activeService={activeService}
                    selectedBookings={selectedBookings}
                    setSelectedProduct={setSelectedProduct}
                    selectedUser={selectedUser}
                    handleError={handleError} 
                    matchedTokens={matchedTokens}
                    slots={selectedBookings}
                    handleBooking={handleBooking}
                    errors={errors.tokens}
                />
        },
    ]

    return(
        <Card className="content-card">
            <MultiStep 
                showNavigation={true}
                goTo={goToName}
                steps ={steps}
                stepOrder={stepOrder}
                saveStep={saveStepName}
                onChangeStep={validate}
            />
            <ErrorDiv errors={errors.general} />
        </Card>
    )
}