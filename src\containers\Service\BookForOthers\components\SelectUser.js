import React from 'react';

import { ErrorDiv } from './ErrorDiv';
import { NewUserTypeahead } from '../../../../components/Typeahead/NewUserTypeahead';

import Users from '../../../../api/Users';

export const SelectUser = ({
    setSelectedUser,
    selectedUser,
    checkServiceAndUserTokens,
    activeService,
    userTokens,
    setUserTokens,
    selectedBookings,
    handleError,
    errors,
    ...props
})=>{
    const sortTokens = (tokens) => {
        if (!tokens || !Array.isArray(tokens)) return [];

        const grouped = tokens.reduce((acc, token) => {
            const key = token.product_name;

            if (!acc[key]) {
                acc[key] = {
                    id: token.product_id,
                    product_name: token.product_name,
                    number_of_tokens: 0,
                    exp_dates: [],
                    product_variant_id: token.product_variant_id
                };
            }

            acc[key].number_of_tokens++;
            if (token.exp_date) {
                acc[key].exp_dates.push(token.exp_date);
            }

            return acc;
        }, {});

        return Object.values(grouped).map(group => {
            let soonest_exp = null;

            if (group.exp_dates.length > 0) {
                // Find the earliest date
                soonest_exp = group.exp_dates.reduce((earliest, current) => {
                    const currentDate = new Date(current);
                    const earliestDate = new Date(earliest);
                    return currentDate < earliestDate ? current : earliest;
                });
            }

            return {
                id: group.id,
                product_name: group.product_name,
                number_of_tokens: group.number_of_tokens,
                soonest_exp: soonest_exp,
                variant_id: group.product_variant_id
            };
        });
    }

    const handleSelectedUser=(selection)=>{
        setSelectedUser(selection[0]);
        getUserTokens(selection[0]?.id);
        checkServiceAndUserTokens(userTokens, activeService?.products, selectedBookings)
    }

    const getUserTokens=async (id)=>{   
        //need to get user tokens and see if the user has the token
        //if so, can use the tokens for the booking
        try{
            let response = await Users.tokens({
                user_id: id
            })
            if(response.status === 200 && response.data) {
                setUserTokens(sortTokens(response.data))
                checkServiceAndUserTokens(response.data, activeService?.products, selectedBookings)
            }
            else if (response.errors) handleError("general", response.errors)
            else handleError("general", "Unknown error getting user tokens")
        }catch(ex){
            console.error(ex)
            handleError("general", ex)
        }
    }

    return(
        <>
            <NewUserTypeahead 
                passSelection={(selection)=>handleSelectedUser(selection)} 
                multiple={false}
                initialData={selectedUser ? [selectedUser] : null}
                async={true}
            />
            <ErrorDiv errors={errors} />
        </>
    )
}