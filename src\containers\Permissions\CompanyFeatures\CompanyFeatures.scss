@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/themes';

.company-features{
    .row-pair{
        @include basic-flex-row;
    }
    .input-col{
        @include basic-flex-column;
    }
    input:not([type="checkbox"]):not([type="radio"]), select{
        @include basic-input-select;
    }
    label{
        @include basic-label;
    }
    .positive-row{
        background-color: #40824b57;
    }
    .negative-row{
        background-color: #b90e0e49;
    }
    .y-n{
        padding: 4px 12px 4px 12px;
        width: 55px;
    }
    .view-assign-features{
        table{
            td.override-checkbox{
                border-right: hidden;
            }
            th, td, tr{
                border: 1px solid $primary-font-color;
                padding: 4px 12px 4px 12px;
            }
            th.label{
                border-left: none;
            }
            i{
                vertical-align: middle;
                color: $primary-font-color;
                height: auto;
            }
            .check-change{
                padding-right: 6px;
                font-size: 6px;
            }
            input{
                vertical-align: middle;
            }
            .check-td{
                text-align: center;
            }
        }
    }
    .default-features-wrapper{
        table{
            th, td, tr{
                border: 1px solid $primary-font-color;
                padding: 4px 5px 4px 5px;
            }
            .check-td{
                text-align: center;
            }
            input{
                vertical-align: middle;
            }
            i{
                padding-right: 6px;
                font-size: 6px;
                vertical-align: middle;
                color: $primary-font-color;
                height: auto;
            }
        }
    }
    .override-checkbox {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        border: none;
        .override-checkmark {
            visibility: hidden;
        }
    }
    .override-checkbox input[type="radio"] {
        appearance: none;
        position: relative;
    }
    .override-checkbox input[type="radio"] + label {
        margin-left: 20px;
        font-weight: normal;
        color: $primary-font-color;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
    }
    .override-checkbox input[type="radio"].markthis + label {
        .override-checkmark {
            margin-right: 8px;
        }
    }
    .override-checkbox input[type="radio"]:checked.markthis + label {
        font-weight: bold;
        .override-checkmark {
            visibility: visible;
        }
    }
}