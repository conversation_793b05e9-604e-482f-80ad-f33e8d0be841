import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Container, Card, Button, Modal } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { useLocation, Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import SubHeader from '../../components/common/SubHeader';
import SortOrderTree from '../../components/common/SortOrderTree';
import ExternalMenuItem from './NewMenuItem/MenuItemComponents/ExternalMenuItem';
import NewMenuItem from './NewMenuItem';
import ConfirmDelete from './NewMenuItem/MenuItemComponents/ConfirmDelete';
import AddFolder from './NewMenuItem/MenuItemComponents/AddFolder';
import { adjustMenuItems, assignParentIds, getHighestSortOrder, getOnlyDefaultItems, onlyCompanyItems, disabledDefaultItems } from '../../components/common/SortOrderTree/SortOrderTreeUtils'
import { setErrorCatcher, setSuccessToast } from '../../utils/validation'
import { getMenuItems } from './MenuItemUtils';
import { getSingleModule, getMenuItems as getSingleItem } from '../Permissions/PermissionsUtils/PermissionUtils';
import usePrevious, { useRoleCheck } from '../../components/common/CustomHooks';

import Permissions from '../../api/Permissions';
import './MenuItem.scss'
export const MenuItems = ({importedCompanyId=null, companyName=null}) => {

    const mountedRef = useRef(false);
    const currentOrder = useRef([]);
    const currentType = useRef();
    const newItemRef = useRef(0);
    const location = useLocation();
    const userRole = useRoleCheck();
    const reduxCompanyId = useSelector(state =>state.auth.user.company_id)
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [reset, setReset] =useState(false);
    const [menuItems, setMenuItems] = useState([]);
    const [disabledItems, setDisabledItems] = useState([]);
    const [highestSortOrder, setHighestSortOrder]=useState(0) //parent length, once set, already includes a +1 for a new item
    const [activeItem, setActiveItem]=useState(null); //the selected item in the tree
    const [clearSelected, setClearSelected]=useState(false); //to pass to the tree to toggle it to clear the selected item if true
    const [companyId, setCompanyId]=useState(null); 
    const [orderHasChanged, setOrderHasChanged]=useState(false); //as soon as the first change is made, toggles to true to control theings like disabling buttons and clicking on confirmation for certain actions
    const oldCompanyId = usePrevious(companyId); 

    const [modalShow, setModalShow]=useState(false);
    const [showNewItem, setShowNewItem]=useState(false);
    const [showEditItem, setShowEditItem]=useState(false);
    const [showExternal, setShowExternal]=useState(false);
    const [showDelete, setShowDelete]=useState(false);
    const [showDisable, setShowDisable]=useState(false); //to disable an item
    const [showFolder, setShowFolder]=useState(false);
    const [showConfirmation, setShowConfimation]=useState(false);
    const [showDisabledItems, setShowDisabledItems]=useState(false); //to see the tree of disabled items
    const adminDash = useRef(JSON.parse(localStorage.getItem("adminDash")));
    const ownerDash = useRef(JSON.parse(localStorage.getItem("ownerDash")));

    const getMenuItemData = useCallback(async( companyOrDefault )=>{
        let response;
        if(companyOrDefault === "default") response = await getMenuItems();
        //even though we don't need the company ID for the call to get the items, we do need it when we sort them
        //if we want to get the company details but don't yet have the id, we want to wait until we do have it
        if(companyOrDefault === "company" && companyId) response = await getMenuItems(); 
        if(response?.errors) {
            setError(setErrorCatcher(response.errors))
            setLoading(false);
        }
        else if(response?.data && mountedRef.current) {
            let sorted;
            let sortDisabled;
            if(location.pathname.includes("default")){
                let defaultItems = await getOnlyDefaultItems(response.data);
                sorted = await adjustMenuItems(defaultItems);
                let disabledItems = await disabledDefaultItems(response.data)
                sortDisabled = await adjustMenuItems(disabledItems);
            
            }else{
                let companyItems = await onlyCompanyItems(response.data, companyId)
                sorted = await adjustMenuItems(companyItems);
                let disabledCompanyItems = await onlyCompanyItems(response.data, companyId, false);
                sortDisabled = await adjustMenuItems(disabledCompanyItems)
            }
            setMenuItems(sorted);
            setDisabledItems(sortDisabled);
            setLoading(false);
            setOrderHasChanged(false)
        }
    },[companyId, location.pathname])

    useEffect(()=>{
        mountedRef.current = true

        return()=> mountedRef.current = false
    },[])

    useEffect(()=>{
        if(mountedRef.current && menuItems.length>0){
            let filtered = menuItems.filter(item=>item.parent_id === null);
            let highest = getHighestSortOrder(filtered)
            setHighestSortOrder(highest)
        }
    },[menuItems])

    useEffect(()=>{
        if(location.pathname.includes("default")){
            getMenuItemData("default")
            setCompanyId(null)
        }
        else if(!location.pathname.includes("default")){
            if(importedCompanyId && !companyId && mountedRef.current){
                setCompanyId(importedCompanyId)
            }else if(!companyId && !importedCompanyId && mountedRef.current){
                setCompanyId(reduxCompanyId)
            }
            if(companyId && mountedRef.current) {
                setLoading(true);
                getMenuItemData("company");
            }
        }
    },[importedCompanyId, companyId, oldCompanyId, getMenuItemData, location, reduxCompanyId]);

    //adminDash and ownerDash both have to be separate useEffects or it will cause timing issues
    useEffect(()=>{
        if(adminDash.current && mountedRef.current){
            setMenuItems([])
            setCompanyId(adminDash.current?.selectedCompany[0]?.id)
        }
    },[adminDash]);

    useEffect(()=>{
        if(ownerDash.current && mountedRef.current){
            setMenuItems([])
            setCompanyId(ownerDash.current?.selectedCompany[0]?.id)
        }
        if(!ownerDash.current && !adminDash.current && !importedCompanyId && userRole.id !== 1 && mountedRef.current){
            setMenuItems([]);
            setCompanyId(reduxCompanyId)
        }
    },[ownerDash, adminDash, importedCompanyId, reduxCompanyId, userRole]);

    useEffect(()=>{
        if(companyId || (!companyId && oldCompanyId !== companyId)) {
            setLoading(true);
            getMenuItemData();
        }
    },[companyId, oldCompanyId, getMenuItemData])

    const passState=(stuff)=>{
        if(!orderHasChanged) setOrderHasChanged(true);
        currentOrder.current = stuff;
    }

    const saveSortOrder=async()=>{
        setSuccess();
        setError();

        let currentItems = currentOrder.current;
        let newOrder = assignParentIds(currentItems, menuItems);

        //remove children
        for(let i = 0; i < currentItems.length; i++){
            delete currentItems[i].children;
            currentItems[i].parent_id = null;
        }
        //make one array
        let allItems = [...currentItems, ...newOrder];
        //replace "id" with "menu_item_id"
        for(let i = 0; i < allItems.length; i++){
            allItems[i].menu_item_id = allItems[i].id;
            delete allItems[i].id;
            allItems[i].company_id = companyId;
            allItems[i].module_id = allItems[i].wholeItem.module_id;
            allItems[i].text = allItems[i].wholeItem.text;
            allItems[i].icon = allItems[i].wholeItem.icon || "";
            delete allItems[i].wholeItem
        }
        let response;
        try{
            if(location.pathname.includes("default")) response = await Permissions.MenuItems.sort({sort:allItems});
            else response = await Permissions.MenuItems.editMany({menu_items: allItems})
            if(response.status === 200 && response.data){
                setSuccess(setSuccessToast('Menu Item order has been saved.'))
                setOrderHasChanged(false);
            }else{
                setError(setErrorCatcher(response.errors))
            }
        }catch(ex){
            console.error(ex);
        }
    }

    const revertToOriginal=()=>{
        setLoading(true);
        getMenuItemData(location.pathname.includes("default") ? "default" : "company");
    }

    const handleAllClose=(reset, newId=null)=>{
        setModalShow(false);
        if(showNewItem) setShowNewItem(false);
        if(showEditItem) setShowEditItem(false);
        if(showExternal) setShowExternal(false);
        if(showFolder) setShowFolder(false);
        if(showDelete) setShowDelete(false);
        if(showDisable) setShowDisable(false);
        if(newId) handleNewItem(newId);
        else if(reset){
            setLoading(true);
            setOrderHasChanged(false);
            getMenuItemData(location.pathname.includes("default") ? "default" : "company");
        }
        setClearSelected(true)
        setActiveItem(null);
    }

    const handleModalAction=(type, override=false)=>{
        currentType.current = type;
        if(!orderHasChanged || override){
            if(type==="NewItem" || type==="External" || type==="Folder") {
                setActiveItem(null);
                setClearSelected(true);
            }
            setShowConfimation(false);
            if(type==="NewItem") setShowNewItem(true);
            else if(type==="External") setShowExternal(true);
            else if(type==="Folder") setShowFolder(true);
            else if(type==="EditItem") setShowEditItem(true);
            else if(type==="Delete") setShowDelete(true);
            else if(type==="DisableItem")setShowDisable(true);
        }else if(orderHasChanged && !override){
            setShowConfimation(true);
        }
        setModalShow(true);
    }

    const handleDelete=(e)=>{
        if(e.target.value === "yes" && showDelete){
            deleteDefaultItem();
        }else if(e.target.value === "yes" && showDisable){
            disableCompanyItem();
        }
        else if(e.target.value === "no") handleAllClose(false);
    }

    const deleteDefaultItem=async()=>{
        setSuccess();
        setError();
        try{
            let response = await Permissions.MenuItems.delete({
                company_id: companyId ? companyId : null,
                module_id: activeItem.module_id
            })
            if(response.status===200 && response.data) {
                setSuccess(setSuccessToast("Menu Item has been Deleted successfully."))
                handleAllClose(true);
            }
            else if(response.errors) setError(setErrorCatcher(response.errors))
        }catch(ex){console.error(ex)}
    }

    const handleNewItem = async(newId)=>{
        let tempMenuItems = [...menuItems];
        //there is no + or - because the index for the tree has to start at 1. 
        // as such, if you have 100 items, the array index will be 99 but the tree index will be 100.
        //if we're adding a new item, it's the length,  
        let index = menuItems.length
        let response = await getSingleItem(null, newId);
        if(response.errors) {
            setError(setErrorCatcher(response.errors));
            getMenuItemData(location.pathname.includes("default") ? "default" : "company");
        } else if(response.data){
            setLoading(true);
            let newItem = response.data[0];
            // await setSortOrder(response.data[0].module_id)
            //saving data to the new item so it's fitted into the tree properly upon first add
            newItem.index=index;
            newItem.name=response.data[0].text;
            newItem.sort_order = highestSortOrder +1 + newItemRef.current;
            tempMenuItems.push(newItem);
            let sorted = adjustMenuItems(tempMenuItems, true);
            setMenuItems(sorted);
            newItemRef.current = newItemRef.current + 1;
            setLoading(false);
        }
    }

    const setSortOrder = async(moduleId)=>{
        try{
            await Permissions.MenuItems.edit({
                module_id: moduleId, 
                sort_order: highestSortOrder+1 + newItemRef.current,
                company_id: companyId ? companyId : null,
                text: activeItem.text
            })
        }
        catch(ex){console.error(ex)}
    }

    const disableCompanyItem=async()=>{
        try{
            let response = await Permissions.MenuItems.edit({
                company_id: location.pathname.includes("default") ? null : companyId,
                // module_id: activeItem.module_id,
                menu_item_id: activeItem.id,
                is_disabled: showDisabledItems ? 0 : 1,
                text: activeItem.text,
            })
            if(response.status===200 && response.data){
                setSuccess(setSuccessToast("Menu Item has been disabled successfully."));
                handleAllClose(true);
            }else if(response.errors) setError(setErrorCatcher(response.errors))
        }catch(ex) {console.error(ex)}
    }

    const handleClear=()=>{
        setClearSelected(true);
        setActiveItem(null);
    }

    const handleShowDisabled=()=>{
        setLoading(true);
        setShowDisabledItems(!showDisabledItems);
        setReset(true);
        setLoading(false);
    }

    //this is what goes to the side of the tree with all its controls and buttons
    const optionalDataArray = [
        <div className="text-center">
            <div className="btn-section">
                <h6>Handle Sort</h6>
                <Button 
                    className="command-btns"
                    onClick={saveSortOrder}
                    disabled={!orderHasChanged}
                >Save Menu Item Order</Button>
                
                <Button
                    className="command-btns"
                    onClick={handleShowDisabled}
                    disabled={disabledItems.length===0 || orderHasChanged}
                >
                    {disabledItems.length ===0 ? 
                        "No Disabled Items" :
                        <>
                            {showDisabledItems ? 
                                "View Active" 
                            : 
                                "View Disabled Items"
                            }
                        </> 
                    }
                </Button>
                
                <Button 
                    className="command-btns"
                    variant="danger" 
                    onClick={revertToOriginal}
                    disabled={!orderHasChanged}
                >Cancel Reorder</Button>
            </div>
            <fieldset disabled={showDisabledItems}>
                <div className="btn-section">
                    <h6>Add New Menu Items</h6>
                    
                    <Button 
                        className="command-btns" 
                        onClick={()=>handleModalAction("NewItem")} 
                    >New Item To Module </Button>
                    
                    <Button 
                        className="command-btns" 
                        onClick={()=>handleModalAction("External")}
                    >New External Link</Button>
                    
                    <Button 
                        className="command-btns" 
                        onClick={()=>handleModalAction("Folder")}
                    >New Folder</Button>
                </div>
            </fieldset>
            <div className="btn-section">
                <h6> Selected Menu Item</h6>
                <Button 
                    className="command-btns"
                    onClick={handleClear}
                    disabled={!activeItem}
                >Clear Selected Item</Button>

                <Button
                    className="command-btns"
                    onClick={()=>handleModalAction("EditItem")}
                    disabled={!activeItem}
                >Edit Menu Item</Button>
                
                <Button
                    className="command-btns"
                    onClick={()=>handleModalAction("DisableItem")}
                    disabled={!activeItem}
                >{showDisabledItems ?
                    "Enable Menu Item"
                    :
                    "Disable Menu Item"
                }</Button>
                
                {location.pathname.includes("default") && 
                    <Button
                        className="command-btns"
                        onClick={()=>handleModalAction("Delete")}
                        disabled={!activeItem}
                        variant="danger"
                    >Delete Menu Item</Button>
                }
            </div>
        </div>
    ] 

    // create the breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" }
    ];

    // if admin dashboard, add the admin dashboard link to the breadcrumbs
    if (adminDash.current) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" })
    }
    // if owner dashboard, add the owner dashboard link to the breadcrumbs
    if (ownerDash.current) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/owner" }, text: "Permission Dashboard" })
    }
    // add the current page to the breadcrumbs
    breadcrumbs.push({ text: "Menu Settings" });

    return (
        <Container className="menu-settings-wrapper" fluid>
            {(!importedCompanyId) && //there's only an imported company ID when this is a child component and doesn't need its own header
                <SubHeader items={breadcrumbs} />
            }
            <Card className="modal-card">
                {error}
                {success}
                <h4 className="section-title">
                    {location.pathname.includes("default") ? 
                        "Default Menu Settings" 
                    : 
                        <>
                            {adminDash.current ?
                                `${adminDash.current.selectedCompany[0]?.name ? adminDash.current.selectedCompany[0].name : ""} Menu Settings`
                            :
                                `${companyName ? companyName : ""} Menu Settings`
                            } 
                        </>
                    }
                </h4>
                {!importedCompanyId && location?.pathname?.includes("default") &&
                    <p>
                        Changes made here will show for all companies unless they have set overrides.  To set an override for a company, you must be logged into said company and edit from <a href="/p/menu">their menu page</a>.
                    </p>
                }
                <p>
                    Here, in addition to adjusting the items in menus, you can sort and reorganize the menu items users will see in accordance with their permission levels.  Folders are levels of menu orginization.  Any module can be a folder, but it becomes a folder, not a clickable menu item.  Items already dictated as menu folders will have an arrow to the left of their name. 
                </p>
                {loading ?
                    <SkeletonTheme color="#e0e0e0">
                        <div className="mt-3 text-center">
                            <Skeleton height={28} width={200}/>
                            <Skeleton height={16} count={4} />
                        </div>
                    </SkeletonTheme>
                :
                    <div>
                        {showDisabledItems ? 
                            <SortOrderTree 
                                data={disabledItems}
                                passState={passState}
                                passActive={(item)=>setActiveItem(item)}
                                clearSelected={clearSelected}
                                setClearSelected={setClearSelected}
                                treeClearBtn={false}
                                reset={reset}
                                setReset={setReset}
                                treeSettings={{
                                    canDragAndDrop: false,
                                    canDropOnFolder: false,
                                    canReorderItems: false,
                                    canDropOnNonFolder: false,
                                    canSearchByStartingTyping: true,
                                    treeId: "disabled-menu-items-tree"
                                }}
                                optionalDetails={{
                                    title: "",
                                    type: "html",
                                    requiresActive: false,
                                    data: optionalDataArray
                                }}
                            />
                        :
                            <SortOrderTree 
                                data={menuItems}
                                passState={passState}
                                reset={reset}
                                setReset={setReset}
                                clearSelected={clearSelected}
                                setClearSelected={setClearSelected}
                                treeClearBtn={false}
                                passActive={(item)=>setActiveItem(item)}
                                treeSettings={{
                                    canDropOnFolder: true,
                                    canDropOnNonFolder: false,
                                    canReorderItems: true,
                                    canDragAndDrop: true,
                                    canSearchByStartingTyping: true,
                                    treeId: "sort-menu-tree"
                                }}
                                optionalDetails={{
                                    title: "",
                                    type:"html",
                                    requiresActive: false,
                                    data: optionalDataArray    
                                }}
                            />
                        }
                    </div>
                }
            </Card>
            <Modal dialogClassName="menu-item-modal" show={modalShow} onHide={()=>handleAllClose(false)} >
                <Modal.Header closeButton>
                    {showNewItem && 
                        <p>
                            New Menu Item
                        </p>
                    }
                </Modal.Header>
                <Modal.Body>
                    {showNewItem &&
                        <NewMenuItem 
                            modal={true} 
                            editItem={showEditItem ? activeItem : null}
                            onClose={handleAllClose}
                            highestSortOrder={highestSortOrder}
                            companyId={companyId}
                        />
                    }
                    {showEditItem && (activeItem?.module?.module_type_id !==3 && activeItem?.module?.module_type_id !== 4) && 
                        <NewMenuItem 
                            modal={true} 
                            editItem={showEditItem ? activeItem : null}
                            onClose={handleAllClose}
                            companyId={companyId}
                        />
                    }
                    {showEditItem && activeItem.module.module_type_id===3 &&
                        <ExternalMenuItem 
                            setError={setError}
                            setSuccess={setSuccess}
                            onClose={handleAllClose}
                            activeItem={activeItem}
                            companyId={companyId}
                        />
                    }
                    {showExternal &&
                        <ExternalMenuItem 
                            companyId={companyId}
                            onClose={handleAllClose}
                            setError={setError}
                            setSuccess={setSuccess}
                        />
                    }
                    {(showDelete || showDisable) &&
                        <ConfirmDelete 
                            activeItem={activeItem}
                            disable={showDisable ? true : false}
                            handleDelete={handleDelete}
                            enable={showDisabledItems ? true : false}
                        />
                    }
                    {showEditItem && activeItem.module.module_type_id === 4 &&
                        <AddFolder
                            companyId={companyId}
                            onClose={handleAllClose}
                            setSuccess={setSuccess}
                            setError={setError}
                            activeItem={activeItem}
                        />
                    }
                    {showFolder &&
                        <AddFolder
                            companyId={companyId}
                            onClose={handleAllClose}
                            setSuccess={setSuccess}
                            setError={setError}
                        />
                    }
                    {showConfirmation ?
                        <div className="text-center">
                            <p>
                                You've already made changes to the sort order of your tree! 
                                If you add new data now, what you've sorted will be lost.  
                                Would you like to continue or cancel?
                            </p>
                            <p className="menu-settings-btn-row">
                                <Button variant="danger" onClick={()=>handleAllClose(false, null)}>Cancel</Button>
                                <Button onClick={()=>handleModalAction(currentType.current, true)}>Continue</Button>
                            </p>
                        </div>
                    :
                        ""
                    }
                </Modal.Body>
            </Modal>
        </Container>
    )
}
