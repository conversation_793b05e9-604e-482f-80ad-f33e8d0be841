import React, { useState, useEffect, useRef, useCallback } from 'react'
import { <PERSON><PERSON>, Card, Container } from 'react-bootstrap' 
import { Link } from 'react-router-dom'
import SubHeader from '../../../components/common/SubHeader'
import DefaultFeatures from './DefaultFeatures'
import ViewAssignFeatures from './ViewAssignFeatures'
import './CompanyFeatures.scss'

export const CompanyFeatures = () => {

    const mountedRef = useRef(false);

    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current = false
    },[]);

    return (
        <Container fluid>
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Company Permissions" }
            ]} />
            <Card className="content-card company-features">
                <ViewAssignFeatures />
                <DefaultFeatures /> 
            </Card>
        </Container>
    )
}
