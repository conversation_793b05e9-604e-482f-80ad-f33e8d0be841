import React, { useState, useEffect, useRef, useCallback } from 'react'
import usePrevious from '../../../../components/common/CustomHooks';

//src\containers\MediaManager\MediaManagerComponents\MediaPreview.js

const AudioMedia = ({activeMedia, upload}) => {

    const mountedRef = useRef(false);
    const [audio, setAudio] = useState(null);
    const [editAudio, setEditAudio] = useState(false);
    const previousAudio = usePrevious(audio);

    useEffect(()=>{
        mountedRef.current = true;

        return ()=>{
            mountedRef.current = false;
            setAudio(null);
        }
    },[]);

    useEffect(()=>{
        if(mountedRef.current && activeMedia && audio !== previousAudio){
            setAudio(activeMedia?.url);
        }
    },[audio, previousAudio, activeMedia])

    const sendHandler = useCallback(data=>{
        upload(data);
    },[upload]);

    const handleAudio = (files) => {
        console.log(files[0]);
        let newAudio = new FormData();
        newAudio.append("audio", files[0], files[0].name);
        sendHandler(newAudio);
    }

  return (
    <div className="audio-media">
        {audio && 
            <>
                <p>
                    <audio
                        controls
                        src={activeMedia?.url}
                        />
                </p>
                <p>
                    <button onClick={()=>setEditAudio(!editAudio)}>
                        Change 
                        {" "}
                        <i className="far fa-volume" />
                    </button>
                </p>
            </>
        }
        {(!audio || editAudio) &&
            <p>
                <label 
                    className="fake-btn cp"
                    htmlFor="audio-upload"
                >
                    Upload Audio
                    <input 
                        className="hidden"
                        type="file"
                        id="audio-upload"
                        name="audio"
                        accept="audio/*"
                        onChange={(e)=>handleAudio(e.target.files)}
                    />
                </label>
            </p>
        }
    </div>
  )
}

export default AudioMedia