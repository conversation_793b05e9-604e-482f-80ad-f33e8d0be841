import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Modal } from 'react-bootstrap';
import { Link, useParams } from 'react-router-dom';
import { format } from 'date-fns';

import SubHeader from '../../../components/common/SubHeader';
import Registers from '../../../api/Registers';
import NewCatAvailability from './Components/NewCatAvailability';
import DefaultCategory from './Components/DefaultCategory';
import EachDay from './Components/EachDay';

import styles from './RegisterCategoryAvailability.module.scss';
import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';

const categoryAvailabilityDefault={
    "default": "",
    "days":[
        {
            "date": "Sunday",
            "categories": []
        },
        {
            "date": "Monday",
            "categories": []
        },
        {
            "date": "Tuesday",
            "categories": []
        },
        {
            "date": "Wednesday",
            "categories": []
        },
        {
            "date": "Thursday",
            "categories": []
        },
        {
            "date": "Friday",
            "categories": []
        },
        {
            "date": "Saturday",
            "categories": []
        },
    ],
    "extra_dates":[]
}
const defaultAvailable = {
    days: [
        {
           "date":"Sunday",
           "start": null,
           "end": null,
           "closed": false
        },
        {
            "date":"Monday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Tuesday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Wednesday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Thursday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Friday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Saturday",
            "start": null,
            "end": null,
            "closed": false
        }
    ],
    extra_closed: [],
    timezone: "America/New_York"
}

const dateNumbers={
    "Sunday": 0,
    "Monday":1,
    "Tuesday": 2,
    "Wednesday": 3,
    "Thursday": 4,
    "Friday": 5,
    "Saturday": 6
}

const createTempIds=(data)=>{
    let number = 0;
    data.forEach((day)=>{
        day.categories.forEach((item)=>{
            item.tempId=number;
            number++
        })
    })
}

export const RegisterCategoryAvailability=()=>{

    const id=useParams();

    const [ registerDefinition, setRegisterDefinition ] =useState();
    const [ categoryAvailability, setCategoryAvailability ]=useState();
    const [ defaultCategory, setDefaultCategory ] = useState();
    const [ show, setShow ]=useState(null)
    const [ error, setError ]=useState(null);
    const [ success, setSuccess]=useState(null);
    const [ loading, setLoading ]=useState(true);
    const [ activeEdit, setActiveEdit ]=useState(null);
    const [ hasData, setHasData ]=useState(false);

    useEffect(()=>{
        const getRegister=async()=>{
            setLoading(true);
            setHasData(false);
            try{
                let response = await Registers.get(id);
                if(response.status === 200){
                    setRegisterDefinition(response?.data[0]); //this will act like a master if need be;
                    let cats = response?.data[0]?.register_definition.category_availability;
                    if(!cats) setCategoryAvailability(categoryAvailabilityDefault)
                    else if(cats){
                        setCategoryAvailability(cats);
                        createTempIds([...cats?.days, ...cats?.extra_dates]);
                        if(cats?.default) setDefaultCategory(cats?.default);
                        else setDefaultCategory(response?.data[0]?.register_definition?.category_id)
                        setHasData(true)
                    } 
                }
            }catch(ex){
                console.error(ex)
            }
            setLoading(false);
        }
        
        if(id){
            setLoading(true);
            setError(null);
            setSuccess(null)
            getRegister();
        } 
        
        return()=>{
            setLoading(true);
            setError(null);
            setSuccess(null);
            setRegisterDefinition(null);
            setHasData(false)
        }

    },[id]);


    const handleRemoveClick=(each, day)=>{
        let dataCopy = Object.create(categoryAvailability);
        let match
        if(day?.date?.includes("day")){ //day of the week will be string that includes the word "day"
            match = dataCopy.days.find((eachDay)=> eachDay.date === day.date)
        }else{
            match = dataCopy.extra_dates.find((eachDay)=>eachDay.date===day.date)
        }
        let index =match.categories.findIndex((cat)=>cat.tempId === each.tempId)
        match.categories.splice(index, 1)
        setCategoryAvailability(dataCopy)
        
    }

    const createNew=(type, data)=>{
        let dataCopy = Object.create(categoryAvailability);
        if(type==="add"){
            if(data.dayOfWeek){ //if it's new, day of week - just add it to the category
                let match = dataCopy.days[data.dayOfWeek];
                deleteDays(data);
                match.categories.push(data)
            }else if(data.specificDate){ //find the specific day, if it exists, create it if it doesn't
                let simpleDate = format(new Date(data.specificDate), 'MM/dd');
                let match = dataCopy.extra_dates.find((each)=>each.date === simpleDate);
                deleteDays(data);
                if(match){
                    match.categories.push(data)
                }else{
                    let newObject={
                        date:simpleDate,
                        categories:[data]
                    }
                    dataCopy.extra_dates.push(newObject);
                }
            }
        }
        if(type==="edit"){
            if(data?.day?.dayOfWeek){
                let match = dataCopy.days[dateNumbers[data?.day?.dayOfWeek]];
                let index = match.categories.findIndex((each)=>each.tempId === data?.item?.tempId)
                match.categories[index] = data?.item
            }else if(data?.day?.specificDate){
                let simpleDate = format(new Date(data?.day?.specificDate), 'MM/dd');
                let match = dataCopy.extra_dates.find((each)=>each.date === simpleDate);
                let index = match.categories.findIndex(each=> each.tempId === data.item.tempId);
                match.categories[index] = data.item; //don't need to delete days first since it's a different part of the object
            }
        }
        setCategoryAvailability(dataCopy)
        handleHide();
    }

    const deleteDays=(data)=>{
        delete data.dayOfWeek;
        delete data.specificDate;
    }

    const handleEditClick=(data)=>{
            setActiveEdit(data);
            setShow("new");
    }

    const handleHide=()=>{
        setShow(null);
        setActiveEdit(null);
    }

    const handleDefault=(selection)=>{
        setDefaultCategory({id: selection[0]?.id, name: selection[0]?.name, parent: selection[0]?.parent});
    }

    const handleSaves=async(type)=>{
        setSuccess(null);
        setError(null)
        let registerDefCopy = {...registerDefinition};
        let message = "";
        let catAvailability = registerDefCopy.register_definition?.category_availability ? registerDefCopy.register_definition.category_availability : {};
        if(type==="default"){
            catAvailability.default = defaultCategory;
            message="the default category."
        }else if(type==="saveAll"){
            catAvailability.extra_dates = categoryAvailability?.extra_dates;
            catAvailability.days = categoryAvailability?.days;
            message = "the register categories and days."
        }
        registerDefCopy.register_definition.category_availability = catAvailability;
        if(registerDefinition.register_definition?.hasOwnProperty("change_id")) registerDefCopy.register_definition.change_id = ++registerDefCopy.register_definition.change_id 
        else registerDefCopy.register_definition.change_id = 1;

        //this is a new error from the backend and it adds these fields when it creates a register.  If they're null, remove them
        if(registerDefCopy.hasOwnProperty("register_group_id") && !registerDefCopy.register_group_id) delete registerDefCopy.register_group_id;
        if(registerDefCopy.hasOwnProperty("register_group_name") && !registerDefCopy.register_name) delete registerDefCopy.register_group_name;
        
        try{
            let response = await Registers.edit(registerDefCopy)
            if(response.status === 200) {
                setSuccess(setSuccessToast("You have successfully edited " + message ))
                setShow(null); //if modal is open, it will close
            }
            else {
                setError(setErrorCatcher("There was an issue changing " + message));
                if(type==="default") setDefaultCategory(previous => previous);
            }
        }catch(ex){
            console.error(ex)
            if(type==="default") setDefaultCategory(previous => previous)
        }
    }

    const recursiveParentDisplay=(item, first=false)=>{
        return(
            <>
                {item?.parent &&
                    recursiveParentDisplay(item?.parent)
                }
                <span>{item?.name}</span>
                {first ?
                    <></>
                    :
                    <span>{" "}{'>'} </span>
                }
            </>
        )
    }

    return(
        <Container fluid>
            <SubHeader 
                items={[
                    {linkAs: Link, linkProps: {to:"/p/home"}, text: "Home" },
                    {linkAs: Link, linkProps: {to: "/p/registers"}, text: "Register Dashboard"},
                    {text: "Categories by Availability"}
                ]}
            />
            <Card className="content-card">
                {success}
                {error}
                <h4 className="section-title">
                    Categories By Availability
                </h4>
                {loading ? 
                    <>
                    </>
                :
                    <>
                        <div className={styles["button-div"]}>
                            <Button onClick={()=>setShow("default")}>
                                Edit Default
                            </Button>
                            <Button onClick={()=>setShow("new")}>
                                Add New
                            </Button>
                        </div>
                        <div>
                            <div>
                                <div>
                                    <span className={styles["sub-title"]}>
                                        Default Category: 
                                    </span>
                                    <br />
                                    (This is the category that will be active when no other category is selected)
                                </div>
                                <strong>
                                    {/* {defaultCategory?.parent ? <span>{defaultCategory?.parent?.name} {'>'} {defaultCategory?.name}</span> : <span> {defaultCategory?.name}</span>} */}
                                    {recursiveParentDisplay(defaultCategory, true)}
                                </strong>
                                <br />
                                <hr />
                            </div>
                        </div>
                        <div className={styles["register-category-wrapper"]}> 
                            <div className={styles}>
                                <div className={styles["each-row"]}>
                                    <p className={styles["sub-title"]}>
                                        Categories by Day of the Week
                                    </p>
                                    <div className={styles["each-day-div"]}>
                                    {registerDefinition?.register_definition && categoryAvailability?.days?.map((day, i)=>(
                                            <EachDay 
                                                key={`each-day-${i}`}
                                                day={day} 
                                                hours={registerDefinition?.register_definition?.availability?.days?.filter(match => match.date === day.date)[0]} 
                                                removeClick={handleRemoveClick}    
                                                handleEdit={handleEditClick}
                                            />
                                        ))}
                                    </div>
                                </div>
                            </div>
                            {registerDefinition?.register_definition && categoryAvailability?.extra_dates?.length > 0 &&
                                <div className={styles["each-row"]}>
                                    <div>
                                        <hr />
                                        <p className={styles["sub-title"]}>
                                            Extra Categories by Date
                                        </p>
                                        <div className={styles["each-day-div"]}>
                                            {categoryAvailability?.extra_dates?.map((date, i)=>(
                                                <EachDay
                                                    key={`each-extra-day-${i}`}
                                                    day={date}
                                                    format={"date"}
                                                    hours={registerDefinition?.register_definition?.availability?.days}
                                                    removeClick={handleRemoveClick}
                                                    handleEdit={handleEditClick}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            }
                            <div>
                                <Button onClick={()=>handleSaves("saveAll")}>Save</Button>
                            </div>
                        </div>
                </>
                }
                
                
                <Modal dialogClassName={styles["modal-override"]} show={show === "default" ? true : false} onHide={handleHide}>
                    <Modal.Header closeButton>
                        <Modal.Title>
                            Default Category
                        </Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <DefaultCategory 
                            setDefaultCategory={handleDefault} 
                            defaultCategory={defaultCategory?.id}
                            handleCancel={()=>setShow(null)}
                            handleSaveDefault={()=>{handleSaves("default")}}
                        />
                    </Modal.Body>
                </Modal>

                <Modal dialogClassName={styles["modal-override"]} show={show==="new" ? true : false} onHide={handleHide} size="xl" >
                    <Modal.Header closeButton />
                    <Modal.Title>
                        New Availability
                    </Modal.Title>
                    <Modal.Body  >
                        <NewCatAvailability 
                            dateNumbers={dateNumbers} 
                            registerHours={registerDefinition?.register_definition?.availability?.days || defaultAvailable.days} //need to include the default for registers that don't have any availability set
                            handleNew={createNew} 
                            editItem={activeEdit ? activeEdit : null}
                        />
                    </Modal.Body>
                </Modal>
            </Card>
        </Container>
    )
}
