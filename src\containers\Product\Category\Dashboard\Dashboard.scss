@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/themes.scss';

.category-dash-wrapper{
    background-color: $card-background-color;
    margin: 1rem;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    .title{
        margin-left: 1rem;
    }
    .tree-info{
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        @media (max-width: 1050px){
            flex-direction: column;
            align-items: center;
        }
        @media (max-width: 450px){
            align-items: flex-start;
        }
    }
    .cat-dash-tree-wrapper, .new-cat-form{
        .card{
            width: 600px;
            border-radius: $card-border-radius;
            border: $card-standout-border;
            background-color: $card-background-color;
            margin: 1rem;
        }
        @media (min-width: 1351px) and (max-width: 1560px){
            .card{
                width: 500px;
            }
        }
        @media (min-width: 401px) and (max-width: 1350px){
            .card{
                width: 400px;
            }
        };
        @media (max-width: 400px){
            .card{
                width: 300px;
            }
        }
    }
    .category-dash-header{
        display: flex;
        justify-content: space-between;
        margin: 1rem;
    }
    .cat-dash-header-btns{
        display: flex;
        flex-direction: row;
        @media (max-width: 450px){
            flex-direction: column;
            align-items: center;
        }
    }
    .cat-dash-info-wrapper{
        margin-left: .5rem;
        margin-right: 1rem;
    }
    .micro-list-wrapper{
        display: flex;
        justify-content: center;
        margin: 1.5rem;
    }
    div.content-card{
        @media (max-width: 450px){
            margin: 0;
        }
    }
}