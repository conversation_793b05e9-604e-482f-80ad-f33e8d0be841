@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes.scss';

.add-new-field {
    display: flex;
    width: 30px;
    height: 30px;
    align-items: center;
    justify-content: center;
}

.add-new-field i {
    margin: 0 !important;
}

.save-col {
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.w-100.btn-primary {
    justify-self: center;
}

.remove-field {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
}

.new-option, .remove-option {
    display: flex;
    width: 20px;
    height: 20px;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.remove-option {
    transform: translateY(35px);
}

.new-option {
    margin: 10px;
}

.add-new-field:hover,
.new-option:hover,
.remove-field:hover,
.remove-option:hover {
    cursor: pointer;
}

.field-card {
    background-color: $form-control-background-color;
}