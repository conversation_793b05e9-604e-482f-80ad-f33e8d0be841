import React, { useState, useEffect, useCallback } from 'react';
import { Form, Button, Modal } from 'react-bootstrap';

import FeatureTypeahead from '../../../components/Typeahead/FeatureTypeahead';
import { FeaturePermissionGrid } from './FeaturePermissionGrid';

import '../Permissions.scss'

const ROLE_COMPANY_OWNER = 3;
const ROLES = [
    { "id": 3, "name": "Company Owner" },
    { "id": 4, "name": "Admin" },
    { "id": 5, "name": "Staff" },
    { "id": 6, "name": "Non-Staff Manager" },
    { "id": 7, "name": "Patron" },
];

/*    This component is used to display the default permissions for all company if companyId is null
 *    or for a specific company if companyId is not null
 */

export const ModulePermissions = ({features, setError, permissionLevels=null, company=null, user=null, group=null, ...props}) => {

    const [selectedFeature, setSelectedFeature] = useState(null);
    const [nextFeature, setNextFeature] = useState(null);
    const [needsSave, setNeedsSave] = useState(false);
    const [showModalSave, setShowModalSave] = useState(false);
    const [triggerSave, setTriggerSave] = useState(0);
    const [isSaving, setIsSaving] = useState(0); // 0=not saving, 1=saving, 2=save complete
    const [columnName, setColumnName] = useState(null);

    useEffect(() => {
        // if nextFeatureId && isSaving, then we need to wait until the save is complete before selecting the next feature
        if (isSaving===2) {
            if(nextFeature) {
                setSelectedFeature(nextFeature);
                setNextFeature(null);
            }
            setIsSaving(0);
        }
    },[isSaving, nextFeature]);

    const onSubmitModal = (shouldTriggerSave) => {
        setShowModalSave(false);
        if (shouldTriggerSave) {
            setTriggerSave(prev => prev+1);
            setIsSaving(1);
        } else {
            setIsSaving(2);
        }
        setNeedsSave(false);
    }

    const selectNewFeature = useCallback((selected) => {
        // first check if the current permissions need to be saved
        if (selected) {
            if (needsSave) {
                setNextFeature(selected);
                setShowModalSave(true);
            } else {
                setNeedsSave(false);
                setSelectedFeature(selected);
            }
        } else {
            setSelectedFeature(null);
        }
    },[needsSave]);
    
    useEffect(() => {
        if (user?.id) {
            setColumnName("user");
        } else if (group?.id) {
            setColumnName("group");
        }
    },[user, group]);

    return (
        <div>
            <h4 className="section-title">
                {
                user ?
                    <>Assign Permissions for User {user.id || "userID"} {user.first_name+" "+user.last_name}</>
                :
                group ?
                    <>Assign Permissions for Group {group.id || "groupID"} {group.name}</>
                :
                company?.id ?
                    <>Assign Permissions for {"Company "+company.id+" "+company.name}</>
                :
                    <>Assign Default Permissions For All Companies</>
                }
            </h4>

            <FeaturesSelect features={features} onSelect={selectNewFeature} />

            {selectedFeature &&
                <div className="permissions-container">
                    <FeaturePermissionGrid feature={selectedFeature}
                        roles={columnName ? [{id: columnName, name: ''}] : ROLES}
                        companyId={company?.id || null}
                        userId={user?.id || null}
                        groupId={group?.id || null}
                        setError={setError}
                        needsSave={needsSave}
                        setNeedsSave={setNeedsSave}
                        triggerSave={triggerSave}
                        setTriggerSave={setTriggerSave}
                        setIsSaving={setIsSaving}
                        roleCompanyOwner={ROLE_COMPANY_OWNER}
                        hiddenRoles={company?.id ? [ROLE_COMPANY_OWNER] : []} // hide company owner role if the view is for a company
                        allPermissionLevels={permissionLevels}
                    />

                </div>
            }


            <Modal show={showModalSave} className="permissions-modal" backdrop="static" size="lg" onClose={() => setShowModalSave(false)}>
                <Modal.Body>
                    <div className="mb-3 mt-2">You have unsaved changes. Would you like to save them?</div>
                    <div>
                        <Button variant="secondary" onClick={() => onSubmitModal(false)}>No</Button>
                        <Button variant="primary" onClick={() => onSubmitModal(true)}>Yes</Button>
                    </div>
                </Modal.Body>
            </Modal>
        </div>
    )
}

const FeaturesSelect = ({features, onSelect}) => {

    const onSelectFeature = (e) => {
        let selectedFeature = features.find(feature => feature.id===parseInt(e.target.value));
        if (selectedFeature) {
            onSelect(selectedFeature);
        }
    }

    return (
        <Form.Group className="feature-select">
            <Form.Label>Select a Feature</Form.Label>
            {!features ?
                <>loading...</>
            :
                <Form.Control
                    aria-label="Select a Feature"
                    size="lg"
                    as="select"
                    onChange={onSelectFeature}
                >
                    <option></option>
                    {features.map(feature => (
                        <option key={`feature-option-${feature.id}`} value={feature.id}>{feature.name}</option>
                    ))}
                </Form.Control>
            }
       </Form.Group>
    )
}