import React, { useEffect, useState, useRef, useCallback } from 'react'
import { Container, Card, Button, Modal } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { usePara<PERSON>, useHistory, Link } from 'react-router-dom';
import SubHeader from '../../../../components/common/SubHeader';
import Permissions from '../../../../api/Permissions';

import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import Toast from '../../../../components/Toast';
import { CompanyTypeahead } from '../../../../components/Typeahead/CompanyTypeahead';
import EndPointsTypeahead from '../../../../components/Typeahead/EndPointsTypeahead';
import { getFeatures, getModules, getSingleModule, getModuleTypes, getPermissions } from '../../PermissionsUtils/PermissionUtils';
import { ModulePages } from './ModulePages';
import CreateEndpoints from '../../EndPoints/CreateEndpoints';
import Tutorials from '../../../../components/Tutorials';
import RedirectModal from '../../../../components/common/RedirectModal';
import Tooltip from '../../../../components/common/Tooltip';

import '../ModuleDashboard/ModuleDashboard.scss';
import CharacterCounterInput from '../../../../components/common/CharacterCounter/CharacterCounterInput';
import ModuleTypeahead from '../../../../components/Typeahead/ModuleTypeahead';

export const CreatEditModuleForm = ({create, ...props}) => {

    const mountedRef = useRef(false);
    const params = useParams();
    const history = useHistory();
    const editId = params.id;

    const [ features, setFeatures ] = useState([]);
    const [ modules, setModules ] = useState([]);
    const [ activeModule, setActiveModule ] = useState(null);
    const [ selectedType, setSelectedType ] = useState(0);
    const [ moduleTypes, setModuleTypes ] = useState([]);
    const [ availablePermissions, setAvailablePermissions ]=useState([]);
    const [ selectedEndpoints, setSelectedEndpoints ] = useState([]);
    const [ selectedCompany, setSelectedCompany ] = useState([]);
    const [ selectedPermissions, setSelectedPermissions ]= useState([]);
    const [ selectedFeatureId, setSelectedFeatureId ]=useState("0")
    const [ firstLoad, setFirstLoad ]=useState(null)
    const [ loading, setLoading ]=useState(true);
    const [ error, setError ] =useState(null);
    const [ success, setSuccess ] =useState(null);
    const [ showCreateEndpoint, setShowCreateEndpoint ] =useState(false);
    const [ newOrDashShow, setNewOrDashShow ]=useState(false);
    const [ showLengthError, setShowLengthError ]=useState(false);
    const adminDash = JSON.parse(localStorage.getItem("adminDash"))

    const passSelection=useCallback((selection)=>{
        setSelectedEndpoints(selection?.map((selected)=>selected.id));
    },[])

    useEffect(()=>{
        mountedRef.current = true

        const getFeaturesGet=async()=>{
            let featureResponse = await getFeatures()
            if(featureResponse.data) setFeatures(featureResponse.data)
            else setError(<ErrorCatcher error={featureResponse.errors} />)
        }

        const getModulesGet=async()=>{
            let moduleResponse = await getModules();
            if(moduleResponse) setModules(moduleResponse.data);
            else setError(<ErrorCatcher error={moduleResponse.errors} />)
        }

        const getModuleTypesGet=async()=>{
            let typeResponse = await getModuleTypes();
            if(typeResponse) setModuleTypes(typeResponse.data);
            else setError(<ErrorCatcher error={typeResponse.errors} />)
        }

        const getPermissionsGet=async()=>{
            let permissionResponse = await getPermissions();
            if(permissionResponse) setAvailablePermissions(permissionResponse.data);
            else setError(<ErrorCatcher error={permissionResponse.errors} />)
        }
        

        if(mountedRef.current){
            getFeaturesGet();
            getModulesGet();
            getModuleTypesGet();
            getPermissionsGet();
        }
        
        return ()=> mountedRef.current = false
    },[editId]);

    useEffect(()=>{
        const getSingleModuleGet=async()=>{
            let moduleResponse = await getSingleModule(editId);
            if(moduleResponse.data && mountedRef.current) {
                setActiveModule(moduleResponse.data);
                //set for dropdowns and typeheads for edits
                setSelectedType(moduleResponse.data.module_type_id);
                setSelectedEndpoints(moduleResponse.data.endpoints?.map(endpoint=>endpoint.id));
                setSelectedCompany(moduleResponse.data?.company_id);
                setSelectedPermissions(moduleResponse.data.permission_levels);
                setSelectedFeatureId(moduleResponse.data?.feature?.id);
                setLoading(false);

                return moduleResponse.data.permission_levels
            }
            else {
                setError(<ErrorCatcher error={moduleResponse.errors} />)

                return null
            }
        }

        if(editId && mountedRef.current) {
            getSingleModuleGet().then((permissionLevels)=>{
                if(permissionLevels){
                    permissionLevels.forEach((permission)=>{
                        document.getElementById(`permission-${permission}`).checked = true
                    })
                }
            })
        }
        else setLoading(false);

    },[editId]);

//#region functions 

    const submitModule = async (e) =>{
        e.preventDefault();
        setError(null);
        setSuccess(null);

        let formDataObj = createDataObject(e.target)

        let stopProceed = checkLengths(formDataObj);

        if(!stopProceed){
            let response
            try{
                if(!editId) response = await Permissions.Modules.create(formDataObj)
                else if(editId) {
                    formDataObj.id =editId;
                    response = await Permissions.Modules.update(formDataObj)
                }
                if(response.status===200){
                    document.getElementById('module-form').reset();
                    setSuccess(<Toast>Module Created Successfully</Toast>);
                    setNewOrDashShow(true)
                }else{
                    setError(<ErrorCatcher error={response.errors} />)
                }
            }catch(ex){
                console.error(ex)
            }
        }
    }

    const createDataObject=(data)=>{
        const formData = new FormData(data);
        const formDataObj = Object.fromEntries(formData.entries());
        if(selectedFeatureId === "0") formDataObj.feature_id=null;
        else formDataObj.feature_id=selectedFeatureId;
        formDataObj.endpoints =  selectedEndpoints;
        if(formDataObj.url) {
            let url = formDataObj.url.replace(/ /g, '');
            formDataObj.url = url;
        }
        if(formDataObj.component_url) {
            let component_url = formDataObj.component_url.replace(/ /g, '');
            formDataObj.component_url = component_url;
        } 
        formDataObj.permission_levels = selectedPermissions;
        if(selectedCompany) formDataObj.company_id = selectedCompany;
        else formDataObj.company_id = null;
        let menu_item = handleFormatMenuItem(formDataObj);
        if(menu_item) formDataObj.default_menu_item = menu_item;

        return formDataObj
    }

    const passAddition=(addition)=>{
        setFirstLoad(true)
        setSelectedEndpoints([...selectedEndpoints, ...addition]);
    }

    const handleFormatMenuItem=(data)=>{
        let parentId = data.parent_id ? parseInt(data.parent_id) : null;
        let text = data.text ? data.text : null;
        let icon = data.icon ? data.icon : null;
        let menuItem={}
        if(parentId) {
            menuItem.parent_id=parentId;
            delete data.parent_id
        }
        if(text) {
            menuItem.text=text;
            delete data.text
        }
        if(icon) {
            menuItem.icon=icon;
            delete data.icon
        }
        if(!menuItem.parent_id && !menuItem.text && !menuItem.icon) return null;
        else return menuItem
    }

    const handleHide=(success)=>{
        setShowCreateEndpoint(false);
        if(success) setSuccess(<Toast>Endpoint(s) Created Successfully</Toast>)
        setFirstLoad(false)
    }

    const handleChecked=(e)=>{
        if(e.target.checked){
            let temp=[...selectedPermissions];
            temp.push(+e.target.value);
            setSelectedPermissions(temp);
        }
        else if(!e.target.checked){
            let temp=[...selectedPermissions];
            temp.splice(temp.indexOf(e.target.value),1);
            setSelectedPermissions(temp);
        }
    }

    const checkLengths=(dataObject)=>{
        let lengthExceeded = false;
        //if any one of these things is too long, we'll halt creation and display error
        if(dataObject?.name?.length > 45) lengthExceeded = true;
        else if(dataObject?.url?.length > 256) lengthExceeded = true;
        else if(dataObject?.description?.length > 45) lengthExceeded = true;
        else if(dataObject?.default_menu_item?.text?.length > 45) lengthExceeded = true;
        else if(dataObject?.icon?.length > 45) lengthExceeded = true;
        if(lengthExceeded){
            setShowLengthError(true);
            return true;
        }
        else {
            setShowLengthError(false);
            return false;
        }
    }

    const handleCopy = (selection) =>{
        if(selection){
            setSelectedType(selection?.module_type_id);
            setSelectedEndpoints(selection?.endpoints?.map((endpoint)=>endpoint?.id));
            setSelectedFeatureId(selection?.feature?.id);
            setFirstLoad(true);
        }
    }

    // create array of breadcrumbs for subHeader component
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" }
    ];
    if (adminDash) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" })
    }
    breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/module/dashboard" }, text: "Module Dashboard" })
    // if not editing, add text "Create Module"
    if (!editId) breadcrumbs.push({ text: "Create Module" });    
    // if editing add the module name to the breadcrumbs
    if (editId) breadcrumbs.push({ text: `Edit Module ${activeModule ? `- ${activeModule.name}` : ""}` });
    
    // create array of tutorials for subheader component
    const tutorials = [
        { tutorialSection: "Modules", subSection: ["Parts", "Levels"], allSectionTutorial: false, basicInfo: false, navigationTutorial: false}
    ];

//#endregion functions 

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} tutorials={tutorials} />
           
            <Card className="content-card">
                {error}
                {success}
                {loading ?
                    <SkeletonTheme color="#e0e0e0">
                        <div className="mt-3">
                            <Skeleton height={28} width={200}/>
                            <Skeleton height={16} count={4} />
                        </div>
                    </SkeletonTheme>
                :
                    <form className="create-edit-module" id="module-form" onSubmit={submitModule}>
                        <div>
                            <h4 className="content-title">{!editId ? "Create Module" : "Edit Module"}</h4>
                            {!editId && 
                                <div className="input-col">
                                    <span>
                                        Copy a module's details (endpoints, feature, and type).  This will override these fields if they already have data.  
                                    </span>
                                    <div style={{maxWidth: "400px"}}>
                                        <ModuleTypeahead
                                            placeholder="Search Modules"
                                            passSelection={selection => handleCopy(selection[0])}
                                        />
                                    </div>
                                </div>
                            }
                        </div>
                        <div className="row-pair">
                            <div className="input-col">
                                <CharacterCounterInput
                                    characterLimit = {45}
                                    label = "Name"
                                    required
                                    name="name"
                                    placeholder="module name..."
                                    value={activeModule?.name}
                                    columnRow="column"
                                />
                            </div>
                            <div className="input-col">
                                <label htmlFor="module_type_id">
                                    Module Type
                                    {" "}
                                    <span className="required">*</span>    
                                </label>
                                <select 
                                    required 
                                    value={selectedType} 
                                    id="type" 
                                    name="module_type_id" 
                                    onChange={(e)=>setSelectedType(+e.target.value)}
                                >
                                    <option value={0} disabled >Select a Module Type</option>
                                    {moduleTypes?.map((type, i)=>(
                                        <option key={`module-type-${i}`} value={+type.id}>{type.name}</option>
                                    ))}
                                </select>
                            </div>
                        
                            {/* save for later maybe*/}
                            {/* <div className="input-col">
                                <label htmlFor="slug">Module Slug</label>
                                <input 
                                    name="slug"
                                    placeholder="module-function"
                                    defaultValue={activeModule?.slug}
                                />
                            </div> */}
                        </div>
                        <div className="row-pair">
                            <div>
                                {(selectedType ===1 || selectedType===3) &&
                                    <CharacterCounterInput
                                        characterLimit={256}
                                        label="URL"
                                        required
                                        name="url"
                                        placeholder={selectedType === 1 ? "/module/new or /module/edit" : "https://www.website.com"}
                                        value={activeModule?.url}
                                    />
                                }
                                <CharacterCounterInput
                                    characterLimit={45}
                                    label="Description"
                                    name="description"
                                    placeholder="a short description of the module..."
                                    value={activeModule?.description}
                                />
                                <div className="input-col" name="company_id">
                                    <label htmlFor='company_id'>Company Id</label>
                                    <CompanyTypeahead 
                                        placeholder={"Select a company..."}
                                        passSelection={(selection)=>setSelectedCompany(selection[0]?.id)}
                                        initialDataIds={[selectedCompany]}
                                    />
                                </div>
                                <div className="input-col">
                                    <label htmlFor="feature_id">Belongs to Feature:</label>
                                    <select value={selectedFeatureId} onChange={(e)=>setSelectedFeatureId(e.target.value)}name="feature_id">
                                        <option value={0} disabled>Select A Feature</option>
                                        <option value={0}>None</option>
                                        {features?.map((feature)=>(
                                            <option key={`feature-dd-${feature.id}`} value={feature.id}>{feature.name}</option>
                                        ))}
                                    </select>
                                </div>
                                
                            </div>
                            {selectedType !== 4 &&
                                <div className="input-col">
                                    <label htmlFor="permission_levels">Available Permission Levels</label>
                                    <div>
                                        <input 
                                            type="checkbox"
                                            id="permission-0"
                                            value={0}
                                            checked
                                            disabled
                                        />
                                        <label htmlFor="permission-0" className="check-label">All</label>
                                    </div>
                                    {availablePermissions?.map((permission)=>(
                                        <div key={`permission-${permission.id}`}>
                                            <input 
                                                onChange={handleChecked}
                                                type="checkbox"
                                                name="permission_levels"
                                                id={`permission-${permission.id}`}
                                                checked={activeModule?.permissions?.includes(permission)}
                                                value={permission.id}
                                            />
                                            <label htmlFor={`permission-${permission.id}`} className="check-label">{permission.name}</label>
                                            {" "}
                                            {permission.description && 
                                                <Tooltip 
                                                    text={permission.description}
                                                    direction="top"
                                                    width="150px"
                                                    height="auto"
                                                >
                                                    <i className="far fa-question-circle"></i>
                                                </Tooltip>
                                            }
                                        </div>
                                    ))}
                                </div>
                            }
                        </div>
                        <div>
                            {(selectedType ===1 || selectedType === 2) &&
                                <div className="input-col">
                                    <div>
                                        <label htmlFor="required_endpoints">Required Endpoints</label>
                                    </div>
                                    <div className="row-pair">
                                        <EndPointsTypeahead 
                                            passSelection={passSelection} 
                                            name="required_endpoints" 
                                            placeholder="Select EndPoints" 
                                            multiple={true} 
                                            initialDataIds={selectedEndpoints.length > 0 ? selectedEndpoints : null}
                                            overrideFirstLoad={firstLoad}
                                            setOverrideFirstLoad={setFirstLoad}
                                        />
                                        <Button className="new-endpoint-btn" onClick={()=>setShowCreateEndpoint(true)}>+ Create New Endpoint</Button>
                                    </div>
                                </div>
                            }
                            {(selectedType===1 || selectedType===4)&& //menu item
                                <ModulePages 
                                    selectedType={selectedType}
                                    passSelection={passSelection} 
                                    modules={modules} 
                                    activeModule={activeModule} 
                                />
                            }
                         
                        </div>
                        <div className="input-col">
                            <p className="error-text mt-2">
                                {showLengthError &&  "One or more fields exceeds it's character limit."}
                            </p>
                        </div>
                        <Button className="mt-3" type="submit">{activeModule ? "Edit Module" : "Create New Module"}</Button>
                    </form>
                }
            </Card>
            <Modal size={"lg"} show={showCreateEndpoint} onHide={()=>setShowCreateEndpoint(false)}>
                <Modal.Header>
                    Add Endpoint
                </Modal.Header>
                <Modal.Body>
                    <CreateEndpoints 
                        setHide={handleHide} 
                        setSuccess={setSuccess}
                        isModal={true}
                        passAddition={(addition)=>passAddition(addition)}  
                    />
                </Modal.Body>
            </Modal>

            {/* we should redirect somewhere because if we were editing first, the param (from the url) 
            isn't going away.  We don't want to accidentally edit if they intend to make a new one
            which may be what happens if we just clear the form.  But I also don't want to assume we know 
            if they want to go back to the dashboard or make anotehr! We should maybe think about doing this in other places too? */}
            <RedirectModal 
                show={newOrDashShow}
                onHide={()=>setNewOrDashShow(false)}
                redirectOneText={"Dashboard"}
                redirectOneUrl={"/p/module/dashboard"}
                redirectTwoText={"Create New Module"}
                redirectTwoUrl={"/p/module/new"}
                keyboard={false}
                backdrop={"static"}
                createdWhat="a module"
                create={editId ? false : true}
            />
        </Container>

    )
}
