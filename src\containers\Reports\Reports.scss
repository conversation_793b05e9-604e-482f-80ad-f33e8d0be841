@import '../../assets/css/scss/variables';
@import '../../assets/css/scss/mixins';
@import '../../assets/css/scss/themes';


.report-card{
    container-type: inline-size;
    container-name: report-card;
    .a-tag, .a-tag:focus, .a-tag:hover, .a-tag:active{
        color: $primary-inverse-color;
        text-decoration: none;
    }
    .btn{
        margin-top: 1rem;
    }
    input, select{
        @include basic-input-select;
        height: 40px;
        width: 250px;
    }
    .report-types, .date-selections{
        @include basic-flex-row;
        justify-content: flex-start;
    }
    .prop-group{
        @include basic-flex-column;
        margin: 0 1rem 0 1rem;
    }
    .react-datepicker__input-time-container{
        text-align: center;
    }
    
    
    @container report-card (max-width: 700px){
        .report-types, .date-selections{
            @include basic-flex-column;
        }
    }
    @media (max-width: 700px){
        .report-types, .date-selections{
            @include basic-flex-column;
        }
    }
    @media (max-width: 400px){
        input, select{
            width: 200px;
        }
    }
}