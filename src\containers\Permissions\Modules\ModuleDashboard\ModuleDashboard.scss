@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/mixins';
@import '../../../../assets/css/scss/themes';

.mod-dash-wrapper{
    table{
        td{
            padding: $table-row-padding;
        }
        tr:nth-child(even){
            background-color: $table-header-background-color;
        }
    }
    .mod-dash-title{
        @include flex-row-space-between;
    }
    .searches{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        align-items: flex-start;
        @media (max-width: 1200px){
            flex-direction: column;
            align-items: flex-start;
        }
        .input-row{
            @include basic-flex-row;
            label{
                width: 200px;
            }
        }
        .input-col{
            @include basic-flex-column;
            max-width: 300px;
        }
        input:not([type="checkbox"]):not([type="radio"]), select{
            @include basic-input-select;
            max-width: 200px;
        }
        label{
            @include basic-label;
        }
        button.btn.btn-primary{
            width: 200px;
        }
    }
    .react-numeric-input{
        input{
            @include basic-input-select;
        }
        label{
            @include basic-label;
        }
        b{
            display: none !important;
        }
    }
    .footer-row{
        display: flex;
        justify-content: space-between;
        
    }
}
.create-edit-module{
    .row-pair{
        @include basic-flex-row;
    }
    .input-col{
        @include basic-flex-column;
    }
    input:not([type="checkbox"]):not([type="radio"]), select{
        @include basic-input-select;
    }
    label{
        @include basic-label;
    }
    @include basic-flex-column;
    label{
        margin-top: 1rem;
    }
    input:not([type="checkbox"]), select{
        width: 350px;
    }
    input[type="checkbox"]{
        margin: 0 1rem 0 2rem;
    }
    .check-label{
        margin-top: 0;
    }
    .top-bottom{
        margin-top: 1rem;
    }
    .bottom{
        label{
            width: 200px;
            margin-left: 4rem;
        }
        input, select{
            width: 250px;
            // margin-left: 1rem !important;
            margin-right: 0;
        }
        .nudge{
            margin-right: 0;
        }
    }
    .new-endpoint-btn{
        margin-left: 2.5rem;
        background-color: transparent;
        border: 2px solid $primary-color;
        color: $primary-color;
        margin-left: 2rem;
        height: 35px;
    }
    .required{
        color: $error-color;
    }
}
.choice-modal{
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}