import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import MediaPreview from '../MediaManagerComponents/MediaPreview';
import MediaDescription from '../MediaManagerComponents/MediaDescription';

//src\containers\MediaManager\MediaManager.js

const SelectedMediaWrapper = ({activeMedia, setActiveMedia, setRefreshMedia, allTags, onAddTag, multiSelect}) => {
    if (activeMedia && !Array.isArray(activeMedia)) activeMedia = [activeMedia];
    return (
        <Container className="selected-media-wrapper">
            <h6>Selected Media</h6>
            {activeMedia?.map((media, i)=>(
                <React.Fragment key={`activemedia-${i}`}>
                    <Row>
                        <Col>
                            <MediaPreview activeMedia={media} setActiveMedia={setActiveMedia} setRefreshMedia={setRefreshMedia} multiSelect={multiSelect} />
                        </Col>
                    </Row>
                    <Row className="mt-4">
                        <Col>
                            <MediaDescription activeMedia={media} setActiveMedia={setActiveMedia} setRefreshMedia={setRefreshMedia} multiSelect={multiSelect} allTags={[...allTags]} onAddTag={onAddTag} />
                        </Col>
                    </Row>
                </React.Fragment>
            ))}
            {!activeMedia &&
                <p>
                    Select a media item to view or edit.
                </p>
            }
        </Container>
    )
}

export default SelectedMediaWrapper