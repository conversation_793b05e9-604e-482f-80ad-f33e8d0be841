import React, {useState, useEffect, useRef, useCallback} from 'react'
import { Button } from 'react-bootstrap';

import { BundleOfTokens } from './BundleOptions';
import { BundleOfServiceTokens } from './BundleOptions';

import usePrevious from '../../../../components/common/CustomHooks';
import Services from '../../../../api/Services';
import Events from '../../../../api/Events';

//Called in src\containers\Product\NewProduct\NewProduct.js

export const Bundles=({
    selectedProductType, 
    tokensForBundles, 
    setTokensForBundles, 
    setIncludeToken, 
    resetChildren, 
    editingFirstLoad, 
    setOrphanToken, 
    orphanToken,
    associatedServices, 
    setAssociatedServices})=>{

    const mountedRef = useRef(false);
    const [selectedTokens, setSelectedTokens]=useState([]);
    const [quantity, setQuantity]=useState(0);
    const [triggerAfterLoad, setTriggerAfterLoad]=useState(0)
    const oldRefresh=usePrevious(resetChildren);

    const checkServices = useCallback(async(token)=>{
        try{
            let response = await Services.get({
                product_ids: [token.id]
            })
            return response
        }catch(ex){console.error(ex)}
    },[]);

    const checkEvents = useCallback(async(token)=>{
        try{
            let response = await Events.getSimple({
                product_id: token.id
            })
            return response
        }catch(ex){console.error(ex)}
    },[])

    useEffect(()=>{
        mountedRef.current = true

        return()=> mountedRef.current = false
    },[])

    

    useEffect(()=>{
        if(selectedTokens && !editingFirstLoad.current.edit && mountedRef.current){
            setTokensForBundles(selectedTokens)
        }
    },[selectedTokens, setTokensForBundles, quantity, triggerAfterLoad, editingFirstLoad]);

    useEffect(()=>{
        let serviceCheck;
        let eventCheck;
        const theChecks=async()=>{
            let token = selectedTokens[0];
            serviceCheck = await checkServices(token);
            eventCheck = await checkEvents(token);
        }
        if(selectedTokens.length > 0 && selectedProductType === 6){
            theChecks()
            .then(()=>{
                let services = handleTokenCheckResponse(serviceCheck, "services");
                let events = handleTokenCheckResponse(eventCheck, "events");
                //checking orphan status - if both of them have no results, the token is an orphan (poor token!!)
                if(services.orphan && events.orphan){
                    setOrphanToken(true);
                    setAssociatedServices([])
                } 
                else{
                    setOrphanToken(false);
                    setAssociatedServices([...services.associatedEvents, ...events.associatedEvents])
                } 
            })
        }
    },[selectedTokens, selectedProductType, checkServices, checkEvents, setOrphanToken, setAssociatedServices])

    useEffect(()=>{
        if(oldRefresh !== resetChildren && oldRefresh !==undefined && !editingFirstLoad.current.edit && mountedRef.current){
            setSelectedTokens([]);
            setQuantity();
            setOrphanToken(false);
        }
    //we don't want this triggering with editingFirstLoad, just check
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[resetChildren, oldRefresh]);

    useEffect(()=>{
        if(editingFirstLoad.current.edit && !editingFirstLoad.current.bundleDone && tokensForBundles && selectedTokens?.length === 0){
            setSelectedTokens(tokensForBundles);
            if(tokensForBundles?.length > 0 && tokensForBundles[0].hasOwnProperty("quantity"))  setQuantity(tokensForBundles[0].quantity)
            editingFirstLoad.current.bundleDone = true;
            if(editingFirstLoad.current.edit && editingFirstLoad.current.bundleDone && editingFirstLoad.current.variantsDone && editingFirstLoad.current.basicInfoDone && mountedRef.current) editingFirstLoad.current.edit=false;
            setTriggerAfterLoad(1)
        }
        //we don't want to trigger when selectedTokens changes.  We just want to make sure it's 0 when this runs
        // eslint-disable-next-line react-hooks/exhaustive-deps
    },[editingFirstLoad, tokensForBundles]);

    useEffect(()=>{
        if(selectedTokens.length > 0) selectedTokens[0].quantity = quantity;
    },[quantity, selectedTokens]);

    const handleTokenCheckResponse = (response, type)=>{
        let orphan;
        let associatedEvents = [];
        let err;
        if(response.status===200 && mountedRef.current){
            if(response.data[type]?.length > 0){ //has services/events
                orphan = false;
                response.data[type].forEach((service)=>{
                    associatedEvents.push(
                        {name: service.name, id: service.id, type: type}
                    )
                });
            } else if(response.data[type]?.length===0){
                orphan = true;
            } 
        }else if(response.errors){
            orphan = true; //we still want to disable submission if we cannot verify the token
            err = "Could not check the status of the selected token.  Please try again."
        }
        return {orphan: orphan, associatedEvents: associatedEvents, err: err}
    }

    return(
        <>
            {/*Subscriptions*/}
            {selectedProductType===1 &&
                <BundleOfTokens 
                    selectedTokens={selectedTokens}
                    passSelection={setSelectedTokens}
                    setIncludeToken={setIncludeToken}
                />
            }
            {/*Bundles*/}
            {selectedProductType===6 &&
                <>
                    <BundleOfServiceTokens 
                        selectedTokens={selectedTokens}
                        quantity={quantity}
                        setQuantity={setQuantity}
                        passSelection={setSelectedTokens}
                        setIncludeToken={setIncludeToken}
                        setOrphanToken={setOrphanToken}
                    />
                    {orphanToken && selectedTokens.length > 0 ?
                        <div className="orphan-token">
                            <p>
                                <span className="local-errors">
                                    The selected token is not associated with any events or services.
                                </span>
                                <span>
                                    You cannot create a bundle using a token that has no association.  You can visit the event dashboard or the services dashboard instead to associate the desired token with something.
                                </span>
                            </p>
                            <p>
                                <Button onClick={()=>window.open("/events")}>Events</Button>
                                <Button onClick={()=>window.open("/services/dashboard")}>Services</Button>
                            </p>
                        </div>
                    :
                    <div className="no-orphan">
                        {associatedServices.length > 0 && selectedTokens.length > 0 &&
                            <>
                                <p>
                                    The following events and services are associated with the token you have selected.  To view the event or service, you may click on it's name and it will take you to the management page for more information. 
                                </p>
                                <p>
                                    {associatedServices?.map((service, i)=>(
                                        <React.Fragment key={`associated-service-list-${service.name}-${i}`}>
                                            <span className="cp fake-btn-outline" onClick={service.type==="services" ? ()=>window.open(`/p/services/${service.id}`, "_blank") : ()=>window.open(`/p/events/${service.id}`, "_blank")} key={`product-services-${service.id}`}>
                                                {service.name} 
                                            </span>
                                            {i < associatedServices.length-1 && 
                                                <span>
                                                    -- {" "}  
                                                </span>
                                            }
                                        </React.Fragment>
                                    ))}
                                </p>        
                            </>
                        }
                        </div>       
                    }
                </>
            }
        </>
    )
}