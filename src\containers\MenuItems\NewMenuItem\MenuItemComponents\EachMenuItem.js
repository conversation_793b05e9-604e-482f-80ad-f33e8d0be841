import React from 'react'

export const EachMenuItem = ({item, setActiveItem, activeItem, ...props}) => {
    
    return (
        <div className={activeItem?.id ===item.id ? "each-menu-item cp active-item" : "each-menu-item cp"} onClick={()=>setActiveItem(item)}>
            {item?.default_menu_item?.text ? 
                <p>
                    <span className="bold">
                        Menu Item: 
                    </span>
                    {item?.default_menu_item?.text}
                </p>
            :
                <p>
                    <span className="bold">
                        Module Name: 
                    </span>
                    {item?.name}
                </p>
            }
            {item?.url ? 
                <p>
                    <span className="bold">
                        URL:
                    </span>
                    {item?.url}
                </p>
            :
                ""
            }
        </div>
    )
}

export default EachMenuItem