import ErrorCatcher from "../components/common/ErrorCatcher";
import Toast from "../components/Toast";

export function parseErrorToString (error) {
    // console.log(parseErrorToArray(error));
    return parseErrorToArray(error).join("\n");
}

export function parseErrorToArray (error) {
    let contents = [];
    if(error.message) contents.push(error.message);
    else if(typeof(error.data) === "string") contents.push(error.data);
    else if(error.data?.error) contents.push(error.data.error);
    else if(typeof(error)==="string") contents.push(error);
    else if(error.length > 0) {
        contents = [...contents, ...getErrorValues(error, contents)];
    }
    else if(error?.validation) {
        contents = [...contents, ...getErrorValues(error.validation, contents)];
    }
    return contents;
}

//recursively get all values in the errors object, regardless of depth
//TODO: remove before launch, too much error info to be useful to users
export const getErrorValues = (error, contents=[]) => {
    const values = Object.values(error);
    if(typeof(values) !== "string") {
        values.forEach( (value, i) => {
            value && typeof(value) === "object" ? getErrorValues(value, contents) : contents.push(value);
        });
    }
    return contents;
}

export const setErrorCatcher=(error, warning=false, title)=>{
    if(error) {
        return <ErrorCatcher error={error} warning={warning} title={title}/>
    }
    else return null;
}

export const setSuccessToast=(success)=>{
    if(success){
        return <Toast>{success}</Toast>
    }else return null
}

/**Returns a random string for a password */
export const autoGeneratePasswordHandler = () => {
    const length = 15;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-!@#$%^&*(){}[]<>?";
    let retVal = "";
    for (let i = 0, n = charset.length; i < length; ++i) {
        retVal += charset.charAt(Math.floor(Math.random() * n));
    }        
    return retVal;
}

/**Returns if a string contains any symbols 
 * 
 * Checks for the following symbols
 * octal codes on ASCII table in order as below @ : ; < = > ? {space} ! " # $ % & ' ( ) * + , / [ \ ] ^ ` { | } ~         
*/
export const checkForSymbols = (toCheck) =>{
    const testRegEx=/[\100\72-\77\40-\54\57\133-\136\140\173-\176]/.test(toCheck); 
    return testRegEx;
}

/** takes a string and removes all white space, returns clean string */
export const removeWhiteSpace=(toRemove)=>{
    let clean = toRemove.replace(/\s/g, '');
    return clean;
}

/** 
 * Makes sure that all parameters of a password requirement are met
 * Checks Uppercase, Lowercase, Number, Special Character, and Length
 */
export const validatePasswordPattern = (password) => {
    const result = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9]).{8,}$/.test(password)
    return result;
}

/** 
 * Make sure that an email at least has the correct pattern of an email (some characters followed by the @ symbol and a period being followed by at least a couple characters after)
 */
export const checkEmailPattern=(email)=>{
    let result =/^[\w!#$%&'*+/=?`{|}~^-]+(?:\.[\w!#$%&'*+/=?`{|}~^-]+)*@(?:[A-Z0-9-]+\.)+[A-Z]{2,}$/gi.test(email);
    return result;
}


export const passwordStatement="Your password must be at least 8 characters long and must contain contain a symbol, number, uppercase, and lowercase letter."

// these variables are used for both autoGenerateCompliantPassword and indepthValidation
const fullSet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_-!@#$%^&*(){}[]<>?0123456789"
const charset = "abcdefghijklmnopqrstuvwxyz";
const charsetSymbol = "_-!@#$%^&*(){}[]<>?";
const charsetNumeric = "0123456789";

/**
 * The password returned here will be guaranteed to have a character, lower case, uppercase, and a symbol.  
 */
export const autoGenerateCompliantPassword = () => {
    const length = 15;
    let tempPass = "";
    for (let i = 0, n = fullSet.length; i < length; ++i) {
        tempPass += fullSet.charAt(Math.floor(Math.random() * n));
    }    
    tempPass+= charsetSymbol.charAt(Math.floor(Math.random() * charsetSymbol?.length))
    tempPass+= charsetNumeric.charAt(Math.floor(Math.random() * charsetNumeric?.length))
    tempPass+= charset.charAt(Math.floor(Math.random() * charset?.length)).toUpperCase()
    tempPass+= charset.charAt(Math.floor(Math.random() * charset?.length))   
    return tempPass;
}

/** 
 * This will assess what's actually missing from the criteria of a password and return a more informed string
 */
export const indepthPasswordValidationError=(word)=>{
    let specificIssue = ""
    let specificArray = [] //so we can count the results for proper punctuation
    
    let hasLower=false;
    let hasUpper=false;
    let hasSymbol=false;
    let hasNumber=false;
    let hasLength = false;
    
    if(word.length > 7) hasLength = true;

    for(let i =0; i < word.length; i++){
        if(charset.includes(word[i])) hasLower=true;
        if(charset.toUpperCase().includes(word[i])) hasUpper=true;
        if(charsetSymbol.includes(word[i])) hasSymbol = true;
        if(charsetNumeric.includes(word[i])) hasNumber=true;
        if(hasLower && hasUpper && hasNumber && hasSymbol) break;
    }
    
    if(hasLower && hasUpper && hasSymbol && hasNumber && hasLength) return
    else {
        if(!hasLength) specificArray.push(" at least 8 characters")
        if(!hasLower) specificArray.push(" a lowercase letter")
        if(!hasUpper) specificArray.push(" a capital letter")
        if(!hasSymbol) specificArray.push(" a symbol/special character")
        if(!hasNumber) specificArray.push(" a number")
    
        specificIssue = "Your password needs to have"

        for(let i = 0; i < specificArray.length; i++){
        if(specificArray.length > 1 && specificArray.length - 1 === i) specificIssue += " and "
        specificIssue += specificArray[i];
        if(specificArray.length === 1 || specificArray.length-1 === i) specificIssue += "."
        else if (specificArray.length > i && specificArray.length > 2) specificIssue += ", "
        }
        
    }
    return specificIssue;
}