import React, { useState, useEffect, useRef } from 'react'
import { Con<PERSON>er, Card, Breadcrumb } from 'react-bootstrap';
import { useHistory,Link } from 'react-router-dom';
import { format } from 'date-fns-tz';

import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';
import { authUserHasModuleAccessMany } from '../../../utils/auth';

import Transactions from '../../../api/Transactions';
import SubHeader from '../../../components/common/SubHeader';

import './TransactionPage.scss'

const VIEW_ALL_TRANSACTIONS_MODULE_ID = 11;
const VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID = 320;
const TRANSACTION_STATUSES=[
    {id: 1, name: "Uncaptured"},
    {id: 2, name: "Captured"},
    {id: 3, name: "Voided"},
    {id: 4, name: "Cancelled"},
    {id: 5, name: "Expired"},
    {id: 6, name: "In Flight"},
    {id: 7, name: "Complete/Pending Settlement"},
    {id: 8, name: "Failed"},
    {id: 9, name: "Refunded"}
]

export const TransactionPage = () => {

    const mountedRef = useRef(false);
    const history = useHistory();
    const [transactionId, setTransactionId]=useState(+history.location.pathname.split("/")[3])
    const [currentTransaction, setCurrentTransaction]=useState(null);
    const [advancedDetails, setAdvancedDetails]=useState(false);
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [authPermission, setAuthPermissions] = useState();

    useEffect(()=>{
        mountedRef.current = true

        const checkAdvancedDetailAccess=async()=>{
            try{
                let response = await authUserHasModuleAccessMany([VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID, VIEW_ALL_TRANSACTIONS_MODULE_ID]);
                if(response) setAuthPermissions(response)
            }catch(ex){console.error(ex)}
        }

        checkAdvancedDetailAccess();

        return()=> mountedRef.current = false
    },[])

    useEffect(()=>{
        setTransactionId(+history.location.pathname.split("/")[3])
    },[history.location.pathname])

    useEffect(()=>{
        const getTransaction=async()=>{
            if (transactionId){
                try{
                    let response = await Transactions.getAll({id: transactionId});
                    if(response.status === 200){
                        setCurrentTransaction(response.data.transactions[0]) //becauase we're asking by id, we should only ever get 1
                    }else if(response.errors) setError(setErrorCatcher(response.errors))
                    else setError(setErrorCatcher("There was a problem retrieving the transaction"))
                }catch(ex){console.error(ex)}
            }
        }
        getTransaction();
    },[transactionId]);

    const handleUserClick=(id)=>{
        history.push(`/p/users/${id}`)
    }

    const breadCrumbItems = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
    ]
    if(authPermission && authPermission[VIEW_ALL_TRANSACTIONS_MODULE_ID]){
        breadCrumbItems.push({ linkAs: Link, linkProps: { to: "/p/transactions" }, text: "All Transactions" })
    }else breadCrumbItems.push({linkAs: Link, linkProps: {to: "/p/my/transactions"}, text: "My Transactions"})
    breadCrumbItems.push({ text: `Transaction ${transactionId ? `#${transactionId}` : ""}` })


    return (
        <Container fluid className="transaction-page-wrapper">
            <SubHeader items={breadCrumbItems} />
            <Card className="content-card">
                {currentTransaction &&
                    <>
                        <h4 className="section-title">
                            <i className="far fa-credit-card" />
                            {" "}
                            Transaction #{transactionId}
                        </h4>
                        <hr className="transaction-hr" />
                        <div className='transaction-details-wrapper'>
                            <div>
                                <h5 className="trans-status">
                                    {TRANSACTION_STATUSES.filter((status)=>status.id === currentTransaction?.transaction_status_id)[0]?.name || "No Status Listed"}
                                </h5>
                                <hr className="transaction-hr" />
                            </div>
                            <div>
                                <p className="data-pair">
                                    <span>
                                        Created At:
                                    </span>
                                    <span>
                                        {format(new Date(currentTransaction.created_at), 'M/d/yy')}
                                        {" "}
                                        {format(new Date(currentTransaction.created_at), 'h:mm a z')}
                                    </span>
                                </p>
                                <p className="data-pair">
                                    <span>
                                        Updated At:
                                    </span>
                                    <span>
                                        {format(new Date(currentTransaction.updated_at), 'M/d/yy')}
                                        {" "}
                                        {format(new Date(currentTransaction.updated_at), 'h:mm a z')}
                                    </span>
                                </p>
                                <p className="data-pair">
                                    <span>
                                        Amount:
                                    </span>
                                    <span>
                                        ${currentTransaction?.amount?.toFixed(2)}
                                    </span>
                                </p>
                                <p className="data-pair">
                                    <span>
                                        Payment Method:
                                    </span>
                                    <span>
                                        {currentTransaction?.payment_method}
                                    </span>
                                </p>
                                <p className="data-pair">
                                    <span>
                                        Type:
                                    </span>
                                    <span>
                                        {currentTransaction?.transaction_type}
                                    </span>
                                </p>
                                <p className={`${authPermission && authPermission[VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID] ? "cp" : ""} data-pair`} onClick={authPermission && authPermission[VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID] ? ()=>handleUserClick(currentTransaction.user.id) : null}>
                                    <span>
                                        User:
                                    </span>
                                    <span>
                                        {currentTransaction?.user?.first_name} {" "} {currentTransaction?.user?.last_name}
                                        {" "}
                                        ({currentTransaction?.user?.username})
                                        {" "}
                                        {authPermission && authPermission[VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID] &&
                                            <i className="far fa-link" />
                                        }
                                    </span>
                                </p>
                            </div>
                            {authPermission && authPermission[VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID] &&
                                <div>
                                    <p className={`${authPermission && authPermission[VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID] ? "cp" : ""} data-pair`} onClick={authPermission && authPermission[VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID] ? ()=>handleUserClick(currentTransaction.admin_auth_id) : null}>
                                        <span>
                                            Admin Auth:
                                        </span>
                                        <span>
                                            {currentTransaction.admin_auth}
                                            {" "}
                                            {authPermission[VIEW_ADVANCED_ORDER_DETAILS_MODULE_ID] &&
                                                <i className="far fa-link" />
                                            }
                                        </span>
                                    </p>
                                    {currentTransaction.transId &&
                                        <p className="data-pair">
                                            <span>
                                                Trans Id:
                                            </span>
                                            <span>
                                                {currentTransaction.transId}
                                            </span>
                                        </p>
                                    }
                                    {currentTransaction.transKey &&
                                        <p className="data-pair">
                                            <span>
                                                Trans Key:
                                            </span>
                                            <span>
                                                {currentTransaction.transKey}
                                            </span>
                                        </p>
                                    }
                                </div>
                            }
                            <hr className="transaction-hr" />
                            <div className="order-detail">
                                <h4>
                                    <i className="far fa-list" />
                                    {" "}
                                    Order Details
                                </h4>
                                <p className="data-pair cp" onClick={()=>{history.push(`/p/order/${currentTransaction?.order?.id}`)}}>
                                    <span>
                                        Order Id:
                                    </span>
                                    <span>
                                        #{currentTransaction?.order?.id}
                                        {" "}
                                        <i className="far fa-link" />
                                    </span>
                                </p>
                                <p className="data-pair">
                                    <span>
                                        Order Total:
                                    </span>
                                    <span>
                                        ${currentTransaction?.order?.total_price}
                                    </span>
                                </p>
                                <p className="data-pair">
                                    <span>
                                        Created At
                                    </span>
                                    <span>
                                        {format(new Date(currentTransaction?.order?.created_at), 'M/d/yy')}
                                        {" "}
                                        {format(new Date(currentTransaction?.order?.created_at), 'h:mm a z')}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </>
                
                }
            </Card>
        </Container>
    )
}
