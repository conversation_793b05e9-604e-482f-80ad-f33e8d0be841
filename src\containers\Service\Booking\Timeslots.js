import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Row, Form, Button,  Tabs, Tab } from 'react-bootstrap';
import { format, formatISO, startOfToday, set as setTime, isWithinInterval, addDays, subDays, isToday, isPast, addMinutes } from 'date-fns';

import * as actions from '../../../store/actions';
import { selectRangeStart, selectRangeEnd, selectCurrentLocationConflictEvents, selectTimeSlot, selectTokensForCurrentBooking } from '../../../store/selectors';
import Events from '../../../api/Events';
import TimeslotGrid from './TimeslotGrid';
import { useRoleCheck } from '../../../components/common/CustomHooks';
import { BookingDescription } from '../../Service/Components/Booking/BookingDescription'
import "react-datepicker/dist/react-datepicker.css";
import './Booking.scss';
import { saveLogging } from '../../../utils/thunks';
import WeeklyDateScroller from '../../../components/common/WeeklyDateScroller';
import { findDateRanges, findMinMax } from '../../../utils/helperFunctions';

const PATRON_CART_URL = "/p/shop";

const Timeslots = ({ onChangeInput=()=>{}, goTo }) => {
    const dispatch = useDispatch();
    const history = useHistory();

    const currentBooking = useSelector(state => state.serviceBooking.current_booking);
    const errors = useSelector(state => state.serviceBooking.errors);
    const currentUserRole = useRoleCheck();

    const rangeStart = useSelector(selectRangeStart); // ISO format
    const rangeEnd = useSelector(selectRangeEnd); // ISO format
    const conflictEvents = useSelector(selectCurrentLocationConflictEvents);
    const selectedTimeslot = useSelector(selectTimeSlot);
    const tokens = useSelector(selectTokensForCurrentBooking);

    const [pagePartTimeslots, setPagePartTimeslots] = useState();
    const [pagePartServiceInfo, setPagePartServiceInfo] = useState();
    const [linkToBundles, setLinkToBundles] = useState(null);

    // link to product bundles
    useEffect(() => {
        if (currentBooking?.service?.id && currentBooking.bundles_available_for_purchase) {
            if (currentBooking.bundles_available_for_purchase.length > 0) {
                setLinkToBundles(
                            <Button variant="light icon-on-right"
                                size="sm"
                                onClick={()=>history.push(PATRON_CART_URL)}
                                style={{"marginBottom": "0"}}
                            >Purchase Bundles
                                <i className="far fa-arrow-right"/>
                            </Button>
                            // {currentBooking.bundles_available_for_purchase.map((product, i) => 
                            //     <>
                            //         Bundles available for this Service: 
                            //         {i>0 ? "," : ""}
                            //         {" " + product.name}
                            //     </>
                            // )}
                );
            } else {
                setLinkToBundles("");
            }
        }
    },[currentBooking, history]);

    // TIMESLOT SELECTOR

    const onClickBack = useCallback(() => {
        dispatch(actions.setServiceBooking({ service: null, selected_location: null, selected_slots: [], conflict_events: null }));
        goTo("Selection");
    }, [dispatch, goTo]);

    const onClickNext = useCallback(() => {
        goTo("Summary");
    }, [goTo]);

    const onChangeDate = useCallback(newDate => {
        dispatch(actions.setServiceBooking({
            selected_date: newDate,
            selected_slots: []
        }));
    }, [dispatch]);

    const onChangeLocation = useCallback(location => {
        dispatch(actions.setServiceBooking({
            selected_location: parseInt(location),
            selected_slots: []
        }));
    }, [dispatch]);

    // generate the daysList (array of dates) to loop through to make grid
    useEffect(() => {
        // set the min hour to the rounded hour before the first timeslot
        // set the max hour to the rounded hour after the latest timeslot ends

        if (currentBooking.service?.blocks) {
            let blocks = currentBooking.service?.blocks;
            let minMax = findMinMax(blocks);
            let list = findDateRanges(blocks, rangeStart, rangeEnd, minMax)

            dispatch(actions.setServiceBooking({
                event_query: {
                    location_ids: currentBooking.service.location_ids,
                    datetime_ranges: list
                },
                min_grid_hour: minMax.min,
                max_grid_hour: minMax.max
            }));
        }
    },[rangeStart, rangeEnd, currentBooking.service, dispatch]);

    useEffect(() => {
        // re-fetch the new API - events conflicts for given locations and days
        let mounted = true;

        dispatch(saveLogging(`event query (timeSlot 149) current Booking -  ${currentBooking.event_query}`))
        // get all events if nothing is already loaded
        // This *should* only be called if something changes with the query
        // TODO: Re-fetch the data after a 5 minute timer, to make sure we have the latest info
        if(currentBooking.event_query?.datetime_ranges && currentBooking.event_query?.datetime_ranges.length>0) {
            Events.getByLocationAndDate({ ...currentBooking.event_query, include_meta_events: 0 })
            .then(response => {
                if(mounted) {
                    if (!response.errors) {
                        dispatch(actions.setServiceBooking({
                            conflict_events: response.data
                        }));
                    }
                }
            }).catch(e => console.error(e));
        } else {
            dispatch(actions.setServiceBooking({
                conflict_events: null
            }));
        }

        return () => {
            mounted = false;
        }
    },[currentBooking.event_query, dispatch]);

    // loop for making the page items
    useEffect(() => {

        const isInRange = (date) => {
            return isWithinInterval(
                date,
                { start: new Date(rangeStart), end: new Date(rangeEnd) }
            )
        }

        const onDateBack = (event) => {
            let interval = currentBooking.small_screen ? currentBooking.small_screen_num_days : 7;
            let newDate = subDays(new Date(currentBooking.selected_date), interval);
            if (isPast(newDate)) {
                newDate = new Date();
            }
            onChangeDate(newDate);
        }

        const onDateNext = (event) => {
            let interval = currentBooking.small_screen ? currentBooking.small_screen_num_days : 7;
            let newDate = addDays(new Date(currentBooking.selected_date), interval);
            onChangeDate(newDate);
        }

        const hasMinimumTimeslots = () => {
            if (currentBooking.selected_slots?.length < currentBooking.service.min_timeslots) {
                return false;
            }
            return true;
        }

        const hasNoPartialToken = () => {
            if (currentBooking.selected_slots?.length % currentBooking.service.timeslots_for_token === 0) {
                return true;
            }
            return false;
        }

        const statusErrorMessages = (
            <>
            {selectedTimeslot.start &&
                <Form.Row className="service-selection stacked highlight timeslot-display mb-0">
                    <div className="mb-1">Selected Timeslot: {format(selectedTimeslot.start, "EEEE MMM d, yyyy h:mm a")} - {format(addMinutes(selectedTimeslot.end,1), "h:mm a")}</div>
                    {!hasMinimumTimeslots() ?
                        <div className={`error-text`}>Minimum {currentBooking.service.min_timeslots} blocks required.</div>
                        :
                        !hasNoPartialToken() &&
                            <div className={`error-text`}>Number of selected blocks must be a multiple of {currentBooking.service.timeslots_for_token}.</div>
                    }
                </Form.Row>
            }
            </>
        );

        if (currentBooking.service) {
            if (currentBooking.selected_location && currentBooking.service.location_ids.includes(currentBooking.selected_location)) {
                let rangeStartD = new Date(rangeStart);
                let rangeEndD = new Date(rangeEnd);
                
                // on big screens, back button should be disabled when the current date is on the screen - today is within range
                // on small screens back button should be disabled when current date is selected
                let backDisabled = false;
                if ( (!currentBooking.small_screen && isInRange(new Date()))
                    || (currentBooking.small_screen && isToday(currentBooking.selected_date)) ) {
                    backDisabled = true;
                }
                
                setPagePartTimeslots(
                    <>
                        <WeeklyDateScroller 
                            backDisabled={backDisabled}
                            smallScreen={currentBooking.small_screen}
                            selectedDate={currentBooking.selected_date}
                            onDateBack={onDateBack}
                            onDateNext={onDateNext}
                            onChangeDate={onChangeDate}
                            rangeStartDate={rangeStartD}
                            rangeEndDate={rangeEndD}
                            isInRange={isInRange}
                        />
                        {/* <Form.Row className="service-selection datepicker">
                            <ButtonGroup>
                                <Button variant="light"
                                    data-cy="date-range-back"
                                    disabled={backDisabled}
                                    className="datepicker-back-next back"
                                    onClick={onDateBack}
                                >
                                    <i className="far fa-angle-left"></i>
                                </Button>
                                <DatePicker 
                                    dateFormat="MM/dd/yyyy"
                                    minDate={currentBooking.small_screen ? new Date() : startOfISOWeek(new Date())}
                                    maxDate={new Date(new Date().getFullYear()+1,12,31)}
                                    showMonthDropdown
                                    showYearDropdown
                                    selected={currentBooking.selected_date}
                                    onChange={onChangeDate}
                                    customInput={
                                        <Button variant="light" className="datepicker-calendar" type="button" data-cy="date-range-picker">
                                            {format(rangeStartD, "MM/dd/yyyy")}
                                            {' - '}
                                            {format(rangeEndD, "MM/dd/yyyy")}
                                        </Button>
                                    }
                                    dayClassName={(date) => isInRange(date) ? "react-datepicker__day--selected" : ""}
                                />
                                <Button variant="light"
                                    data-cy="date-range-forward"
                                    className="datepicker-back-next next"
                                    onClick={onDateNext}
                                >
                                    <i className="far fa-angle-right"></i>
                                </Button>
                            </ButtonGroup>
                        </Form.Row> */}
                        <Form.Row className="service-selection stacked left">
                            <Tabs
                                id="timeslot-tabs"
                                activeKey={currentBooking.selected_location}
                                onSelect={(k) => onChangeLocation(k)}
                                className="mb-1 mt-1"
                            >
                                {currentBooking.service.location_ids.map(loc =>
                                    <Tab key={`location-tab-${loc}`}
                                        eventKey={loc}
                                        title={currentBooking.location_names ? currentBooking.location_names[loc] : `Location ${loc}`}
                                    >
                                        {/* Don't load the grid unless it's active - it would slow the page down for no good reason */}
                                        {parseInt(currentBooking.selected_location)===loc &&
                                        <>
                                            {statusErrorMessages}
                                            <TimeslotGrid
                                                location={loc}
                                                startDate={rangeStartD} // Date format
                                                endDate={rangeEndD} // Date format
                                                // onSelect={onSelectTimeslot}
                                                minHour={currentBooking.min_grid_hour}
                                                maxHour={currentBooking.max_grid_hour}
                                                conflictEvents={conflictEvents}
                                                service={currentBooking.service}
                                                showConflictInfo={currentUserRole?.id<=5}
                                            />
                                        </>
                                        }
                                    </Tab>
                                )}
                            </Tabs>
                        </Form.Row>
                        {statusErrorMessages}
                        <div className="button-row">
                            <Button variant="secondary" onClick={onClickBack}>
                                <i className={`fas fa-arrow-left`}></i>
                                Back
                            </Button>
                            <Button variant="primary" onClick={onClickNext} className={hasMinimumTimeslots() ? '' : 'hidden'} data-cy="next-btn">
                                Next
                                <i className={`fas fa-arrow-right`}></i>
                            </Button>
                        </div>
                    </>
                );

            } else if (currentBooking.service.location_ids?.length>0) { 
                // no selected_location - set default as the first location in the list
                onChangeLocation(currentBooking.service.location_ids[0]);
            }
        } else {
            setPagePartTimeslots();
        }
    },[currentBooking, rangeStart, rangeEnd, conflictEvents, selectedTimeslot, currentUserRole, onChangeLocation, onChangeDate, onClickNext, onClickBack]);

    return (
        <div>
            {currentBooking?.service?.id &&
                <BookingDescription 
                    service={currentBooking.service}
                    onClickBack={onClickBack}
                    tokens={tokens}
                    linkToBundles={linkToBundles}
                />
            }
            {pagePartTimeslots}
            <div className={`err ${!!errors.timeslots ? "" : "hidden"}`}>
                {errors.timeslots}
            </div>
        </div>
    );
}

export default Timeslots;