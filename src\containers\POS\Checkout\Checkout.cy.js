/*eslint-disable*/
/// <reference types="cypress" />

let patronEmail = Cypress.env('impact_patron_user')
let managerEmail = Cypress.env('impact_admin_user');
let password = Cypress.env('login_password')

import Checkout from './index';
import './Checkout.module.scss';
import store from '../../../redux-store';

describe("It will mount the checkout component and ensure everything renders properly", { scrollBehavior: false }, ()=>{
    
    let orderUserDiscount;

    const dispatches=(order)=>{
        store.dispatch({
            type: 'SET_COMPANY_CONFIG',
            config: {
                guest_user_id: 7
            }
        })
        store.dispatch({
            type: 'POS_ORDER_ALL',
            order: order,
            register_id: 1
        })
        store.dispatch({
            type: 'POS_ORDER',
            order: order.id,
            register_id: 1
        })
        store.dispatch({
            type: 'POS_ADD_ITEM',
            items: order.items[0],
            register_id: 1
        })
    }

    before(()=>{
        cy.fixture('/Order/order.json').then((data)=>{
            orderUserDiscount = data.orderUserDiscount.data[0];
        })
    })
    
    beforeEach(()=>{
        cy.viewport(1200, 1080)
    })
    
    it("will check that all the buttons have rendered and basic info is present", ()=>{
        dispatches(orderUserDiscount);
        cy.mount(<Checkout
                register_id={1}
                is_patron={false}
                show={true}
            />, {reduxStore: store});
        cy.get('[data-cy="payment-type-2"]') //cash
            .should('exist'); 
        cy.get('[data-cy="payment-type-TERMINAL"]') //scan Card
            .should('exist');
        cy.get('[data-cy="payment-type-1"]') //enter card
            .should('exist');
        cy.get('[data-cy="payment-type-3"]') //check
            .should('exist');
        cy.get('[data-cy="payment-type-4"]') //gift card
            .should('exist');
        cy.get('[data-cy="payment-type-5"]') //manager discount
            .should('exist');
        cy.get('[data-cy="payment-tip-10"]') //10%
            .should('exist');
        cy.get('[data-cy="payment-tip-15"]') //15%
            .should('exist');
        cy.get('[data-cy="payment-tip-20"]') //20% 
            .should('exist');
        cy.get('[data-cy="payment-tip-other"]') //other
            .should('exist');
        cy.get('#memo')
            .should('exist');
        cy.get('[data-cy="checkout-totals-subtotal"]')
            .should('exist')
            .invoke('text')
            .should("include", "Subtotal$3.18");
        cy.get('[data-cy="checkout-totals-coupons"]')
            .should('exist')
            .invoke('text')
            .should('include', "-$0.56");
        cy.get('[data-cy="checkout-totals-container"]')
            .children()
            .contains('Taxes')
            .next()
            .invoke('text')
            .should('include', "$0.22");
        cy.get('[data-cy="checkout-totals-container"]')
            .children()
            .contains('Tip')
            .should('not.exist')
        // cy.get('[data-cy="checkout-totals-tip"]')
        //     .should('not.exist')
        cy.get('[data-cy="checkout-totals-total"]')
            .should('exist')
            .invoke('text')
            .should('include', "Total$3.4");
        cy.get('[data-cy="checkout-totals-outstanding"]')
            .should('exist')
            .invoke('text')
            .should('include', "Pending Balance$3.40");
        cy.get('[data-cy="details-add-to-cart"]')
            .should('exist')
            .should('have.attr', 'disabled')
    });

    it("will check that cash renders",()=>{
        dispatches(orderUserDiscount);
        cy.mount(<Checkout
                register_id={1}
                is_patron={false}
                show={true}
            />, {reduxStore: store});
        cy.get('[data-cy="cash-payment-button-group"]')
            .should('not.exist');
        cy.get('[data-cy="payment-type-2"]') //cash
            .click();
        cy.get('[data-cy="cash-payment-button-group"]')
            .should('exist');
        cy.get('[data-cy="cash-payment-button-group"]')
            .children()
            .should('have.length', 6);
        cy.get('[data-cy="cash-payment-button-group"]')
            .children()
            .eq(0)
            .invoke('text')
            .should('include', '1');
        cy.get('[data-cy="cash-payment-button-group"]')
            .children()
            .eq(3)
            .invoke('text')
            .should('include', '20');
        cy.get('[data-cy="checkout-totals-cash-discount"]')
            .should('exist')
            .invoke('text')
            .should('include', '0.14')
        cy.get('[data-cy="checkout-totals-payment-0"]')
            .should('not.exist');
        cy.get('.PaymentType_wrapper__rxObr > .Tip_buttons__X38h5 > :nth-child(1)')
            .click()
            .type('11');
        cy.get('.form-group > .form-label')
            .click({force: true}) // have to force a click elsewhere to force things to render as they don't render on change of typing
        cy.get('[data-cy="checkout-totals-payment-0"]')
            .invoke('text')
            .should('include', "Cash Tendered$11.00")
        cy.get('[data-cy="details-add-to-cart"]')
            .should('exist')
            .should('not.have.attr', 'disabled')
        cy.get('[data-cy="details-add-to-cart"]')
            .invoke('text')
            .should('include', "Complete Sale")
    });

    it("will check that a tip is added",()=>{
        dispatches(orderUserDiscount);
        cy.mount(<Checkout
                register_id={1}
                is_patron={false}
                show={true}
            />, {reduxStore: store});

        //check tip buttons
        cy.get('[data-cy="checkout-totals-container"]')
            .children()
            .contains('Tip')
            .should('not.exist')
        cy.get('.Tip_buttons__X38h5')
            .should('exist')
            .and('be.visible');
        cy.get('[data-cy="payment-tip-10"]')
            .click({force:true});
        cy.get('[data-cy="checkout-totals-container"]')
            .children()
            .contains('Taxes')
            .next()
            .invoke('text')
            .should('include', "$0.22");
        cy.get('[data-cy="checkout-totals-container"]')
            .children()
            .contains('Tip')
            .next()
            .invoke('text')
            .should('include', "$0.32");
        // cy.get('[data-cy="checkout-totals-tip"]')
        //     .invoke('text')
        //     .should('include', "Tip$0.32")

        //check custom tip
        cy.get('[data-cy="payment-tip-input"]')
            .should('not.be.visible');
        cy.get('[data-cy="payment-tip-other"]')
            .click();
        cy.get('[data-cy="payment-tip-input"]')
            .should('be.visible')
            .click()
            .clear()
            .type(1.00);
        cy.get('[data-cy="checkout-totals-container"]')
            .children()
            .contains('Tip')
            .next()
            .should('exist');
        cy.get('[data-cy="checkout-totals-container"]')
            .children()
            .contains('Tip')
            .next()
            .invoke('text')
            .should('include', "$1.00");
        // cy.get('[data-cy="checkout-totals-tip"]')
        //     .should('exist');
        // cy.get('[data-cy="checkout-totals-tip"]')
        //     .invoke('text')
        //     .should('include', "Tip$1.00")
    });

    it("will check that enter scan card changes the render and enter card works", ()=>{
        dispatches(orderUserDiscount);
        cy.mount(<Checkout
                register_id={1}
                is_patron={false}
                show={true}
            />, {reduxStore: store});

        cy.get('#amount') 
            .should('not.exist');    
        cy.get('[data-cy="checkout-terminal-payment"]')
            .should('not.exist')
        cy.get('[data-cy="payment-type-TERMINAL"]') //scan card
            .click();
        cy.get('#amount')
            .should('exist');
        cy.get('[data-cy="checkout-terminal-payment"]')
            .should('exist')

            
        cy.get('[data-cy="checkout-card-payment"]')
            .should('not.exist');
        cy.get('[data-cy="payment-type-1"]') //enter card
            .click()
        cy.get('[data-cy="confirmation-proceed-btn"]')
            .click();
        cy.get('[data-cy="checkout-card-payment"]')
            .should('exist');
        
        //enter values
            cy.get('#first_name')
                .should('exist')
                .type('John')
            cy.get('#last_name')
                .should('exist')
                .type('Doe')
            cy.get('#bill_address1')
                .should('exist')
                .type('1234 Main St')
            cy.get('#bill_address2')
                .should('exist')
                .type('Apt 2')
            cy.get('#bill_city')
                .should('exist')
                .type('Anytown')
            cy.get('#bill_state')
                .should('exist')
                .select('Alaska')
        //Check values
            cy.get('#first_name')
                .should('have.value', 'John')
            cy.get('#last_name')
                .should('have.value', 'Doe')
            cy.get('#bill_address1')
                .should('have.value', '1234 Main St')
            cy.get('#bill_address2')
                .should('have.value', 'Apt 2')
            cy.get('#bill_city')
                .should('have.value', 'Anytown')
            cy.get('#bill_state')
                .should('have.value', 'AK')
    })

    it("will check that the check portion renders a different view",()=>{
        dispatches(orderUserDiscount);
        cy.mount(<Checkout
                register_id={1}
                is_patron={false}
                show={true}
            />, {reduxStore: store});

        cy.get('[data-cy="checkout-check-payment"]')
            .should('not.exist');
        cy.get('[data-cy="payment-type-3"]')
            .click();
        cy.get('[data-cy="checkout-check-payment"]')
            .should('exist');
        cy.get('video.w-100')
            .should('exist');
        cy.get('[data-cy="error-check-number"]')
            .should('not.exist')
        cy.get('[data-cy="add-check-button"]')
            .should('exist')
            .click();
        cy.get('[data-cy="error-check-number"]')
            .should('exist')
        cy.get('#check_number')
            .type('1234');
        cy.get('[data-cy="error-check-name"]')
            .should('not.exist')
        cy.get('[data-cy="add-check-button"]')
            .click();
        cy.get('[data-cy="error-check-name"]')
            .should('exist')
        cy.get('#check_name')
            .type('John Doe');
        cy.get('[data-cy="add-check-button"]')
            .click();
        
    });

    it("will check the gift card payments",()=>{
        dispatches(orderUserDiscount);
        cy.mount(<Checkout
                register_id={1}
                is_patron={false}
                show={true}
            />, {reduxStore: store});

        cy.get('#card_code')
            .should('not.exist')
        cy.get('[data-cy="payment-type-4"]')
            .click()
        cy.get('#card_code')
            .should('exist')
    });

    it("will check manager discount",()=>{
        dispatches(orderUserDiscount);
        cy.mount(<Checkout
                register_id={1}
                is_patron={false}
                show={true}
            />, {reduxStore: store});
        cy.get('[data-cy="checkout-managerDiscount-payment"]')
            .should('not.exist');
        cy.get('[data-cy="payment-type-5"]')
            .click()
        cy.get('[data-cy="checkout-managerDiscount-payment"]')
            .should('exist')
        cy.get('#username')
            .should('exist')
            .click()
            .type(patronEmail)
        cy.get('#password')
            .should('exist')
            .click()
            .type(password)
        cy.get('#amount')
            .clear()
            .type(1.00)
        cy.get('#username')
            .click()
            .type(managerEmail)
        cy.get('#amount')
            .clear()
            .type(2.00)
        cy.get('[data-cy="checkout-totals-payment-0"]')
            .should('exist')
            .invoke('text')
            .should('include', "Manager Discount$2.00")
    })
})