import React, { useState, useEffect, useRef } from 'react';
import { Container, Badge } from 'react-bootstrap';

import styles from './Tags.module.scss';
const Tags = ({allTags, selectedTags, setSelectedTags, editTags=false, addTag, ...props}) => {

    const mountedRef = useRef(false);
    const [tags, setTags] = useState([...allTags]);

    useEffect(()=>{
        mountedRef.current = true;
        return ()=>{
            mountedRef.current = false;
        }
    },[]);

    useEffect(()=>{
        if(mountedRef.current && allTags){
            setTags([...allTags])
        }
    },[allTags]);

    useEffect(()=>{
        if(mountedRef.current && selectedTags && tags.length > 0){
            for(let i = 0; i < tags.length; i++){
                if(selectedTags.some(a=>a.id===tags[i].id)){
                    tags[i].checked = true;
                }
            }
        }
    },[selectedTags, tags])

    const handleTag=(tag)=>{
        let tempTags = [...tags];
        for(let i = 0; i < tempTags.length; i++){
            if(tempTags[i].id === tag.id){
                tempTags[i].checked = !tempTags[i].checked;
            }
        }
        let selected = tempTags.filter(tag => tag.checked);
        setTags(tempTags);
        setSelectedTags(selected);
        if (editTags && addTag) addTag(selected);
    }

    return (
        <Container fluid>
            <div className={styles.tags}>
                {tags?.sort((a, b) => a.name.localeCompare(b.name))?.map(tag =>(
                    <Badge
                        key={`tag-select-${tag.id}-${editTags?"edit":""}`} 
                        as="a" 
                        pill 
                        className={`badge-chip ${styles["badge-chip"]} ${tag.checked ? styles.minus : styles.plus}`}
                        onClick={()=>handleTag(tag)}
                    >
                        {tag.name} {tag.checked && <i className="far fa-check"/>}
                    </Badge>
                ))}

                {addTag &&
                    <Badge as="a" pill className={`badge-chip ${styles["badge-chip"]}`} onClick={addTag}>Add a tag <i className="far fa-plus"/></Badge>
                }
            </div>
            {props.children}
        </Container>
    )
}

export default Tags;