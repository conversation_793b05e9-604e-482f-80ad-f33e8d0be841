@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes.scss';

.micro-product-list-wrapper{
    min-width: 400px;
    max-width: 700px;
    .each-prod{
        border-bottom: 1px solid $neutral-hover-background-color;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 2px;
        cursor: pointer;
    }
    @media (max-width: 450px){
        min-width: 300px;
        max-width: 300px;
    }
    @media (max-width: 300px){
        min-width: 250px;
        max-width: 250px;
    }
}