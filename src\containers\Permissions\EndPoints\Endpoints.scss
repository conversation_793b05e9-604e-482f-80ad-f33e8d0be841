@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';
@import '../../../assets/css/scss/mixins';

.endpoint-wrapper{

    .view-endpoints{
        @include basic-flex-column;
        .filter-row{
            @include basic-flex-row;
            justify-content: space-evenly;
            margin: 1rem 0 1rem 0;
        }
        .input-col{
            @include basic-flex-column;
        }
        input:not([type="checkbox"]):not([type="radio"]), select{
            @include basic-input-select;
        }
        label{
            @include basic-label;
        }
        .each-endpoint{
            @include basic-flex-column;
            max-width: 1000px;
            padding-bottom: 5px;
            .method-slug{
                @include basic-flex-row;
                justify-content: space-between;
                margin: 1rem 0 1rem 0;
            }
            .endpoint-row{
                @include flex-row-space-between;
                padding-top: 10px;
            }
            @media(max-width:1200px){
                .endpoint-row{
                    flex-direction: column;
                    max-width: 450px;
                }
            }
            .POST{
                color: rgba(239, 115, 0, 0.86);
            }
            .GET{
                color: rgb(52, 184, 22);
            }
            .PUT{
                color: rgb(0, 150, 214);
            }
            .DELETE{
                color: rgb(157, 0, 115);
            }
            .method{
                width: 90px;
                font-weight: 700;
            }
            .slug{
                width: 300px;
            }
            .edit-btn{
                margin-right: 1rem;
                background-color: $secondary-color;
            }
            .module-row{
                margin-left: 3rem;
                @include flex-row-space-around;
                .name-url{
                    @include flex-row-space-between;
                    .name{
                        width: 200px;
                        margin-right: 1rem;
                    }
                    .url{
                        width: 250px;
                    }
                }
            }
            @media (max-width: 850px){
                .module-row{
                    @include basic-flex-column;
                    .name-url{
                        @include basic-flex-column;
                    }
                }
            }
        }
    }
    .space-between-row{
        @include flex-row-space-between;
    }
}
button.outline.btn.btn-primary{
    background-color: transparent;
    border: 2px solid $primary-color;
    color: $primary-color;
    margin-left: 2rem;
}
.new-endpoint-row{
    input[type="radio"]{
        margin: 0 .25rem 0 1.25rem;
    }
    margin-bottom: 1rem;
    button.x-btn.btn.btn-primary{
        padding: 5px !important;
        margin-left: 1rem;
    }
    .edit-btns{
        @include flex-row-space-between;
        margin-top: 2rem;
    }
    @media(max-width: 645px){
        display: flex;
        flex-direction: column;
    }
    input:not([type="checkbox"]):not([type="radio"]), select{
        @include basic-input-select;
    }
    label{
        @include basic-label;
    }
}
.assign-modules{
    input{
        width: 300px;
    }
    .rbt.has-aux{
        width: 330px;
    }
}
.edit-endpoint-modal.modal-body{
    min-height: 500px;
    max-height: 80vh;
}
.existing-endpoint-list{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
}