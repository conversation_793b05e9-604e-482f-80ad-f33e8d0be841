@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/themes.scss';

.week-display {
    display: grid;
    overflow-x: auto;
    grid-template-columns: 200px repeat(7, minmax(75px, 1fr));
    grid-template-rows: auto;
    grid-auto-rows: minmax(10px, auto);
}

.weekly-cell, .day {
    display: flex;
    justify-content: center;
}
.day, .week-topleft {
    font-weight: bold;
    padding-bottom: 8px;
}
.week-location-name {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 1.0rem;
    line-height: 36px;
}
.week-display-container {
    border: 1px solid #CCC; 
}

.form-check-input:disabled~.form-check-label, .form-check-input[disabled]~.form-check-label {
    color: $disabled-color;
}
.weekly-cell.reserved .form-check.form-radio label {
    border: 1px solid rgb(158, 158, 158);
    background-color: rgb(158, 158, 158);
    color: #CCC;
    font-size: 1.1rem;
    cursor: default;
}
