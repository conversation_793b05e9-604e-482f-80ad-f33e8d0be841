import React, { useState, useEffect } from 'react';
import { Row, Col, Table, Button } from 'react-bootstrap';
import { format } from 'date-fns';

import Items from './Items';
import APICms from '../../../../api/Cms';

const List = (props) => {
    const [submissions, setSubmissions] = useState();

    useEffect(() => {
        const _getFormSubmissions = async () => {
            const res = await APICms.forms.get({ page_id: props.id });
            setSubmissions(res?.data || []);
        };
        _getFormSubmissions();
    }, [props.id]);

    useEffect(() => {
        return () => {
            setSubmissions(null);
        };
    }, []);

    // Function to generate CSV from submissions data and trigger download
    const downloadCSV = () => {
        if (!submissions || submissions.length === 0) return;

        // Step 1: Build a set of unique keys from each "content" object, ignoring "sb_form_options"
        const contentColumnsSet = new Set();
        submissions.forEach(item => {
            if (item.content) {
                Object.keys(item.content).forEach(key => {
                    if (key !== 'sb_form_options') {
                        contentColumnsSet.add(key);
                    }
                });
            }
        });
        const contentColumns = Array.from(contentColumnsSet);
        // Prepend Date and IP Address columns
        const columns = ["Date", "IP Address", ...contentColumns];

        // Step 2: Build CSV string with header row
        let csv = columns.join(',') + '\n';

        // Step 3: Build CSV rows for each submission
        submissions.forEach(item => {
            // Process Date and IP Address first
            const dateValue = item.created_at ? format(new Date(item.created_at), "MM/dd/yyyy h:mma") : '';
            const ipValue = item.ip_address ? String(item.ip_address).trim().replace(/"/g, '""') : '';
            let row = [
                `"${dateValue}"`,
                `"${ipValue}"`
            ];
            // Then process each content column
            contentColumns.forEach(key => {
                let value = item.content ? item.content[key] : undefined;
                if (value === undefined) {
                    row.push('""');
                } else {
                    if (typeof value === 'object') {
                        value = JSON.stringify(value);
                    }
                    if (typeof value === 'string') {
                        value = value.trim();
                    }
                    value = String(value).replace(/"/g, '""');
                    row.push(`"${value}"`);
                }
            });
            csv += row.join(',') + '\n';
        });

        // Step 4: Create a Blob and trigger the download
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        const formName = props.content.title || 'form-responses';
        const sanitizedName = formName.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')  // Remove special characters
            .replace(/\s+/g, '_')         // Replace spaces with underscores
            .trim();
        a.download = `${sanitizedName}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    return (
        <>
            <Row className="mb-3">
                <Col>
                    <Button onClick={downloadCSV}>Download CSV</Button>
                </Col>
            </Row>
            <Table>
                <thead>
                    <tr>
                        <th style={{ width: "100px" }}>Date</th>
                        <th style={{ width: "150px" }}>IP Address</th>
                        <th colSpan="50">Information</th>
                    </tr>
                </thead>
                <tbody>
                    {!submissions && (
                        <tr>
                            <td colSpan="50">Loading...</td>
                        </tr>
                    )}
                    {submissions && submissions.length === 0 && (
                        <tr>
                            <td colSpan="50">No submissions found.</td>
                        </tr>
                    )}
                    {submissions && submissions.map((submission, i) => (
                        <Items key={`formsubmmit-${i}`} {...submission} />
                    ))}
                </tbody>
            </Table>
        </>
    );
};

export default List;
