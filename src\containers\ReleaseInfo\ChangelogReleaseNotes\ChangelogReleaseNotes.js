import React from 'react';

import './ChangelogReleaseNotes.scss';
import Accordion from '../../../components/common/Accordion';

const fixIcon = <i className="far fa-bug" />
const featureIcon = <i className="far fa-sparkles" />
const codeStyleIcon = <i className="far fa-code" />
const refactorIcon = <i className="far fa-stroopwafel" />
const revertIcon = <i className="far fa-trash-undo" />
const testIcon = <i className="far fa-tachometer-alt-fastest" />

const handleNotes=(note, i)=>{
    if (i === 0) return {};
    else{
        let modifiedNote = {
            title: `${note?.date} - ${note?.version}`,
            content:note?.section.map((section, j)=>(
                <React.Fragment key={`release-notes-section-${i}-${j}`}>
                    <h6>
                        {section.title === "Bug Fixes" && fixIcon}
                        {section.title === "Features" && featureIcon}
                        {section.title === "Code Style" && codeStyleIcon}
                        {section.title === "Refactor" && refactorIcon}
                        {section.title === "Revert" && revertIcon}
                        {section.title === "Tests" && testIcon}
                        {" "}{section.title}
                    </h6>
                    {section.notes.map((note, k)=>(
                        <li key={`each-note-${i}-${j}-${k}`}>
                            {note}
                        </li>
                    ))}
                </React.Fragment>
            ))
        }
        
        return modifiedNote
    }
}

export const ChangelogReleaseNotes = (props) => {
    return (
        <div className="changelog-release-notes-wrapper" data-cy="changelog-notes">
            {props?.notes?.map((note, i)=>(
                <React.Fragment key={`release-notes-${i}`}>
                {i === 0 &&
                    <>
                        <h5>
                            {note?.date} - {note?.version}
                        </h5>
                        {note?.section.map((section, j)=>(
                            <React.Fragment key={`release-notes-section-${i}-${j}`}>
                                <h6>
                                    {section.title === "Bug Fixes" && fixIcon}
                                    {section.title === "Features" && featureIcon}
                                    {section.title === "Code Style" && codeStyleIcon}
                                    {section.title === "Refactor" && refactorIcon}
                                    {section.title === "Revert" && revertIcon}
                                    {section.title === "Tests" && testIcon}
                                    {" "}{section.title}
                                </h6>
                                {section.notes.map((note, k)=>(
                                    <li key={`each-note-${i}-${j}-${k}`}>
                                        {note}
                                    </li>
                                ))}
                            </React.Fragment>
                        ))}
                    </>
                }
                <div>
                    <Accordion items={[handleNotes(note, i)]} />
                </div>
                </React.Fragment>
            ))}
        </div>
    )
}
