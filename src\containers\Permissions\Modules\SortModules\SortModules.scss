@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/themes';
@import '../../../../assets/css/scss/mixins';

.sort-modules{
    .modules-list {
        font-size: 1.0rem;
        margin: 1rem 0;
    }
    .module-content {
        margin: 2rem 1rem;
    }
    /* .module-content .form-check {
        margin: 5px 0;
    } */
    .module-content .form-group {
        margin-bottom: 1.5rem;
    }
    h6 {
        margin-top: 1rem;
    }
    .MuiTreeItem-label a {
        width: 100%;
    }
    .MuiTreeItem-label a:hover {
        background-color: rgba(0, 0, 0, 0.04);
        cursor: pointer;
    }
    .MuiTreeItem-content:hover {
        cursor: auto;
    }
    .MuiTreeItem-root.Mui-selected > .MuiTreeItem-content .MuiTreeItem-label, .module-content .MuiTreeItem-label:hover {
        background: none !important;
    }
    .MuiTreeItem-label a, .MuiTreeItem-label .description {
        padding-left: 10px;
    }
}