import React, { useState, useEffect, Suspense } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Container, Col, Row, Button, Card, } from "react-bootstrap";
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import SubHeader from '../../../../components/common/SubHeader';
import Permissions from '../../../../api/Permissions';
import DnDSortableTree from '../../../../components/common/DnDSortableTree';
import Stack from '../../../../components/common/Stack';

import './SortModules.scss';
import '../../../../components/common/Skeleton.scss';

export const SortModules = (props) => {
    let history = useHistory();

    const [ modules, setModules ]=useState([]);
    const [ loading, setLoading ]=useState(true);
    const moduleTreeEndpoint = Permissions.Modules.moduleSortOrder
    const adminDash = JSON.parse(localStorage.getItem("adminDash"))

	useEffect(() => {
        let mounted = true;
        
        if(modules.length===0){
            setLoading(true)
            Permissions.Modules.get() // get list of all modules
            .then(response => {
                if(mounted) {
                    setModules(response.data)
                }
            })
            .catch(e => console.error(e));
            setLoading(false);
        }

        return () => {
            mounted = false;
        }
	}, [props, modules]);

    useEffect(()=>{
        if(modules.length>0){
            setLoading(false)
        }
    },[modules])

    function newButtonHandler() {
        history.push("/p/module/new");
    };

    // create breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/" }, text: "Home" } 
    ];

    // if adminDash is true, add admin breadcrumbs
    if(adminDash){
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" })
    }

    // add module dashboard breadcrumb and sort modules active
    breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/module/dashboard" }, text: "Module Dashboard" });
    breadcrumbs.push({ text: "Sort Modules" });
    

    return (
        <Container fluid className="full-height">
            <SubHeader items={breadcrumbs} />
            <Row className="body">
                <Col>
                    <Card className="content-card">
                        <Stack direction="horizontal" gap={2}>
                            <h4 className="tm-1 section-title order-2 order-lg-1">Module Management</h4>
                            <div className="ms-sm-auto order-1 order-lg-2">
                                <Button variant="primary" onClick={newButtonHandler}>New Module</Button>
                            </div>
                        </Stack>
                        <hr/>
                                {loading ?
                                    <SkeletonTheme color="#e0e0e0">
                                        <div className="mt-3">
                                            <Skeleton height={100} />
                                            <div className="skeleton-border">
                                                <Skeleton height={45} width={50} count={5} className="skele-col" />
                                                <Skeleton height={45} width={300} count={5} className="skele-col" />
                                            </div>
                                        </div>
                                    </SkeletonTheme>
                                :
                                    <DnDSortableTree 
                                        propData={modules} 
                                        setModules={setModules}
                                        id={"module_id"}
                                        endpoint={moduleTreeEndpoint}
                                        refreshUrl={'/module/dashboard'}
                                    />
                                }
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};