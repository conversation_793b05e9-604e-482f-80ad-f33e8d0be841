{"data": [{"id": 1, "parent_id": null, "name": "Administrative Menu", "description": null, "url": null, "icon": null, "module_type_id": 4, "component_url": null, "roles_associated": [1], "users_associated": [], "sort_order": 13, "company_id": null, "feature": null, "permission_levels": [], "default_menu_item": {"id": 1, "text": "Administrative Menu", "icon": "", "parent_id": null, "sort_order": 20}, "endpoints": []}, {"id": 2, "parent_id": null, "name": "Home", "description": "All the pieces will need to be a Widget", "url": "/p/", "icon": "far fa-home", "module_type_id": 1, "component_url": "containers/Home", "roles_associated": [1, 2, 3, 4, 5, 6, 7], "users_associated": [], "sort_order": 1, "company_id": null, "feature": {"id": 1, "name": "Required"}, "permission_levels": [], "default_menu_item": {"id": 2, "text": "Dashboard", "icon": "far fa-home", "parent_id": null, "sort_order": 1}, "endpoints": [{"id": 362, "slug": "/company/company/{company_id}", "method": "GET"}, {"id": 417, "slug": "/user/module_permission", "method": "POST"}, {"id": 6, "slug": "/group[/group_id}]", "method": "GET"}, {"id": 423, "slug": "/routes", "method": "GET"}, {"id": 28, "slug": "/company_config", "method": "POST"}, {"id": 242, "slug": "/user/user[/{id}]", "method": "GET"}]}, {"id": 3, "parent_id": null, "name": "Users - Folder", "description": "", "url": null, "icon": "far fa-users", "module_type_id": 4, "component_url": null, "roles_associated": [1, 2, 3, 4, 5], "users_associated": [], "sort_order": 6, "company_id": null, "feature": null, "permission_levels": [], "default_menu_item": {"id": 3, "text": "Users", "icon": "far fa-users", "parent_id": null, "sort_order": 11}, "endpoints": []}, {"id": 4, "parent_id": 3, "name": "User Dashboard", "description": "", "url": "/p/users/dashboard", "icon": "", "module_type_id": 1, "component_url": "containers/User/Dashboard", "roles_associated": [1, 2, 3, 4, 5], "users_associated": [], "sort_order": 1, "company_id": null, "feature": {"id": 7, "name": "User Management"}, "permission_levels": [], "default_menu_item": {"id": 4, "text": "User Dashboard", "icon": "", "parent_id": 3, "sort_order": 1}, "endpoints": [{"id": 274, "slug": "/user/list", "method": "POST"}]}, {"id": 5, "parent_id": 3, "name": "Subscriptions Dashboard", "description": "", "url": "/p/users/subscriptions", "icon": "", "module_type_id": 1, "component_url": "containers/User/Subscriptions", "roles_associated": [3, 4, 5], "users_associated": [], "sort_order": 2, "company_id": null, "feature": {"id": 10, "name": "Products"}, "permission_levels": [], "default_menu_item": {"id": 5, "text": "Subscriptions Dashboard", "icon": "", "parent_id": 3, "sort_order": 2}, "endpoints": []}, {"id": 6, "parent_id": null, "name": "Company Admin", "description": "", "url": null, "icon": "far fa-store-alt", "module_type_id": 4, "component_url": null, "roles_associated": [1, 2], "users_associated": [], "sort_order": 7, "company_id": null, "feature": null, "permission_levels": [], "default_menu_item": {"id": 6, "text": "Company Admin", "icon": "far fa-store-alt", "parent_id": null, "sort_order": 20}, "endpoints": []}, {"id": 8, "parent_id": 6, "name": "Locations Dashboard", "description": "", "url": "/p/locations/dashboard", "icon": "", "module_type_id": 1, "component_url": "containers/Location/Dashboard", "roles_associated": [1, 2], "users_associated": [], "sort_order": 2, "company_id": null, "feature": {"id": 6, "name": "Multi-Locations"}, "permission_levels": [], "default_menu_item": {"id": 8, "text": "Locations Dashboard", "icon": "", "parent_id": 6, "sort_order": 3}, "endpoints": [{"id": 108, "slug": "/location", "method": "POST"}]}, {"id": 13, "parent_id": 12, "name": "Products Dashboard", "description": "View all Products", "url": "/p/products/dashboard", "icon": "", "module_type_id": 1, "component_url": "containers/Product/Dashboard", "roles_associated": [1, 2, 3, 4, 5], "users_associated": [], "sort_order": 1, "company_id": null, "feature": {"id": 10, "name": "Products"}, "permission_levels": [], "default_menu_item": {"id": 13, "text": "Products Dashboard", "icon": "", "parent_id": 12, "sort_order": 1}, "endpoints": [{"id": 350, "slug": "/product/type", "method": "GET"}, {"id": 170, "slug": "/product", "method": "POST"}, {"id": 1, "slug": "/category", "method": "POST"}]}, {"id": 21, "parent_id": 12, "name": "Categories", "description": "", "url": "/p/products/categories/dashboard", "icon": "", "module_type_id": 1, "component_url": "containers/Product/Category/Dashboard", "roles_associated": [1, 2, 3, 4, 5], "users_associated": [], "sort_order": 2, "company_id": null, "feature": {"id": 10, "name": "Products"}, "permission_levels": [], "default_menu_item": {"id": 21, "text": "Categories", "icon": "", "parent_id": 12, "sort_order": 5}, "endpoints": [{"id": 1, "slug": "/category", "method": "POST"}, {"id": 4, "slug": "/category/edit", "method": "POST"}, {"id": 170, "slug": "/product", "method": "POST"}]}, {"id": 46, "parent_id": 45, "name": "Discount Dashboard", "description": "", "url": "/p/discount/dashboard", "icon": "", "module_type_id": 1, "component_url": "containers/Coupons/Dashboard", "roles_associated": [1, 2, 3, 4, 5], "users_associated": [], "sort_order": 1, "company_id": null, "feature": {"id": 13, "name": "Discounts"}, "permission_levels": [], "default_menu_item": {"id": 46, "text": "Discount Dashboard", "icon": "", "parent_id": 45, "sort_order": 1}, "endpoints": [{"id": 1, "slug": "/category", "method": "POST"}, {"id": 32, "slug": "/event", "method": "POST"}, {"id": 170, "slug": "/product", "method": "POST"}, {"id": 184, "slug": "/coupon[/{coupon_id}]", "method": "GET"}, {"id": 333, "slug": "product/type", "method": "GET"}, {"id": 6, "slug": "/group[/group_id}]", "method": "GET"}]}]}