import React, { useState, useEffect, useCallback, Suspense } from 'react';
import { useHistory } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Button } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';

import { randomUUID } from '../../../../utils/cms';
import {confirm} from '../../../../components/Confirmation';
import Stack from '../../../../components/common/Stack';
import { selectCurrentEvent } from '../../../../store/selectors';
import * as actions from '../../../../store/actions';

import Info from './Info';

const Summary = (props) => {
    const {goTo, onSave, isValid, disabled} = props;

    const history = useHistory();
    const dispatch = useDispatch();

    const currentEvent = useSelector(selectCurrentEvent);
    const rootEvent = useSelector(state => state.eventwizard.master);
    const errors = useSelector(state => state.eventwizard.errors);
    const responseObject = useSelector(state => state.eventwizard.response_object);

    const [showSave, setShowSave] = useState(false);
    const [pagePart, setPagePart] = useState();
    const [pagePartSubmitErrors, setPagePartSubmitErrors] = useState();
    const [indexCounter, setIndexCounter] = useState({ });
    
    const getPointerFromString = (string) => {
        let new_string = string+"";
        return new_string==='' ? [] : new_string.split(',').map(num => parseInt(num));
    }

    const viewParentClickHandler = useCallback(() => {
        history.push(`/p/events/${rootEvent.parent_id}`);
    }, [rootEvent.parent_id, history]);

    const onClickEvent = useCallback((e, step) => {
        let pointer = getPointerFromString(e.target.value);
        dispatch(actions.setEventWizardPointer(pointer));
        goTo(step);
    }, [dispatch, goTo]);

    const onClickAddChild = useCallback((event, new_date) => {
        let pointer = getPointerFromString(event.target.value);
        let new_event = {...props.defaultEvent,
            start_datetime: new_date,
            end_datetime: new_date
        };  // BUG FIX - don't remove this, stops infinite loop
        new_event.children = [];            // BUG FIX - don't remove this, stops infinite loop

        dispatch(actions.addEventWizardChild(pointer, new_event));
        goTo("Name");
    }, [dispatch, goTo, props.defaultEvent]);

    const onClickChildDelete = useCallback(event => {
        let pointer = getPointerFromString(event.target.value);
        let name = null;
        confirm(`Are you sure you want to delete ${name ? 'event '+name : 'this event'}?`, {
            title: "Delete",
            okText: "Yes",
            cancelText: "No",
            cancelButtonStyle: "secondary"
        }).then(result => {
            if (result) {
                dispatch(actions.deleteEventWizardChild(pointer));
            }
        });
    }, [dispatch]);

    // child_index is the index of the child array
    // event_index is the counter of the individual events (including each event created by recurring)
    const printEvent = useCallback((event, errorObj=null, pointer=[], event_index=0, parent_id=null) => {
        let child_event_index = 0; 
        let recurring_event_index = event_index;

        if (event?.recurring?.create_parent===1 && errorObj) {
            errorObj = errorObj[0].children;
            recurring_event_index = 0;
        }

        const _id = randomUUID();

        const output = 
            <Info
                key={_id}
                event={event}
                pointer={pointer}
                event_index={event_index}
                recurring_event_index={recurring_event_index}
                errorObj={errorObj}
                onClickAddChild={onClickAddChild}
                onClickEvent={onClickEvent}
                onClickChildDelete={onClickChildDelete}
                isValid={isValid}
                id={_id}
                parent_id={parent_id}
            >
                {event.children?.length>0 && event.children.map((child, child_index) => {
                    let err = errorObj && errorObj[event_index] && errorObj[event_index].children ? errorObj[event_index].children : null;
                    let childEvent = printEvent(child, err, [...pointer, child_index], child_event_index, _id);
                    child_event_index = childEvent.next_index; //increment by the number of events returned from the child
                    return childEvent.output;
                })}
            </Info>
        // recurring events already increment - for all others just add one
        // ignore recurring increment if create_parent because they are located within parent container in return obj
        event_index = (!event.is_recurring || event.recurring?.create_parent===1) ? event_index+1 : recurring_event_index;
        return { next_index: event_index, event, output };
    }, [onClickAddChild, onClickChildDelete, onClickEvent, isValid]);

    useEffect(() => {
        setIndexCounter({});
        setPagePart(
            <Suspense fallback={             
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{marginBottom:"1rem"}} />
                    <Skeleton height={12} count={5} />
                </SkeletonTheme>
            }>
                {/* Print all the events recursively */}
                {printEvent(rootEvent, responseObject).output}                
            </Suspense>
        );
    },[rootEvent, responseObject, printEvent]);

    useEffect(() => {
        // returns true if itself or any of its children have an error message
        function findErrors(returnArray) {
            let hasErrors = false;
            if (Array.isArray(returnArray)) returnArray.forEach(object => {
                if (object.errors) {
                    hasErrors = true;
                } else if (findErrors(object.children)) {
                    hasErrors = true;
                }
            });
            return hasErrors;
        }
        if (findErrors(responseObject)) {
            setPagePartSubmitErrors(
                <div className={`err`}>
                    Cannot create events: There is a conflict with one or more of your events. Please make changes to the highlighted dates/times.
                </div>
            );
        } else setPagePartSubmitErrors();
    },[responseObject, rootEvent]);

    useEffect(() => {
        if (onSave) setShowSave(isValid("_last", false, true));
    },[isValid, onSave, currentEvent]);

    return (
        <Row>
            <Col className="wizard">
                <div className={`d-flex ${+rootEvent?.parent_id>0 ? "flex-column": "flex-row"} flex-wrap align-items-center justify-content-center`}>
                    <span className="title">Summary</span>
                    <Stack direction="horizontal" gap={3} className="justify-content-end ms-auto">
                        {+rootEvent?.parent_id>0 &&
                            <Button variant="light" className="me-2" onClick={viewParentClickHandler} disabled={disabled}>View Parent Event</Button>
                        }
                        {onSave && showSave &&
                            <Button variant="primary" onClick={onSave} disabled={disabled}>Save</Button>
                        }
                    </Stack>
                </div>
                <hr/>
                {pagePart}
                {pagePartSubmitErrors}
                {/* The wizard should not be able to get to Summary if there are any validation errors but just in case... */}
                <div className={`err ${!!errors ? "" : "hidden"}`}>
                    {Object.keys(errors).map(key => errors[key] ? errors[key] : '')}
                </div>
            </Col>
        </Row>
    );
}

export default Summary;