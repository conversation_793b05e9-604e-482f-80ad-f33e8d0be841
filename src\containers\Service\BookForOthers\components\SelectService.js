import React, { useEffect} from 'react';
import { formatISO, startOfWeek, endOfWeek } from 'date-fns';

import ProductCard from '../../../../containers/POS/Items/Products';
import { BookingDescription } from '../../Components/Booking/BookingDescription';
import { ErrorDiv } from './ErrorDiv';

import { findMinMax, findDateRanges, adjustEventConflictData } from '../../../../utils/helperFunctions';
import Events from '../../../../api/Events';
import Locations from '../../../../api/Locations';
import Services from '../../../../api/Services';

export const SelectService=({
    setServices,
    services,
    activeService,
    handleError,
    setBlocks,
    dateWeek,
    setConflictEvents,
    setSelectedLocation,
    setActiveService,
    checkServiceAndUserTokens,
    userTokens,
    selectedBookings,
    errors,
    ...props
})=>{

    useEffect(() => {
        const getServices = async ()=>{
            try{
                let response = await Services.get({
                    start_date: formatISO(new Date()),
                    max_record: 99,
                    include_deleted: false,
                    search_words: null
                })
                if(response.data && response.status === 200) setServices(response.data.services);
                else if (response.errors) handleError("general", response.errors)
                else handleError("general", "Unknown error getting services")
            }catch(ex){
                console.error(ex)
            }
        }

        getServices();

    },[]);

    const handleServiceSelect=(service)=>{
        setBlocks(service);
        getLocations(service);
    }

    const getEventConflicts = async(blocks, locations)=>{
        let minMax = findMinMax(blocks);
        let list = findDateRanges(blocks, startOfWeek(dateWeek), endOfWeek(dateWeek), minMax)
        try{
            let response = await Events.getByLocationAndDate({
                location_ids: [locations[0].id],
                datetime_ranges: list,
                include_meta_events: 0
            })
            if(response.status === 200 && response.data?.length) setConflictEvents(adjustEventConflictData(locations[0].id, response.data))
            else if (response.errors) {
                handleError("general", response.errors)
                setConflictEvents(null)
            }
            else {
                handleError("general", "Unknown error getting conflicts")
                setConflictEvents(null)
            }
        }catch(ex){
            console.error(ex);
            handleError("general", ex)
        }
    }

    //get the locations for things like name after selecting the active service
    const getLocations=async(service)=>{
        service.locations = [];
        let locations;
        try{
            let response = await Locations.get() //endpoint can only do one at a time, doesn't work with an array of ids so we get them all and filter
            if(response.data && response.status === 200) {
                locations = response.data.filter(location => service.location_ids.includes(location.id));
                service.location_info = locations;
                setSelectedLocation(locations[0].id); //If there are multiple, we'll just start with the first one because it can be changed anyway
                await getEventConflicts(service.blocks, locations);
                setActiveService(service);
                checkServiceAndUserTokens(userTokens, service.products, selectedBookings)
            }
            else if (response.errors) handleError("general", response.errors)
            else handleError("general", "Unknown error getting locations")
        }catch(ex){
            handleError("general", ex)
        }
    }


    return(
        <>
            <div className="d-flex flex-wrap">
                {services?.length > 0 &&
                    services?.map((service)=>{
                        let price;
                        if (service?.products?.length === 1) price = service?.products[0]?.variants[0]?.price;
                        else if (service?.products?.length > 1) price = service?.products.map(product => product.variants[0].price);
                        return(
                            <ProductCard 
                                key={`service-${service.id}`}   
                                item={service}
                                type={0}
                                name={service?.name}
                                price={price}
                                click={()=>handleServiceSelect(service)}
                            />
                        )
                    })
                }
            </div>
            {activeService &&
                <BookingDescription 
                    service={activeService}
                    onClickBack={()=>console.log("stuff")}
                    tokens={[]}
                    linkToBundles={null}
                />
            }
            <ErrorDiv errors={errors} />
        </>
    )
}