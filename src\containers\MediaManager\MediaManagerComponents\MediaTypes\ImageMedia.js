import React, { useState, useEffect, createRef, useCallback } from "react";
import { <PERSON>, <PERSON>, Con<PERSON><PERSON>, <PERSON><PERSON>, Card } from "react-bootstrap";
import { setErrorCatcher } from "../../../../utils/validation";
import SingleUpload from "../../../../components/Uploader";
import EasyCrop from "../../../../components/EasyCrop/EasyCrop";

//src\containers\MediaManager\MediaManagerComponents\MediaPreview.js

const ImageMedia=({ activeMedia, isMobile, isNew=false, upload, ...props}) =>{

    //isNew is to dictate a brand new image
    //newImage is to dictate uploading a new one

    const windowWidth = window.innerWidth;
    const [adjustImage, setAdjustImage] = useState(false);
    const [newImage, setNewImage] = useState(false);
    const [ previewImage, setPreviewImage ]=useState(activeMedia?.url || null);

    useEffect(()=>{
        if (activeMedia?.url) {
            setPreviewImage(activeMedia.url)
        }
    },[activeMedia])

    const toggleAdjustImage = () => {
        setAdjustImage(!adjustImage)
        setNewImage(false)
    };

    const toggleNewImage = () => {
        setNewImage(!newImage)
        setAdjustImage(false)
    }

    const handleNewImage =(file, save=false)=>{
        let newImage;
        if(file instanceof FormData){
            newImage = file;
        }else{
            let newImage= new FormData();
            newImage.append('file', file[0]);
        }
        setPreviewImage(newImage);
        upload(newImage,save);
    }

    const setImg = (cropped, blob)=>{
        let croppedFile = new FormData();
        croppedFile.append('file', blob, activeMedia?.description)
        if(blob.size > 2000000){
            setErrorCatcher("Image is too large!", true)
        }else{
            setPreviewImage(cropped);
            upload(croppedFile, true);
        }
    }

    return (
        <Container fluid>
            <Row>
                <Col sm={12}>
                    <SingleUpload 
                        multiple
                        previewSrc={previewImage} 
                        DZRef={createRef()} 
                        type="image/*, .heic, .heif" 
                        maxHeight={windowWidth < 2100 ? "500px" : "600px"} 
                        minHeight="300px" 
                        minWidth="100%"
                        backgroundSize="contain" 
                        onSend={handleNewImage}
                        disableCrop
                    >
                        {!previewImage &&
                            <Card className="standout position-absolute h-100 w-100">
                                <Card.Body className="d-flex justify-content-center align-items-center">
                                    <small>Drop an image or click</small>
                                </Card.Body>
                            </Card>
                        }                        
                    </SingleUpload>
                </Col>
                {!isNew &&
                    <Col sm={12} className="mt-2">
                        {/* <Button variant="secondary" onClick={toggleAdjustImage}>Adjust</Button>
                        <label className="btn btn-secondary" htmlFor="img-upload">
                            Replace
                            <input
                                className="hidden"
                                type="file"
                                id="img-upload"
                                name="img"
                                accept="image/*"
                                onChange={(e)=>handleNewImage(e.target.files, true)}
                            /> 
                        </label> */}
                        {isMobile &&
                            <>
                                <label className="btn btn-secondary" htmlFor="img-capture">
                                    Capture
                                    <input 
                                        className="hidden"
                                        type="file" 
                                        id="img-capture" 
                                        name="img" 
                                        capture="environment" 
                                        accept="image/*" 
                                        onChange={(e)=>handleNewImage(e.target.files, true)}
                                    />
                                </label>
                                <p>
                                    <small>Capture is only on devices that allow.  </small>
                                </p>
                            </>
                        }
                    </Col>
                }
            </Row>
            {adjustImage &&
                <EasyCrop
                    cancel={() => setAdjustImage(false)}
                    img={activeMedia?.url}
                    hideShow={()=>setAdjustImage(false)}
                    setImg={setImg}
                    restrictPosition={false}
                    showSizeControls={true}
                />
            }
        </Container>
    );
}

export default ImageMedia;