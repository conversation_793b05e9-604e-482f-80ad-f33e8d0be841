{"data": [{"id": 1, "slug": "/category", "method": "POST", "modules": [{"id": 143, "name": "Assign Mo<PERSON>le Permissions to User", "url": "/p/module/assign/user/:user_id", "module_type_id": 1}, {"id": 46, "name": "Discount Dashboard", "url": "/p/discount/dashboard", "module_type_id": 1}, {"id": 47, "name": "New Discount", "url": "/p/discount/create", "module_type_id": 1}, {"id": 123, "name": "Edit Discounts", "url": "/p/discount/edit/:id", "module_type_id": 1}, {"id": 48, "name": "Upcoming Events", "url": "/p/event-register", "module_type_id": 1}, {"id": 13, "name": "Products Dashboard", "url": "/p/products/dashboard", "module_type_id": 1}, {"id": 21, "name": "Categories", "url": "/p/products/categories/dashboard", "module_type_id": 1}, {"id": 61, "name": "Addon Categories", "url": "/p/products/categories/addons", "module_type_id": 1}, {"id": 110, "name": "Create Products", "url": "/p/products/new", "module_type_id": 1}, {"id": 111, "name": "Edit Products", "url": "/p/products/:id", "module_type_id": 1}, {"id": 91, "name": "Create New Registers", "url": "/p/registers/create", "module_type_id": 1}, {"id": 92, "name": "Edit Registers", "url": "/p/registers/:id", "module_type_id": 1}]}, {"id": 2, "slug": "/category/{category_id}]", "method": "GET", "modules": [{"id": 48, "name": "Upcoming Events", "url": "/p/event-register", "module_type_id": 1}, {"id": 94, "name": "Patron Cart: Events & Services", "url": "/p/shop", "module_type_id": 1}, {"id": 95, "name": "<PERSON><PERSON>: Checkout", "url": "/p/cart", "module_type_id": 1}]}, {"id": 3, "slug": "/category/add", "method": "POST", "modules": [{"id": 56, "name": "<PERSON><PERSON> Accounts", "url": "/p/admin/merge", "module_type_id": 1}, {"id": 24, "name": "Create New Event", "url": "/p/events/wizard", "module_type_id": 1}]}, {"id": 4, "slug": "/category/edit", "method": "POST", "modules": []}, {"id": 5, "slug": "/category/delete", "method": "DELETE", "modules": [{"id": 149, "name": "Home of the Repos - Link", "url": "https://github.com/", "module_type_id": 3}, {"id": 150, "name": "Home of the Bard - Link", "url": "https://google.com", "module_type_id": 3}, {"id": 151, "name": "Google! - Folder", "url": "", "module_type_id": 4}, {"id": 152, "name": "Fold me - Folder", "url": "", "module_type_id": 4}, {"id": 153, "name": "Rawr - Folder", "url": "", "module_type_id": 4}, {"id": 154, "name": "More Noms - Folder", "url": "", "module_type_id": 4}, {"id": 141, "name": "Assign De<PERSON><PERSON>missions", "url": "/p/module/assign/default", "module_type_id": 1}]}]}