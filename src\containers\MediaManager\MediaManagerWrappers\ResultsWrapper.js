import React, { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import {Container} from 'react-bootstrap';
import GridMedia from '../MediaManagerComponents/GridMedia';
import ListMedia from '../MediaManagerComponents/ListMedia';

import APIUsers from '../../../api/Users';

const imageIcon = <i className="far fa-image" />      
const videoIcon = <i className="far fa-file-video" /> 
const docIcon = <><i className="far fa-file-alt" />   </>
const audioIcon = <i className="far fa-file-audio" /> 
const otherIcon = <i className="far fa-file-plus" />  

//src\containers\MediaManager\MediaManager.js

const ResultsWrapper = ({activeMedia, setActiveMedia, refreshMedia, setRefreshMedia, filter, multiSelect}) => {
    const user = useSelector(state => state.auth.user);

    const [ media, setMedia ]=useState([]);
    const [ gridList, setGridList ]=useState("grid");

    const getMedia = useCallback(async ()=>{
        const response = await APIUsers.getAllMedia({company_id: user.company_id, ...(filter || {})});
        if (response?.data){
            let modified = addFillerIcons(response.data)
            setMedia(modified);
        }
    },[user, filter]);

    const addFillerIcons = (media)=>{
        for (let i = 0; i < media.length; i++){
            if (media[i].media_type === 1) media[i].icon = imageIcon;
            else if (media[i].media_type === 4) media[i].icon = videoIcon;
            else if (media[i].media_type === 5) media[i].icon = docIcon;
            else if (media[i].media_type === 7) media[i].icon = audioIcon;
            else media[i].icon = otherIcon;
        }
        return media;
    }

    useEffect(()=>{
        getMedia();
    },[filter, getMedia]);

    useEffect(()=>{
        if (refreshMedia){
            getMedia();
            setRefreshMedia(false);
        }
    },[refreshMedia, getMedia, setRefreshMedia]);

    useEffect(()=>{
        return ()=>{
            setMedia([]);
        }
    },[]);

    return (
        <Container className="results-wrapper">
            <div className="results-header">
                <h6>Search Results</h6>
                <span>
                    <i 
                        className={`far fa-th-large cp ${gridList==="grid" ? "active-selected" : ""}`} 
                        onClick={()=>setGridList("grid")}
                    />
                    <i 
                        className={`far fa-list cp ${gridList==="list" ? "active-selected" : "" }`} 
                        onClick={()=>setGridList("list")}
                    />
                </span>
            </div>
            <div className="displays">
                {gridList === "grid" && 
                    <GridMedia 
                        allMedia={media} 
                        setActiveMedia={setActiveMedia} 
                        activeMedia={activeMedia}
                        multiSelect={multiSelect}
                    />
                }
                {gridList === "list" && 
                    <ListMedia 
                        allMedia={media} 
                        setActiveMedia={setActiveMedia} 
                        activeMedia={activeMedia}
                        multiSelect={multiSelect}
                    />
                }
            </div>
        </Container>
    ) 
}

export default ResultsWrapper;