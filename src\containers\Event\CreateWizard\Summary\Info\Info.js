import React from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Container, Image, Accordion, Card } from 'react-bootstrap';
import { format, formatISO } from 'date-fns';
import Stack from '../../../../../components/common/Stack';

import styles from './Info.module.scss';

const Toggle = (props) => {
    if (props.parent_id) {
        return (
            <Accordion.Toggle as={Card.Header} {...props}>
                <>
                    {props.children}
                </>
            </Accordion.Toggle>
        );
    } else return <div>{props.children}</div>;
}

const Collapse = (props) => {
    if (props.parent_id){
        return (
            <Accordion.Collapse {...props}>
                <>
                    {props.children}
                </>
            </Accordion.Collapse>
        );

    } else return <div>{props.children}</div>;
}

const Info = (props) => {
    const {editEvent = true, event, pointer=[], event_index = 0, recurring_event_index = 0, errorObj, onClickAdd<PERSON>hild, onClickEvent, onClickChildDelete, isValid, id, parent_id} = props;

    let _recurring_event_index = +recurring_event_index;
    let indent = pointer?.length || 0;    
    let errors = errorObj && errorObj[event_index] ? errorObj[event_index].errors : null;

    const conflictText = <span className="conflict-text"><i className="far fa-arrow-left"></i> CONFLICT</span>

    return (
        <>
            <Container fluid className={`wizard summary event-group-wrapper ${event.children?.length>0?"w-children":""}`}>
                <Toggle eventKey={id} parent_id={parent_id}>
                    <Row>
                        <Col>
                            <h1 className="event-name title left">{event.name ? event.name : 'Untitled Event'}</h1>
                            <div className="d-flex">
                                { !!event.is_meta && event.name && editEvent &&
                                    <Button variant="light" size="sm" className="mt-1" value={pointer.join(',')} onClick={(e) => onClickAddChild(e, formatISO(new Date(event?.start_datetime)))}><small>Add Child Event to {event.name}</small></Button>
                                }
                            </div>
                        </Col>
                        {editEvent && 
                            <Col sm="auto" className="pe-0">
                                <Button className={`btn rounded my-auto me-0 ${indent===0 ? 'hidden' : ''}`} variant="outline-light" value={pointer.join(',')} onClick={onClickChildDelete}><i className="far fa-trash-alt m-0"/></Button>
                                <Button className="btn rounded my-auto me-0" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Name")}><i className="far fa-edit m-0"/></Button>
                            </Col>
                        }
                    </Row>
                </Toggle>
                <Collapse eventKey={id} parent_id={parent_id}>
                    <Row className="summary-row">
                        <Col className="pe-0">
                            <label className="form-label">Short Description</label><br/>
                            {event.short_description}
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        <Col className="pe-0">
                            <label className="form-label">Detailed Description</label><br/>
                            <div dangerouslySetInnerHTML={{__html: event.description}} />
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        <Col sm={12} lg className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label">Type</label>
                                {isValid("Type", false, true) &&
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Type")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            {event.event_type_name || "Not selected."}
                        </Col>
                        <Col sm={12} lg className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label text-no-wrap">Age Requirement</label>
                                {isValid("Age", false, true) &&
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Age")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            {(event.max_age>0 || event.min_age>0) ? 
                                (event.min_age > 0 && !+event.max_age ? event.min_age + " years old and up" : "") +
                                (!+event.min_age && event.max_age > 0 ? "Up to " + event.max_age + " years old" : "") + 
                                (event.min_age > 0 && event.max_age > 0 ? "From " + event.min_age + " to " + event.max_age + " years old" : "")
                            : "No"}
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        {/* <Col>Requires Membership: {event.requires_membership ? "Yes" : "No"}</Col> */}
                        <Col sm={12} lg className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label">Registration</label>
                                {isValid("Registration", false, true) &&
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Registration")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            {event.requires_registration > 0 ? "Yes" : "No"}
                        </Col>
                        <Col sm={12} lg className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label">{pointer.length>0 ? "Additional " : ''}Fee(s)</label>
                                {isValid("Cost", false, true) &&
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Cost")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            {event.has_fee && (event.variants?.filter(a=>a.name!=="Default" && a.name && a.price>0)?.length>0 ? "Default " : "") + "$"+event.price}
                            {event.variants?.filter(a=>a.name!=="Default" && a.name && a.price>0)?.length>0 && ", "}
                            {event.variants && event.variants.filter(a=>a.name!=="Default" && a.name && a.price>0).map((variant, i) => (
                                <span key={`event-summary-variant-${i}`}>{i>0 ? ', ' : ''} {variant.name} (${variant.price || 0.00})</span>
                            ))}
                            {!event?.has_fee && !event?.variants && event?.product_price &&
                                <span>${event.product_price}</span>
                            }
                            {!event.has_fee && !event?.product_price && !event?.variants &&
                                <span>No</span>
                            }
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        <Col className="pe-0">
                            { !!event.is_meta ?
                                <>
                                    <Stack direction="horizontal" gap={2}>
                                        <label className="form-label">Dates</label>
                                        {isValid("Dates", false, true) &&
                                            <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Dates")}><i className="far fa-edit m-0"/></Button>
                                        }
                                    </Stack>
                                    <ul className="meta date">
                                        <li className={errors ? 'conflict' : ''}>
                                            {event.start_datetime && event.end_datetime &&
                                                <>
                                                    {format(new Date(event.start_datetime), "MM/dd/yyyy")} - {format(new Date(event.end_datetime), "MM/dd/yyyy")}
                                                </>
                                            }
                                            {errors ? conflictText : ''}
                                        </li>
                                    </ul>
                                </>
                            :
                                <>
                                    <Stack direction="horizontal" gap={2}>
                                        <label className="form-label">Dates</label>
                                        {isValid("Times", false, true) &&
                                            <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Times")}><i className="far fa-edit m-0"/></Button>
                                        }
                                    </Stack>
                                    <ul className="nonmeta date">
                                        {event.is_recurring ?
                                            <>
                                                {event.recurring?.data?.map(week => {
                                                    return Object.keys(week.dateTimes).map(key => {
                                                        let block = week.dateTimes[key];
                                                        return Object.keys(block.locations_selected).map(location_id => {
                                                            if (block.locations_selected[location_id]) {
                                                                errors = errorObj && errorObj[_recurring_event_index] ? errorObj[_recurring_event_index].errors : null;
                                                                let print_block = (
                                                                    <li key={`recurring-${location_id}-${block.start_datetime}`}
                                                                        className={errors ? 'conflict' : ''}>
                                                                        {block.start_datetime && block.end_datetime &&
                                                                            <>
                                                                                {format(new Date(block.start_datetime), "eeee MM/dd/yyyy -- h:mm b")}
                                                                                {format(new Date(block.end_datetime), " - h:mm b ")} at {" "}
                                                                            </>
                                                                        }
                                                                        {event.locations_selected[location_id]}
                                                                        {errors ? conflictText : ''}
                                                                    </li>
                                                                );
                                                                _recurring_event_index++;
                                                                return print_block;
                                                            }
                                                            return null;
                                                        });
                                                    });
                                                })}
                                                {event.recurring?.data.length===0 &&
                                                    <li>No date/times selected.</li> // this should not happen because validation
                                                }
                                            </>
                                        :
                                            <>
                                                {event && event.start_datetime && event.end_datetime &&
                                                    <li className={errors ? 'conflict' : ''}>
                                                        {format(new Date(event.start_datetime), "eeee MM/dd/yyyy -- h:mm b")}
                                                        {format(new Date(event.end_datetime), " - h:mm b ")}
                                                        {event.hasOwnProperty("locations_selected") && 
                                                            <span>
                                                                at Location {event?.locations_selected[event?.location_id]}
                                                            </span>
                                                        }
                                                        {errors ? conflictText : ''}
                                                    </li>
                                                }
                                            </>
                                        }
                                    </ul>
                                </>
                            }
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        <Col className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label">Tags</label>
                                {isValid("Tags", false, true) && 
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Tags")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            {event?.tags?.length>0 ? event?.tags?.map(((tag, i) =>
                                <span key={`event-summary-tag-${i}`}>{i>0 ? ', ' : ''} {tag.name}</span>
                            )) : "None"}
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        <Col className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label">Custom Fields</label>
                                {isValid("CustomFields", false, true) &&
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "CustomFields")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            {event?.custom_fields?.filter(a=>a.placeholder_text)?.length>0 ? event?.custom_fields?.filter(a=>a.placeholder_text)?.map(((field, i) =>
                                <span key={`event-summary-tag-${i}`}>{i>0 ? <br /> : ''} {field.placeholder_text}</span>
                            )) : "None"}
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        <Col className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label">Groups to Invite</label>
                                {isValid("AddGroups", false, true) &&
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "AddGroups")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            {event?.groups?.length>0 ? event?.groups?.map(((group, i) =>
                                <span key={`event-summary-group-${i}`}>{i>0 ? ', ' : ''} {group.name}</span>
                            )) : "None"}
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        <Col className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label">Users to Invite</label>
                                {isValid("AddUsers", false, true) &&
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "AddUsers")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            {event?.users?.length>0 ? event?.users?.map(((user, i) =>
                                <span key={`event-summary-user-${i}`}>{i>0 ? ', ' : ''} {user.first_name} {user.last_name}</span>
                            )) : "None"}
                        </Col>
                    </Row>
                    <Row className="summary-row">
                        <Col className="pe-0">
                            <Stack direction="horizontal" gap={2}>
                                <label className="form-label">Images</label>
                                {isValid("Image", false, true) &&
                                    <Button className="btn rounded m-0 my-auto" variant="outline-light" value={pointer.join(',')} onClick={e=>onClickEvent(e, "Image")}><i className="far fa-edit m-0"/></Button>
                                }
                            </Stack>
                            <div className="d-flex flex-wrap">
                                {event?.images?.length>0 ? event?.images?.map(((image, i) =>
                                    <Image onClick={()=>onClickEvent({target:{value:pointer.join(',')}}, "Image")} key={`event-summary-image-${i}`} src={image.preview_url} thumbnail style={{width:"50px", height:"50px", objectFit:"cover"}} />
                                )) : "None"}
                            </div>
                        </Col>
                    </Row>
                </Collapse>
            </Container>
            {props.children &&
                <Accordion defaultActiveKey={props.children?.[0]?.props?.id}>
                    <Card as={Container} fluid className="pt-0" >
                        {props.children}
                    </Card>
                </Accordion>
            }
        </>
    );
}

export default Info;