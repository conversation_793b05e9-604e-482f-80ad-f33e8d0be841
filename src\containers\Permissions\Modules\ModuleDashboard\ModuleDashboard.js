import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Container, Card, Button } from 'react-bootstrap';
import { useHistory, Link } from 'react-router-dom';
import NumericInput from 'react-numeric-input2';
import SubHeader from '../../../../components/common/SubHeader';
import EndpointsTypeahead from '../../../../components/Typeahead/EndPointsTypeahead';
import FeatureTypeahead from '../../../../components/Typeahead/FeatureTypeahead';
import ModuleTypeTypeahead from '../../../../components/Typeahead/ModuleTypeTypeahead';
import Pagination from '../../../../components/common/Pagination';
import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import Tutorials from '../../../../components/Tutorials';
import { getModules } from '../../PermissionsUtils/PermissionUtils';

import Permissions from '../../../../api/Permissions';

import './ModuleDashboard.scss'

export const ModuleDashboard = () => {
    
    const history = useHistory();
    const mountedRef = useRef(false);
    const TABLE_HEADINGS=[
        {id: 1, name: "Id", col: "id"},
        {id: 2, name: "Name", col: "name"},
        {id: 3, name: "Url", col: "url"},
        {id: 4, name: "Feature", col: "feature_id"},
        {id: 5, name: "Module Type", col: "module_type_id"},
        {id: 6, name: "Endpoints", col: "endpoint_ids"},
        {id: 8, name: "Edit", col: null}
    ]

    const [ loading, setLoading ]= useState(true);
    const [ success, setSuccess ]= useState(null);
    const [ error, setError ]= useState(null);
    const [ modules, setModules ]= useState([]);
    const [ moduleTypes, setModuleTypes ]=useState([]);
    const [ searchTerm, setSearchTerm ]=useState("");
    const [ searchTypes, setSearchTypes ]=useState([1,2]);
    const [ searchHasEndpoints, setSearchHasEndpoints ]=useState(false);
    const [ searchFeatureIds, setSearchFeatureIds ]=useState([]);
    const [ searchEndpointIds, setSearchEndpointIds ]=useState([]);
    const [ maxRecords, setMaxRecords ]=useState(15);
    const [ page, setPage ]=useState(1);
    const [ totalRecords, setTotalRecords ]=useState(0);
    const [ sortCol, setSortCol ]=useState("id");
    const [ sortDir, setSortDir ]=useState("ASC");
    const adminDash = JSON.parse(localStorage.getItem("adminDash"))

    const getModulesGet=useCallback(async(params)=>{
        let response = await getModules(params);
        if(response.data && mountedRef.current) {
            setModules(response.data.modules);
            setTotalRecords(response.data.total_record_count);
            // if(response.data.total_record_count/maxRecords < page) setPage(1);
            // else setPage(response.data.this_page);
        }
        else if(response.errors) setError(<ErrorCatcher error={response.errors}/>)
        setLoading(false)
    },[])

    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current = false
    },[]);

    //only getting the modules once - search will filter and set a secondary state
    useEffect(()=>{
        
        const getTypes=async()=>{
            try{
                let response = await Permissions.getModuleTypes();
                if(response.status===200 && mountedRef.current) setModuleTypes(response.data);
                else if(response.errors) setError(<ErrorCatcher error={response.errors}/>)
            }catch(ex){console.error(ex)}
        }

        if(mountedRef.current){
            getModulesGet({
                is_paginated: true,
                max_records: maxRecords,
                sort_col: sortCol,
                sort_dir: sortDir,
                page_no: page,
                module_types: searchTypes
            });
            getTypes();
        }

    //Having no dependencies so this runs on first load.  
    //Afterwards, it will be handled by the following useEffect.  
    //This is the starting point
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[getModulesGet]);

    useEffect(()=>{
        setLoading(true)
        let params={
            is_paginated: true,
            max_records: maxRecords,
            sort_col: sortCol,
            sort_dir: sortDir,
            page_no: page,
            module_types: searchTypes,
            endpoint_ids: searchEndpointIds,
            feature_ids: searchFeatureIds,
        }
        if(searchTerm && searchTerm!=="") params.search = searchTerm;
        if(searchHasEndpoints){
            if(searchHasEndpoints === 1) params.has_endpoints=true;
            else if(searchHasEndpoints === 2) params.has_endpoints = false;
        } 

        getModulesGet(params)

    },[getModulesGet, searchTerm, searchTypes, searchHasEndpoints, searchFeatureIds, searchEndpointIds, maxRecords, page, sortDir, sortCol])
    
    useEffect(()=>{
        setPage(1)
    },[searchTerm, searchTypes, searchHasEndpoints, searchFeatureIds, searchEndpointIds])

    const handleItemsPerPage=(e)=>{
        if(!e) setMaxRecords(1);
        else setMaxRecords(e);
    }

    const sortOnClick=(type)=>{
        setSortCol(type);
        if(sortDir==="ASC") setSortDir("DESC");
        else setSortDir("ASC");
    }

    // create breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/" }, text: "Home" }
    ];

    // if adminDash is true, add the admin dashboard breadcrumb
    if (adminDash) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" });
    }

    breadcrumbs.push({ text: "Module Dashboard" });

    // create tutorials array
    const tutorials = [
        {
            tutorialSection: "Modules",
            allSectionTutorial: true,
            navigationTutorial: false,
            basicInfo: true
        }
    ];

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} tutorials={tutorials} /> 
            <Card className="content-card mod-dash-wrapper">
                <h4 className="section-title mod-dash-title">
                    Module Dashboard
                    <div>
                        <Button onClick={()=>{history.push('/p/module/new')}}>
                            Create New Module
                        </Button>
                        <Button onClick={()=>{history.push('/p/module/sort')}}>
                            Sort Modules
                        </Button>
                    </div>
                </h4>
                {error}
                <div className="searches">
                    <div className="input-col">
                        <label htmlFor="search-term">Search</label>
                        <input 
                            name="search-term" 
                            onChange={(e)=>setSearchTerm(e.target.value)} 
                        />
                    </div>
                    <div className="input-col">
                        <label htmlFor="search-types">Types</label>
                        <ModuleTypeTypeahead 
                            passSelection={(selection)=>setSearchTypes(selection.map(each=>each.id))}
                            multiple={true}
                            initialDataIds={searchTypes}
                        />
                    </div>
                    <div className="input-col">
                        <label htmlFor="has-endpoints">Has Endpoints?</label>
                        <select 
                            name="has-endpoints" 
                            onChange={(e)=>setSearchHasEndpoints(+e.target.value)} 
                        >
                            <option value={""}>Any Endpoints</option>
                            <option value={1}>Has Endpoints</option>
                            <option value={2}>No Endpoints</option>
                        </select>   
                    </div>
                    <div className="input-col">
                        <label htmlFor="search-feature-ids">Feature Ids</label>
                        <FeatureTypeahead 
                            passSelection={(selection)=>setSearchFeatureIds(selection.map(each=>each.id ))}
                            multiple={true}
                        />
                    </div>
                    <div className="input-col">
                        <label htmlFor="search-endpoint-ids">Endpoint Ids</label>
                        <EndpointsTypeahead 
                            passSelection={(selection)=>setSearchEndpointIds(selection.map(each=>each.id))}
                            multiple={true}
                        />
                    </div>
                </div>
                <table>
                    <thead>
                        <tr>
                            {TABLE_HEADINGS.map((heading)=>(
                                <th key={`heading-${heading.id}`} onClick={()=>sortOnClick(heading.col)}>
                                    <span>
                                        {heading.name}
                                    </span>
                                    {heading.col &&
                                        <>
                                            {sortCol === heading.col && sortDir === "ASC" && <i className={`ml-1 fad fa-sort-down`} />}
                                            {sortCol === heading.col && sortDir === "DESC" && <i className={`ml-1 fad fa-sort-up`} /> }
                                            {sortCol !== heading.col && <i className={`ml-1 fad fa-sort`} />}
                                        </>
                                    }
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {modules?.map((module)=>(
                            <tr className="cp" key={`module-dash-row-${module.id}`} onClick={()=>history.push(`/p/module/${module.id}`)}>
                                <td>
                                    {module?.id}
                                </td>
                                <td>
                                    {module?.name}
                                </td>
                                <td>
                                    {module?.url}
                                </td>
                                <td>
                                    {module?.feature?.name || "--"}
                                </td>
                                <td>
                                    {moduleTypes && module &&
                                        moduleTypes.find((type)=>type.id===module.module_type_id)?.name
                                    }
                                </td>
                                <td>
                                    {module?.endpoints.length > 0 ? "Yes" : "No"}
                                </td>
                                <td>
                                    <i className="far fa-pencil-alt" />
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
                <div className="footer-row">
                    <div > 
                        <label htmlFor="records-per-page">Per Page:</label>
                        <NumericInput 
                            name="records-per-page"
                            placeholder={`${maxRecords}`}
                            onChange={handleItemsPerPage}
                        />
                    </div>
                    {(totalRecords/maxRecords) > 1 && modules &&
                        <div >
                            <Pagination
                                itemsCount={totalRecords}
                                itemsPerPage={maxRecords}
                                currentPage={page}
                                setCurrentPage={setPage}
                                alwaysShown={false}
                            />
                        </div>
                    }
                </div>
            </Card>
        </Container>
    )
}
