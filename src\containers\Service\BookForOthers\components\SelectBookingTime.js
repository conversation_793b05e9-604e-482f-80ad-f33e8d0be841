import React from 'react';
import { Button } from 'react-bootstrap';
import { subWeeks, addWeeks, startOfWeek, endOfWeek } from 'date-fns';

import TimeslotGrid from '../../Booking/TimeslotGrid';
import WeeklyDateScroller from '../../../../components/common/WeeklyDateScroller';
import { BookingDescription } from '../../Components/Booking/BookingDescription';
import { ErrorDiv } from './ErrorDiv';

export const SelectBookingTime=({
    setSelectedBookings,
    checkServiceAndUserTokens,
    userTokens,
    activeService,
    setDateWeek,
    dateWeek,
    setSelectedLocation,
    selectedUser,
    selectedLocation, 
    conflictEvents,
    handleError,
    errors,
    ...props
})=>{

    const handleSlotChange=(slots)=>{
        setSelectedBookings(slots);
        checkServiceAndUserTokens(userTokens, activeService?.products, slots)
    }

    return(
        <>
            {activeService &&
                <BookingDescription 
                    service={activeService}
                    tokens={[]}
                    linkToBundles={null}
                />
            }
            <WeeklyDateScroller 
                backDisabled={false}
                smallScreen={false}
                selectedDate={null}
                onDateBack={()=>setDateWeek(subWeeks(dateWeek, 1))}
                onDateNext={()=>setDateWeek(addWeeks(dateWeek, 1))}
                onChangeDate={()=>console.log("change")}
                rangeStartDate={startOfWeek(dateWeek)}
                rangeEndDate={endOfWeek(dateWeek)}
            />
            {activeService &&
                activeService?.location_info?.map((info)=>(
                    <Button key={`location-${info.id}`} onClick={()=>setSelectedLocation(info.id)}>
                        {info?.name}
                    </Button>
                ))
            } 
            {activeService && selectedUser &&           
                <TimeslotGrid 
                    location={selectedLocation}
                    startDate={startOfWeek(dateWeek)}
                    endDate={endOfWeek(dateWeek)}
                    minHour={6}
                    maxHour={18}
                    conflictEvents={conflictEvents}              
                    service={activeService}
                    showConflictInfo={true}
                    onSlotChange={(change)=>handleSlotChange(change.selected_slots)}
                />
            }
            <ErrorDiv errors={errors} />
        </>
    )
}