import React, { useState, useEffect, useRef } from 'react';
import { Card, Container, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import AvailableProducts from './ArchiveComponents/AvailableProducts/AvailableProducts';
import CurrentList from './ArchiveComponents/CurrentList/CurrentList';
import SubHeader from '../../../components/common/SubHeader';
import usePrevious from '../../../components/common/CustomHooks';
import ArchivedList from './streamsArchive.json';
import './ArchivedStreams.scss';

export const ArchivedStreams = () => {

    const COMPANY_ID = 2;
    const streamList = ArchivedList.filter(stream => stream.company_id === COMPANY_ID)[0].archives
    const mountedRef = useRef(false);
    const screenSize = window.innerWidth;
    
    const [selectedProductId, setSelectedProductId] = useState(null);
    const [error, setError] = useState(null);
    const [purchased, setPurchased] = useState(false);
    const [showAvailableStreamEvents, setShowAvailableStreamEvents] = useState(false);

    const oldProductId = usePrevious(selectedProductId);

    useEffect(()=>{
        mountedRef.current = true

        return()=> mountedRef.current = false
    },[]);

    useEffect(()=>{
        if(oldProductId !== selectedProductId && mountedRef.current) {
            setPurchased(false);
            setShowAvailableStreamEvents(false)
        }
    },[selectedProductId, oldProductId])

    return (
        <Container fluid className="archived-streams">
            {error}
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Livestream Recordings" }
            ]} />
            <Card className="content-card">
                <h4 className="section-title">
                    Archived Streams
                </h4>
                <p>
                    If you missed a streamed event, no worries! You can view the recordings of a purchased stream here or you can purchase any stream events you may have missed to enjoy now!
                </p>
                <br />
                <div className="main-body">
                    {selectedProductId && screenSize < 500 &&
                        <Button className="new-stream-btn" onClick={()=>setShowAvailableStreamEvents(!showAvailableStreamEvents)}>
                            {showAvailableStreamEvents ? 
                                "Hide Streams"
                                :
                                "Select New Stream" 
                            }
                        </Button>
                    }
                    {(!selectedProductId || (screenSize > 500 || showAvailableStreamEvents)) &&
                        <AvailableProducts 
                            streamList={streamList} 
                            setError={setError}
                            selectedProductId={selectedProductId}
                            setSelectedProductId={setSelectedProductId}
                            purchased={purchased}
                            setPurchased={setPurchased}
                        />
                    }
                    {!selectedProductId && 
                        <div>
                            <h4>
                                Select a Stream Event
                            </h4>
                        </div>
                    }
                    {selectedProductId && 
                        <CurrentList 
                            streamList={streamList}
                            setError={setError}
                            selectedProductId={selectedProductId}
                            purchased={purchased}
                        />
                    }
                </div>
            </Card>
        </Container>
    )
}
