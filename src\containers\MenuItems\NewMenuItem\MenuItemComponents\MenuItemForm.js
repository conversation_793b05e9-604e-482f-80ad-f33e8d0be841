import React, {useState, useEffect, useRef} from 'react';
import { Button } from 'react-bootstrap';
import Permissions from '../../../../api/Permissions';

import { setSuccessToast, setErrorCatcher } from '../../../../utils/validation';

export const MenuItemForm = ({activeModule, companyId, highestSortOrder, edit, ...props}) => {

    const mountedRef = useRef(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();
    const [displayedText, setDisplayedText] = useState("");
    const [icon, setIcon] = useState("");
    const [disabled, setDisabled] = useState(false);
    const [localError, setLocalError]=useState()

    useEffect(()=>{
        mountedRef.current = true;

        return ()=>{
            mountedRef.current = false;
            setError();
            setSuccess();
            setDisplayedText("");
            setIcon("");
        }
    },[]);

    useEffect(()=>{
        if(activeModule && mountedRef.current){
            setDisplayedText(activeModule?.default_menu_item?.text || "");
            setIcon(activeModule?.default_menu_item?.icon || "");
        }
    },[activeModule]);

    useEffect(()=>{
        let disabled = false;
        let error = "";
        if(!displayedText){
            disabled = true
        }else if (displayedText.length > 45 || icon.length > 45){
            disabled = true;
            error = "You have exceeded the character limit for one or more fields"
        }
        if(mountedRef.current){
            setDisabled(disabled);
            setLocalError(error);
        }
    },[displayedText, icon])

    const cleanUp=()=>{
        setError();
        setSuccess();
        setDisplayedText("");
        setIcon("");
    }

    const saveMenuItem = async (e) => {
        e.preventDefault();
        setSuccess();
        setError();

        //have to have a difference between props.error and a local error because this works as both a modal (so the parent needs to hold it) and its own page
        if(props.setSuccess && props.setError){
            props.setSuccess()
            props.setError()
        }
        const menuItem={
            icon: icon,
            text: displayedText,
            company_id: companyId,
        }
        //to put it at the bottom of the list when it's made
        if(!edit && highestSortOrder) menuItem.sort_order = highestSortOrder
        //to make sure the menu_item_id is present when editing 
        if(activeModule?.id) menuItem.menu_item_id = activeModule.id
        else menuItem.module_id = activeModule.module_id
        try{
            let response = await Permissions.MenuItems.edit(menuItem);
            if(response.status ===200 && response.data){
                if(props.success) props.setSuccess(setSuccessToast("Menu Item Updated Successfully"))
                else setSuccess(setSuccessToast("Menu Item Updated Successfully"))
                if(props.onClose) props.onClose(true, menuItem.module_id || null)
                cleanUp();
            }else if(response.errors){
                if(props.error) props.setError(setErrorCatcher(response.errors))
                else setError(setErrorCatcher(response.errors))
            }
        }catch(ex){console.error(ex)}
    }

    return (
        <div className="menu-item-form">
            <p>
                <span className="required-star">Please Note:</span>This menu item will only be visible for users who have the permission to view this module.
            </p>
            <h5 className="section-title">
                Selected Module    
            </h5>
            {success}
            {error}
            <form onSubmit={saveMenuItem}>
                <div>
                    <p>
                        <span className="menu-label">
                            Module Name:
                        </span>
                        <span>
                            {activeModule?.name ?
                                activeModule.name
                            :
                                "---"
                            }
                        </span>
                    </p>
                    <p>
                        <span className="menu-label">
                            URL:
                        </span>
                        <span>
                            {activeModule?.url ? 
                                activeModule.url
                            :
                                "---"
                            }
                        </span>
                    </p>
                    <p>
                        <span className="menu-label">
                            Description:
                        </span>
                        <span>
                            {activeModule?.description ?
                                activeModule.description
                            :
                                "---"
                            }
                        </span>
                    </p>
                </div>
                <h5 className="section-title">
                    Menu Item
                </h5>    
                <div className="inputs">
                    <p>
                        <label htmlFor="displayed-text">
                            Displayed Text <span className="required-star">*</span>:
                        </label>
                        <input 
                            required
                            value={displayedText}
                            onChange={(e)=>setDisplayedText(e.target.value)}
                        />
                    </p>
                    <p className={`count ${displayedText.length > 45 ? "error-text" : ""}`}>
                        {displayedText.length}/45
                    </p>
                    <p>
                        <label htmlFor="icon">
                            Icon:
                        </label>
                        <input
                            value={icon}
                            onChange={(e)=>setIcon(e.target.value)}
                        />
                    </p>
                    <p className={`count ${icon.length > 45 ? "error-text" : ""}`}>
                        {icon.length}/45
                    </p>
                </div>
                {localError &&
                    <p className="error-text">
                        {localError}
                    </p>    
                }
                <Button type="submit" disabled={disabled}>{edit? "Edit" : "Add"} Menu Link</Button>
            </form>
        </div>
    )
}

export default MenuItemForm;
