import React, { useEffect, useState, useCallback } from 'react';
import { Button } from 'react-bootstrap';

import ModuleTypeahead from '../../../../components/Typeahead/ModuleTypeahead';
import usePrevious from '../../../../components/common/CustomHooks'
import '../Endpoints.scss'

//src\containers\Permissions\EndPoints\ViewEndpoints\ViewEndpoints.js
//src\containers\Permissions\EndPoints\CreateEndpoints\CreateEndpoints.js

const METHODS = ["GET", "POST", "PUT", "DELETE"]

export const NewEndpointRow = ({
    endpoint, 
    passEndpoints=()=>{}, //used for save button if delete is true, for passing up for creation
    removeEndpoint=()=>{}, //used for deleting when delete is true, for removing when creating 
    setSelectedModules=()=>{}, //used for passing up the selected modules
    editEndpoint=false, 
    validateSlug=()=>{},
    ...props}) => {

    const [ slug, setSlug ] = useState("");
    const [ method, setMethod ] = useState("");
    const previousSlug = usePrevious(slug);
    const previousMethod = usePrevious(method);

    const handleSlugValidate = useCallback((e) => {
        setSlug(validateSlug(slug))
    },[validateSlug, slug]);

    /*useEffect(()=>{
        console.log(slug)
    },[slug])*/

    useEffect(()=>{
        if(!editEndpoint && (slug!==previousSlug || method!==previousMethod)){
            passEndpoints(endpoint.tempId, slug, method)
        }
    // including the previous as a trigger just create unnecessary re-renders
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[slug, method, passEndpoints]);

    useEffect(()=>{
        if(editEndpoint && endpoint){
            setSlug(endpoint.slug)
            setMethod(endpoint.method)
        }
    },[editEndpoint, endpoint])

    return (
        <div className="new-endpoint-row">
            <input 
                data-cy="name-slug" 
                name="slug" 
                value={slug} 
                onChange={(e)=>setSlug(e.target.value)} 
                onBlur={handleSlugValidate} 
            />
            <span data-cy="check-boxes">
                {METHODS.map((methodOption, i)=>(
                    <React.Fragment key={`method-${i}`}>
                        <input 
                            checked={method===methodOption || false} 
                            type="radio" id={`method-select-${endpoint?.tempId || 0}-${methodOption}`} 
                            value={methodOption} 
                            data-cy={`${methodOption}`}
                            onChange={(e)=>setMethod(e.target.value)}
                        />
                        <label htmlFor={`method-select-${endpoint?.tempId || 0}-${methodOption}`} data-cy="method-select-label">{methodOption}</label>
                    </React.Fragment>
                ))}
            </span>
            {endpoint?.tempId !== 1 && !editEndpoint &&
                <Button className="x-btn" onClick={()=>removeEndpoint(endpoint.tempId)} data-cy="remove-endpoint-btn">
                    Remove
                </Button>
            }
            {editEndpoint &&
                <div className="mt-3" data-cy="module-typeahead">
                    <ModuleTypeahead 
                        multiple={true} 
                        initialDataIds={endpoint?.modules?.map((module=>module.id))} 
                        passSelection={(selection)=>setSelectedModules(selection)} 
                    />
                </div>
            }
            {editEndpoint &&
                <div className="edit-btns">
                    <Button data-cy="save-btn" onClick={()=>passEndpoints(endpoint.id, slug, method)}>
                        Save Changes
                    </Button>
                    <Button data-cy="delete-btn" variant="danger" onClick={()=>removeEndpoint(endpoint.id)}>
                        Delete
                    </Button>
                </div>
            }
        </div>
    )
}
