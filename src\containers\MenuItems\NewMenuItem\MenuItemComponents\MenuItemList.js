import React, { useState, useEffect, useRef } from 'react'
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';

import Pagination from '../../../../components/common/Pagination';
import { getModules } from '../../../Permissions/PermissionsUtils/PermissionUtils';
import { setErrorCatcher, setSuccessToast } from '../../../../utils/validation';
import EachMenuItem from './EachMenuItem';

export const MenuItemList = (props) => {
  
    const defaultModuleTypes = [1,3,4] //page, external, folder
    const mountedRef = useRef(false);
    const [noItem, setNoItem] = useState(false);
    const [moduleTypes, setModuleTypes]=useState(defaultModuleTypes)
    const [loading, setLoading] = useState(true);
    const [error, setError]=useState(false);
    const [search, setSearch]=useState("");
    const [maxRecords, setMaxRecords]=useState(25);
    const [totalRecords, setTotalRecords]=useState(""); 
    const [page, setPage]=useState(1);
    const [modules, setModules] = useState([]); //for display

    useEffect(()=>{
        mountedRef.current = true

        return()=> mountedRef.current = false
    },[])

    useEffect(()=>{
        const getAllModules=async()=>{
            setLoading(true)
            let parameters = {
                search: search,
                max_records: maxRecords,
                page_no: page,
                is_paginated: true,
                module_types: moduleTypes
            }
            let response = await getModules(parameters);
            if(mountedRef.current){
                if(response && response.data){
                    setModules(response.data.modules);
                    setTotalRecords(response.data?.total_record_count)
                    setLoading(false)
                }else{
                    setError(setErrorCatcher(response.errors));
                }
            }
        }

        getAllModules();
        
    },[noItem, moduleTypes, search, maxRecords, page])

    

    const handleCheckbox=()=>{
        let types = [];
        let menu = document.getElementById("menu-items-only").checked;
        if(menu) types.push(1);
        let folder = document.getElementById("folders-only").checked;
        if(folder) types.push(4);
        let external = document.getElementById("external-links-only").checked;
        if(external) types.push(3);
        setModuleTypes(types);
    }
  
    return (
        <div>
            {error}
            <p>
                <label htmlFor="search-input">
                    Search:
                </label>
                <input 
                    id="search-input"
                    onChange={(e)=>setSearch(e.target.value)}
                />
            </p>
            <div>
                <p>
                    <input
                        type="checkbox"
                        id="menu-items-only"
                        onChange={handleCheckbox}
                        />
                    <label htmlFor="menu-items-only">Pages</label>
                    <input
                        type="checkbox"
                        id="external-links-only"
                        onChange={handleCheckbox}
                        />
                    <label htmlFor="external-links-only">External Links</label>
                    <input
                        type="checkbox"
                        id="folders-only"
                        onChange={handleCheckbox}
                    />
                    <label htmlFor="folders-only">Folders</label>
                </p>
                <p className="pagination-p"> 
                    <Pagination 
                        itemsCount={totalRecords}
                        itemsPerPage={maxRecords}
                        currentPage={page}
                        setCurrentPage={setPage}
                        alwaysShown={true}
                    />
                </p>
            </div>
            <div className="select-module">
                {loading ? 
                    <SkeletonTheme color="#e0e0e0">
                        <div className="mt-3 text-center">
                            <Skeleton height={16} count={10} style={{marginTop: "5px", paddingRight:"15px;" }}/>
                        </div>
                    </SkeletonTheme>
                :
                    <>
                        {modules?.map((module)=>(
                            <EachMenuItem 
                                item={module} 
                                setActiveItem={props.setActiveModule} 
                                activeItem={props.activeModule} 
                                key={`each-menu-item-${module.id}`}
                            />
                        ))}
                    </> 
                }
            </div>
        </div>
    )
}

export default MenuItemList;