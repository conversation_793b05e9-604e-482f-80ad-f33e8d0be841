import React, {useEffect, useState, useRef} from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, Button } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { Link } from 'react-router-dom';
import SubHeader from '../../../../components/common/SubHeader';
import { handleFeatureChecks, getFeaturesByPermissions } from '../../PermissionsUtils/PermissionUtils';
import { CompanyTypeahead } from '../../../../components/Typeahead/CompanyTypeahead';
import { EachRow } from './EachRow';
import { setErrorCatcher, setSuccessToast } from '../../../../utils/validation';
import usePrevious from '../../../../components/common/CustomHooks';

import Permissions from '../../../../api/Permissions';

import '../CompanyFeatures.scss'

const TABLE_HEADINGS = ["Name", "Default", "Purchased", "Override", "Has Permission"]

export const ViewAssignFeatures = ({company, ...props}) => {

    const mountedRef = useRef(false);
    const [ activeCompany, setActiveCompany ]=useState([]);
    const [ currentChecks, setCurrentChecks ]=useState([]);
    const [ features, setFeatures ]=useState([]);
    const [ loading, setLoading ]=useState(false);
    const [ error, setError ]=useState();
    const [ success, setSuccess ]=useState();
    const oldActiveCompany = usePrevious(activeCompany);
    const adminDash = JSON.parse(localStorage.getItem("adminDash"))
    
    useEffect(()=>{
        mountedRef.current = true
        
        if(adminDash && !company && mountedRef.current) setActiveCompany(adminDash.selectedCompany)
        
        return ()=> mountedRef.current = false
    //adding adminDash here creates an endless loop ... not entirely sure why
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[])

    useEffect(()=>{
        if(company) setActiveCompany([company])
    },[company])

    // useEffect(()=>{
    // },[adminDash])

    useEffect(()=>{
        if(activeCompany?.length > 0 && oldActiveCompany !== activeCompany){
            setLoading(true);
            const getFeaturesGet = async () =>{
                let response = await getFeaturesByPermissions(activeCompany[0].id);
                if (response && mountedRef.current) {
                    setFeatures(response.data);
                    setLoading(false);
                }
                else {
                    setError(setErrorCatcher(response.errors))
                    setLoading(false);
                }
            }

            const getAllFeatures = async()=>{
                let response = await Permissions.Features.getAll({include_product_variants: true});
                console.log(response)
            }

            getFeaturesGet();
            getAllFeatures();
        }
        else if(activeCompany.length === 0 ) setFeatures([])
    },[activeCompany, oldActiveCompany]);

    const sendCheckUp=(id, checked)=>{
        let currentIdsAndChecks = handleFeatureChecks(id, checked, currentChecks);
        setCurrentChecks(currentIdsAndChecks);
    }

    const findCurrentCheck = (id) => {
        let value = null;
        // check the current checks
        let currentCheck = currentChecks.find((check)=>check.id===id);
        if (currentCheck) return currentCheck.checked;

        // if not found, check the features
        let feature = features.find((feature)=>feature.id===id);
        if (feature) {
            if (feature.company_override===null) value = null;
            else if (!!feature.company_override) value = true;
            else value = false;
        }

        return value;
    }

    const saveOverrides=()=>{
        setSuccess();
        setError();
        let tempFeatures = [];
        for(let i=0; i < currentChecks.length; i++){
            let value = !!currentChecks[i].checked;
            if (currentChecks[i].checked===null) value = null;
            tempFeatures.push({
                feature_id: currentChecks[i].id,
                is_enabled: value,
            })
        }
        submitOverrides(tempFeatures)
    }

    const submitOverrides= async(tempFeatures)=>{
        try{
            let response = await Permissions.Features.assign({company_id: activeCompany[0].id, features: tempFeatures})
            if(response.status===200 && response.data){
                setSuccess(setSuccessToast("Overrides submitted successfully"))
                afterSubmission();
            }
        }catch(ex){
            setError(setErrorCatcher("There was an error submitting overrides"))
        }
    }

    const afterSubmission=async()=>{
        setLoading(true);
        let response = await getFeaturesByPermissions(activeCompany[0].id);
        if (response && mountedRef.current) {
            setFeatures(response.data);
            setLoading(false);
        }
        else {
            setError(setErrorCatcher(response.errors))
            setLoading(false);
        }
    }

    // create the breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" }
    ];

    if (adminDash) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" })
    }

    breadcrumbs.push({ text: "Assign Features" });
    
    return (
        <Container fluid>
            {!company &&
                <SubHeader items={breadcrumbs} />
            }
            <Card className="modal-card company-features">
                <div className="view-assign-features">
                    <h4 className="section-header">
                        View and Assign Features {activeCompany.length > 0 && `for ${activeCompany[0]?.name}`}
                    </h4>
                    <p>
                        Any assigned overrides will be applied even if the default permissions are changed and will have to be removed manually.
                    </p>
                    {error}
                    {success}
                    {loading ? 
                        <SkeletonTheme color="#e0e0e0">
                            <div className="mt-3">
                                <Skeleton height={16} count={8} />
                            </div>
                        </SkeletonTheme>
                    :
                        <>
                            {!company && <CompanyTypeahead multiple={false} passSelection={(selection)=>setActiveCompany(selection)}/>}
                            {activeCompany.length > 0 && 
                                <>
                                    <table>
                                        <thead>
                                            <tr>
                                                {TABLE_HEADINGS.map((heading, i)=>(
                                                    <th key={`table-heading-${i}`}>
                                                        {heading}
                                                    </th>
                                                ))}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {features?.map((feature, i)=>(
                                                <EachRow key={`company-assign-row-${i}`} feature={feature} sendCheckUp={sendCheckUp} currentCheck={findCurrentCheck(feature.id)} />
                                            ))}
                                        </tbody>
                                    </table>
                                    <Button onClick={saveOverrides}>Save</Button>
                                </>
                            }
                        </>    
                    }
                </div>
            </Card>
        </Container>
    )
}