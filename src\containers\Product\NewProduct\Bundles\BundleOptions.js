import React, { useState, useEffect, useRef, useCallback } from 'react'

import { AvailableBundlesTypeahead } from'../../../../components/Typeahead';
import { ServiceTokenTypeaheads } from '../../../../components/Typeahead';

import './Bundles.scss'

//called in src\containers\Product\NewProduct\Bundles\Bundles.js

export const BundleOfTokens = ({selectedTokens, passSelection, setIncludeToken}) => {

    const mountedRef = useRef(false);
    const [includeBundles, setIncludeBundles]=useState(false);
    const [firstLoad, setFirstLoad]=useState(true);

    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current=false
    },[]);

    useEffect(()=>{
        if(mountedRef.current) setIncludeToken(includeBundles);
    },[includeBundles, setIncludeToken]);

    useEffect(()=>{
        if(!includeBundles && mountedRef.current) passSelection([]);
    }, [includeBundles, passSelection])

    useEffect(()=>{
        if(firstLoad && mountedRef.current && selectedTokens.length > 0 && setIncludeToken) {
            setIncludeBundles(true);
            setFirstLoad(false);
        }
    },[setIncludeToken, selectedTokens, firstLoad])

    return (
        <div className="bundle-tokens-wrapper">
            <span className="product-flex-col" data-cy="new-product-select-bundles-token-bundles">
                <span className="token-label">Does this subscription include a bundle of tokens.</span>
                <span className="y-n-input">
                    <label htmlFor="sub-bun-y">Yes</label>
                    <input 
                        checked={includeBundles}
                        type="radio" 
                        name="sub-bun-yn" 
                        id="sub-bun-y"
                        value="yes"
                        onChange={()=>setIncludeBundles(true)}
                    />
                    <label htmlFor="sub-bun-n">No</label>
                    <input 
                        checked={!includeBundles}
                        type="radio" 
                        name="sub-bun-yn" 
                        id="sub-bun-n"
                        value="no"
                        onChange={()=>setIncludeBundles(false)}
                    />
                </span>
            </span>
            {includeBundles && 
                <div className='bundles-typeahead' data-cy="new-product-bundle-typeahead">
                    <label htmlFor="available-bundle-typeahead">Bundles<span className="required">*</span></label>
                    <AvailableBundlesTypeahead 
                        name="available-bundle-typeahead"
                        multiple={false}
                        passSelection={passSelection}
                        initialData={selectedTokens}
                    />
                </div>
            }
        </div>
    )
};

export const BundleOfServiceTokens=({selectedTokens, quantity, setQuantity, passSelection, setIncludeToken})=>{

    const mountedRef = useRef(false)
    const [isServiceTokenBun, setIsServiceTokenBun]=useState(false);
    const [firstLoad, setFirstLoad]=useState(true);

    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current =false
    },[]);

    useEffect(()=>{
        if(mountedRef.current) setIncludeToken(isServiceTokenBun);
    },[isServiceTokenBun, setIncludeToken]);

    useEffect(()=>{
        if(!isServiceTokenBun && mountedRef.current) passSelection([]);
    },[isServiceTokenBun, passSelection])

    useEffect(()=>{
        if(firstLoad && mountedRef.current && selectedTokens.length >0 && setIncludeToken) {
            setIsServiceTokenBun(true);
            setFirstLoad(false);
        }
    },[firstLoad, selectedTokens, setIncludeToken]);

    return(
        <div className="bundle-service-tokens-wrapper">
            <span className="product-flex-row">
                <span className="product-flex-col" data-cy="new-product-select-bundles-service-token">
                    <span className="token-label">Is this a bundle of service tokens?</span>
                    <span className="y-n-input">
                        <span>
                            <label htmlFor="sub-bun-y">Yes</label>
                            <input 
                                checked={isServiceTokenBun}
                                type="radio" 
                                name="sub-bun-yn" 
                                id="sub-bun-y"
                                value={true}
                                onChange={()=>setIsServiceTokenBun(true)}
                            />
                        </span>
                        <span>
                            <label htmlFor="sub-bun-n">No</label>
                            <input 
                                checked={!isServiceTokenBun}
                                type="radio" 
                                name="sub-bun-yn" 
                                id="sub-bun-n"
                                value={false}
                                onChange={()=>setIsServiceTokenBun(false)}
                            />
                        </span>
                    </span>
                </span>
                {isServiceTokenBun ?
                    <>
                        <span className="product-flex-col token-typeahead">
                            <label htmlFor="service-token-typeahead" data-cy="new-product-bundle-typeahead">Token<span className="required">*</span></label>
                            <ServiceTokenTypeaheads
                                multiple={false}
                                passSelection={passSelection}
                                initialData={selectedTokens}
                            />
                        </span>
                        <span className="product-flex-col qty-input" data-cy="new-product-token-quantity">
                            <label htmlFor="token-quality">Quantity<span className="required">*</span></label>
                            <input 
                                name="token-quantity"
                                className="focus-glow"
                                value={quantity} 
                                type="number" 
                                onChange={(e)=>setQuantity(e.target.value)} 
                            />
                        </span>
                    </>
                    :
                    null
                }
            </span>
            {isServiceTokenBun ?
                <>
                    <div className="token-footnote">
                        {selectedTokens?.length > 0 && quantity &&
                            <p>
                                This token costs ${selectedTokens[0]?.product_variants[0]?.price} each.  
                                The full price for buying {quantity} token{+quantity >1 ? <span>s</span>:null} individually would be ${(selectedTokens[0]?.product_variants[0]?.price * quantity).toFixed(2)}
                            </p>
                        }
                    </div>
                </>
                :
                null
            }
        </div>
    )
};