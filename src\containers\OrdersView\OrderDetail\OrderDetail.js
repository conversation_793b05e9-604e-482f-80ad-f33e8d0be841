import React, {useState, useEffect, useCallback, useRef} from 'react';
import { useParams } from "react-router-dom";
import { useSelector, useDispatch} from 'react-redux';
import { Button } from 'react-bootstrap';
import { format } from 'date-fns';

import ErrorCatcher from "../../../components/common/ErrorCatcher";
import OrderSummary from "../../../components/OrderSummary";
import Pos from '../../../api/Pos';

export const OrderDetail = ({ orderId, customStatuses=null, onSelectStatus=()=>{}, hideDate=false }) => {

    const mountedRef = useRef(false);

    const [orderFullDetails, setOrderFullDetails] = useState();
    const [error, setError] = useState();

	useEffect(() => {
		mountedRef.current = true;

        return () => {
            mountedRef.current = false;
        }
	// eslint-disable-next-line react-hooks/exhaustive-deps
	},[]);

    // get the full order details from the server
    useEffect(() => {
        if (orderId) {
            Pos.order.get({
                method: "GET",
                id: orderId
            })
            .then( async response => {
                if(!response.errors && mountedRef.current){
                    setOrderFullDetails(response.data);
                }else if(response.errors){
                    console.error(response.errors);
                    setError("There was a problem retrieving order #"+orderId);
                }
            });
        }
    }, [orderId]);

    const modalStatusButton = useCallback((status) => {
        let isCurrentStatus = orderFullDetails.order_status_history.slice(-1)[0].order_custom_status_id === status.id;
        return (
            <Button key={`status-button-${status.id}`} disabled={isCurrentStatus} onClick={()=>onSelectStatus(orderFullDetails.id, status.id)}>{status.name}</Button>
        )
    }, [orderFullDetails, onSelectStatus]);

    if (!orderFullDetails) {
        return (<></>);
    }

    return (
        <>                                  
            <OrderSummary
                order={orderFullDetails}
                timeTitle={"Order Placed:"}
                userTitle="Customer Name:"
                showItems={true}
                showStatusHistory={true}
                hideDate={hideDate}
            />
            {customStatuses &&
                <div className="status-buttons">
                    <span>Active:</span>
                    {customStatuses.filter(custom_status => !!custom_status.is_done===false)
                    .map(custom_status => (
                        modalStatusButton(custom_status)
                    ))}
                    <span>Done:</span>
                    {customStatuses.filter(custom_status => !!custom_status.is_done===true)
                    .map(custom_status => (
                        modalStatusButton(custom_status)
                    ))}
                </div>
            }
        </>
    )
}