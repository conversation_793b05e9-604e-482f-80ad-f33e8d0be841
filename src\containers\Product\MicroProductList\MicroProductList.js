import React, {useState, useEffect, useCallback, useRef} from 'react'
import {useHistory} from 'react-router-dom'
import Skeleton, {SkeletonTheme} from 'react-loading-skeleton'

import Tooltip from '../../../components/common/Tooltip';
import Products from '../../../api/Products';
import './MicroProductList.scss'

const PRODUCT_STATUSES=[
    {id: 1, name: "Active", className: "sub-active"},
    {id: 2, name: "Pending", className: "sub-suspended"},
    {id: 3, name: "Not Available", className: "sub-cancelled"},
    {id: 4, name: "Removed", className: "sub-expired"}
]

export const MicroProductList = ({category, catName="", ...props}) => {

    const history = useHistory();
    const mountedRef = useRef(false)
    const [products, setProducts]=useState([]);
    const [error, setError]=useState("");
    const [loading, setLoading]=useState(false);

    const getProducts=useCallback(async()=>{
        setError("")
        try{
            let response = await Products.getFiltered({
                categories:[category],
                sort_col: "product_type_id"
            })
            if(mountedRef.current && !response.errors){
                setProducts(response.data.products)
                if(response?.data?.product?.length===0) setError("There are no products in this category")
                setLoading(false)
            }else if(response.errors && mountedRef.current){
                setError("Error Retrieving Products")
                setLoading(false)
            }
        }catch(ex){
            console.error(ex)
            setLoading(false)
        }
    },[category])
    
    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current = false
    },[])

    useEffect(()=>{
        if(category) {
            setLoading(true)
            getProducts();
        }else setProducts([])
    },[getProducts, category])

    if(loading) {
        return(
            <SkeletonTheme color="#e0e0e0" className="micro-product-list-wrapper">
                <div className="mt-3 text-center">
                    <Skeleton height={28} width={200}/>
                    <Skeleton height={16} count={4} />
                </div>
            </SkeletonTheme>
        )
    }

    return (
        <div className="micro-product-list-wrapper">
            {category ? 
                <h4>
                    Selected Category Products{catName ? `:${catName}` : ""}
                    {" "}
                    <Tooltip
                        direction="top"
                        text="To view a product in more detail, click on that row.  A new tab will be opened with that product, it's details, and the ability to edit it."
                        width="240px"
                        height="auto"
                    >
                        <span>
                            <i className="far fa-question-circle"/>
                        </span>
                    </Tooltip>
                </h4>
                :
                <h4>Select a category to see it's products below.</h4>
            }
            {
            products?.length > 0 && 
                <>
                    {products?.map((product)=>{
                        let status=PRODUCT_STATUSES?.filter((status)=>product.product_status_id === status.id)[0]
                        return(
                            <div className="each-prod" key={`micro-product-display-${product.id}`} onClick={()=>window.open(`/p/products/${product.id}`)}>
                                <span className="name">
                                    <span className={`${status.className}`}>
                                        ({status.name}) {" "}
                                    </span>   
                                    {product.name} 
                                </span>
                                <span className="type">
                                    {product.product_type_name}
                                </span>
                            </div>
                        )
                    })}
                </>
            }
            {error && 
                <span>{error}</span>
            }

        </div>
    )
}
