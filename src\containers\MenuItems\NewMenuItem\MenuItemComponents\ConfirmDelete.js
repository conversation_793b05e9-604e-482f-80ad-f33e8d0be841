import React from 'react'
import { Button } from 'react-bootstrap'

/** 
 * disable: boolean that handles if it's controling disabling (true) or deleting (false)
 * enable: boolean that handles if it's enable (true) or disable (false)
*/
const ConfirmDelete = ({activeItem, disable, handleDelete, enable}) => {

    return (
        <div className="text-center">
            <h4 className="section-title">
                {disable ?
                    <>
                        {enable ? 
                            "Enable Menu Item"
                        :
                            "Disable Menu Item"
                        }
                    </>
                :
                    "Delete Menu Item"
                }
            </h4>
            <p>
                <span>Name:</span>
                <span>{activeItem?.name}</span>
            </p>
            <p>
                <span>Type:</span>
                <span>
                    {activeItem?.module?.module_type_id===1 && "Menu Item"}
                    {activeItem?.module?.module_type_id===2 && "Widget"}
                    {activeItem?.module?.module_type_id===3 && "External Link"}
                    {activeItem?.module?.module_type_id===4 && "Folder"}        
                </span>
            </p>
            {disable ?
                <p>
                    Are you sure you want to 
                    {enable ? " enable " : " disable "} 
                    this menu item?
                </p>
            :
                <p>Are you sure you want to delete this menu item?</p>
            }
            <div className="y-n-btns menu-settings-btn-row">
                {/* e.target.value (and disable vs delete) determines how the subsequent function will behave */}
                <Button value="no" onClick={handleDelete}>No</Button>
                <Button value="yes" variant="danger" onClick={handleDelete}>Yes</Button>
            </div>
        </div>
    )
}

export default ConfirmDelete