import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Card, Table } from 'react-bootstrap';

import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Pagination from '../../../components/common/Pagination';

import APIGiftCards from '../../../api/GiftCards';
import TableSkeleton from '../../../components/common/TableSkeleton';

const Transactions = (props) => {
    const [error, setError]=useState();
    const [loading, setLoading]=useState(true);
    const [transactions, setTransactions]=useState([]);
    const [totalItems, setTotalItems]=useState(0);
    const [itemsPerPage, setItemsPerPage]=useState(100);
    const [page, setPage]=useState(1);
    const [pages, setPages]=useState([]);
    const [sortColumn, setSortColumn]=useState("id");
    const [sortOrder, setSortOrder]=useState("DESC");

    useEffect(() => {
        const getTransactions = async () => {
            setLoading(true);
            try {
                const res = await APIGiftCards.transactions({
                    gift_card_id: props.gift_card_id, 
                    max_records: itemsPerPage, 
                    page_no: page, 
                    sort_col: sortColumn, 
                    sort_direction: sortOrder
                });
                if (res.data) {
                    setTotalItems(res.data.total_record_count);
                    setTransactions(res.data?.transactions?.data || []);
                    const total_pages=Math.ceil(res.data.total_record_count/itemsPerPage);
                    let tmp_pages=[];
                    for(let i=0;i<total_pages;i++){
                        tmp_pages.push(i);
                    }
                    setPages(tmp_pages);
                } else if (res.errors){
                    setError(<ErrorCatcher error={res.errors} />)
                }
            } catch (error){
                console.log(error);
            }
            setLoading(false);
        }

        getTransactions();

        return () => {
            setLoading(false);
            setTransactions([]);
        }        
    },[props.gift_card_id, page, sortColumn, sortOrder, itemsPerPage]);

    const headerClick = (column) => {
        setSortColumn(column);
        if (sortOrder==="ASC") setSortOrder("DESC");
        else setSortOrder("ASC");
    }

    if (transactions.length === 0 && !loading) return <Card>No transactions found.</Card>;
    if (loading) return <Card>Loading...</Card>;

    return (
        <Card> 
            <Table className="table m-0">
                <thead>
                    <tr>
                        <th onClick={()=>{headerClick("transaction_id")}}>
                            Transaction #
                            <i className={`ml-1 fad fa-${sortColumn==="id"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                        </th>
                        <th onClick={()=>{headerClick("created_at")}}>
                            Date
                            <i className={`ml-1 fad fa-${sortColumn==="recipient_full_name"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                        </th>
                        <th onClick={()=>{headerClick("amount")}} className="text-right">
                            Amount
                            <i className={`ml-1 fad fa-${sortColumn==="recipient_email"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                        </th>
                        <th>Order #</th>
                    </tr>
                </thead>
                <tbody>
                    {transactions?.map((row, i) => (
                        <tr key={`gc-row-${i}`}> 
                            <td>{row.transaction_id}</td>
                            <td>{format(new Date(row.created_at), "M/d/yy h:mm a")}</td>
                            <td className="text-right">{`${row.amount > 0 ? "" : "-"}$${Math.abs(+row.amount).toFixed(2)}`}</td>
                            <td>{row?.transaction?.order_id}</td>
                        </tr>
                    ))}
                </tbody>
            </Table>
            {pages.length > 1 &&
                <div className="d-flex justify-content-end">
                    <Pagination
                        itemsCount={totalItems}
                        itemsPerPage={itemsPerPage}
                        currentPage={page}
                        setCurrentPage={setPage}
                        alwaysShown={false}
                    />
                </div>
            }
            {error}
        </Card>
    );
}

export default Transactions;