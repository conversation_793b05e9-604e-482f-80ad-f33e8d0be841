import React, { useState, useEffect, useCallback, Suspense } from "react";
import { useParams, useLocation, Link } from "react-router-dom";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import { Container, <PERSON>, Button} from 'react-bootstrap';
import Info from '../CreateWizard/Summary/Info'
import sha256 from 'js-sha256';

import DetailSubpage from "../../../components/common/DetailSubPage";
import SubHeader from "../../../components/common/SubHeader";
import ManageUsers from '../ManageUsers';
import AddUsers from '../AddUsers';
import { setErrorCatcher } from "../../../utils/validation";
import { ImageViewer } from "../../../components/common/ImageViewer/ImageViewer";

import Events from "../../../api/Events";
import Users from '../../../api/Users';

import "./Details.scss";
import EventDownloads from "../EventDownloads";

const DetailsPage = (props) => {
    const { id } = useParams();
    const location = useLocation();
    const [error, setError]=useState(null)
    const [eventInfo, setEventInfo] = useState();
    const [eventTypes, setEventTypes] = useState();
    const [currentTab, setCurrentTab] = useState(location?.hash || "Summary");

    const checkPurchased = useCallback(async(user, productId)=>{
        let response = await Users.purchasedProduct({user_id: user?.id, product_id: [productId]})
        if(response.status === 200){
            user.purchased = response.data[productId] ? 
                <span><i className="fas fa-check-circle" />{" "}Yes</span> 
            : 
                <span><i className="fas fa-times-circle" />{" "}No</span>;
        }else user.purchased = "---"
    },[]);

    const checkAnswers = useCallback(async(user, eventId)=>{
        let response = await Events.get_user_responses({user_id: user.id, event_id: eventId});
        if(response.status === 200){
            user.responses = response?.data[0]?.custom_responses;
        }else user.response = "No responses saved"
    },[]);

    const addEventImageUrl = useCallback(async(response)=>{
        if(response?.images?.length){
            response.images.forEach((image)=>{
                image.url = image.preview_url
            })
        }
    },[])

    const getEvent=useCallback(async()=>{
        try{
            let response = await Events.getSingle({id: id})
            if(response.status === 200){
                if(response.data[0].product_id) {
                    response.data[0].has_fee = true;
                    response.data[0].price = response.data[0].product_price
                }
                setEventInfo(response.data[0]);
                if(location.hash) setCurrentTab(location.hash)
                else setCurrentTab("Summary");
                if(response.data[0].users){
                    addEventImageUrl(response.data[0])
                    await response.data[0].users.forEach((user)=>{
                        // checkPurchased(user, response.data[0].product_id);
                        checkAnswers(user, id);
                    })
                } 
            }else if(response.errors){
                setError(setErrorCatcher(response.errors))
            }else setError(setErrorCatcher("There was a problem loading the event details."))
        }catch(ex){
            console.error(ex)
        }
    },[id, checkPurchased, checkAnswers, addEventImageUrl, location])
    

    useEffect(()=>{
        if(id) getEvent();
    },[id, getEvent])

    //we needed them at some point, this is here in case we need them again.  Be prepared
    const getEventTypes = async()=>{
        try{
            let response = await Events.Types.get();
            if(response.status === 200) {
                setEventTypes(response.data.filter(event=>{return event.is_service_type === 0}))
            }else if(response.errors){
                setError(setErrorCatcher(response.data.errors))
            }else setError(setErrorCatcher("There was a problem getting event types"))
        }catch(ex){
            console.error(ex)
        }
    }

    const inviteComponent=(
        <div>
            <p>Copy and paste the link below to invite a user to this event!  This link will take whoever it is sent to right to the portal version of the registration for this event.  There, they will be able to pay and answer any custom questions there.</p>
            <p>
                {window?.location?.origin}/p/event-register?event={eventInfo?.id}?code={sha256(`${eventInfo?.id}-${eventInfo?.event_type_id}-pistachiooOO`)}
            </p>
        </div>
    )

    const allTabs = [
        { 
            id: 1,
            displayName: 'View Details', 
            hash: 'Summary', 
            moduleId: 25, 
            component: <Info id={id} event={eventInfo} errorObj={[]} editEvent={false} isValid={()=>{}} />,
            link: null, 
            icon: 'fal fa-info-circle', 
        },
        { 
            id:2, 
            displayName: 'Invite Users', 
            hash: 'AddUsers', 
            moduleId: 181, 
            component: <AddUsers event={eventInfo} event_id={eventInfo?.id} referer={location.pathname} />, 
            link: null, 
            icon: 'far fa-user-plus',
        },
        { 
            id:3, 
            displayName: 'Manage Users', 
            hash: 'ManageUsers', 
            moduleId: null, 
            component: <ManageUsers importedEvent={eventInfo} importedUsers={eventInfo?.users} eventProductId={eventInfo?.product_id} event_id={id} referer={location.pathname} />, 
            link: null, 
            icon: 'far fa-user-cog',
        },
        {
            id: 4,
            displayName: 'Download Docs',
            hash: "DownloadDocs",
            moduleId: null,
            component: <EventDownloads event={eventInfo} stacked={false} /> ,
            link:null,
            icon:"far fa-save", 
            // alternateClick: exportHandler,
        },
        {
            id: 5,
            displayName: 'Event Image Viewer',
            hash: "ImageViewer",
            moduleId: null,
            component: <ImageViewer images={eventInfo?.images} objectName={`event-${eventInfo?.name}`} largeImgMax={window.innerWidth < 900 ? "300px" : "800px"} thumbMax={window.innerWidth < 500 ? "100px" : "200px"} />,
            link: null,
            icon:"far fa-images"
        },
        {
            id: 6,
            displayName: "Invite Link",
            hash: "InviteLink",
            moduleId: 181,
            component: inviteComponent,
            link: null,
            icon: "far fa-envelope-open"
        },
        { 
            id:7, 
            displayName: 'Event Wizard', 
            hash: 'Wizard', 
            moduleId: 333, 
            component: '', 
            link: '/p/events/:id/wizard', 
            icon: 'fas fa-wand-magic',
        }
    ]

    if (!eventInfo)
        return (
            <Container fluid>
                <SkeletonTheme color="#e0e0e0">
                    <Skeleton height={30} style={{ marginBottom: "1rem" }} />
                    <Skeleton height={12} count={5} />
                    <Skeleton
                        height={30}
                        style={{ marginBottom: "1rem", marginTop: "2rem" }}
                    />
                    <Skeleton height={12} count={10} />
                </SkeletonTheme>
            </Container>
        );

    return (
        eventInfo && (
            <Container fluid className="event-details-page">
                {error}
                <SubHeader items={[
                    { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                    { linkAs: Link, linkProps: { to: "/p/events" }, text: "Events" },
                    { text: eventInfo.name }
                ]} />
                <Card className="content-card">
                    <DetailSubpage 
                        allTabs={allTabs}
                        itemId={id}
                        loading={!eventInfo}
                        currentTab={currentTab}
                    />
                </Card>
            </Container>
        )
    );
};

export default DetailsPage;
