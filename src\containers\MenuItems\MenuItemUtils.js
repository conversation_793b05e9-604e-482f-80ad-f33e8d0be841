import Permissions from "../../api/Permissions";

/**Accepts the following inside of the variable parameters
 * @param {int} parameters.menu_item_id - The id of the menu
 * @param {int} parameters.module_id - The id of the module
 * @param {int} parameters.company_id - The id of the company
 * @param {int} parameters.parent_id - The id of the parent menu item
 * @param {string} parameters.search - The search term
 */
export const getMenuItems=async(parameters)=>{
    let menuResponse={
        data: null,
        errors: null
    }
    
    let paramObject = parameters ? {} : null;
    if(parameters?.menu_item_id) paramObject.menu_item_id = parameters.menu_item_id;
    if(parameters?.module_id) paramObject.module_id = parameters.module_id;
    if(parameters?.company_id) paramObject.company_id = parameters.company_id;
    if(parameters?.parent_id) paramObject.parent_id = parameters.parent_id;
    if(parameters?.search) paramObject.search = parameters.search;

    try{
        let response = await Permissions.MenuItems.get(paramObject);
        if(response.status === 200 && response.data){
            menuResponse.data = response.data;
        }else if(response.errors) menuResponse.errors = response.errors;
    }catch(ex){
        console.error(ex);
        menuResponse.errors = ex;
    }
    return menuResponse;
}