/* eslint-disable */

import Endpoints from '../index'
import EndpointData from './EndpointMock.json'
import ModuleData from './ModulesMock.json'
import ViewEndpoints from '../ViewEndpoints/index'
import CreateEndpoints from '../CreateEndpoints'
import { NewEndpointRow } from '../CreateEndpoints/NewEndpointRow'

describe ('The endpoints dashboard will mount and show proper dummy data', ()=>{
    let user;
    const endpoints=EndpointData.data;
    const modules=ModuleData

    before("It will load the user data", ()=>{
        cy.fixture('User/userResponse.json').then((data)=>{
            user = data;
        });
    })

    beforeEach("it will put a fake user in local storage", ()=>{
        window.localStorage.setItem('user',
            JSON.stringify({
                menu:[],
                roles: user.adminUser.data[0].roles,
                profile: user.adminUser.data[0],
                token: "bearer eyJASDKJ239849skdfjJKFJ.eyasdkj*9"
            })
        );
        cy.viewport(1200, 1080);
    }); //end beforeEach

    it("will mount the endpoint container",()=>{
        cy.mount(<Endpoints />)
        cy.get('.breadcrumb').should('exist')
        cy.get('.breadcrumb').children().should('have.length', 2)
        cy.get('h4').should('have.class', 'section-title').and('contain', 'View Endpoints')
        cy.get('.btn').should('exist').and('contain', 'Create Endpoints')
    }) //end mount endpoint container

    context("will mount the viewing container", ()=>{
        beforeEach("it will mount the view component",()=>{
            cy.intercept('POST', 'api/endpoint', {status: 200, data: endpoints}).as('getEndpoints');
            cy.intercept('POST', 'api/module', modules).as('getModules');
            cy.mount(
                <div className="content-card endpoint-wrapper">
                    <ViewEndpoints 
                        initialEndpoints={endpoints} 
                        setError={()=>{}} 
                        setSuccess={()=>{}}
                        setReset={()=>{}}
                        />
                </div>
            )
        })//end beforeEach
        
        it("will check the endpoints are all present",()=>{
            cy.get('[data-cy="each-endpoint-div"]').eq(0).within(()=>{
                cy.get('[data-cy="endpoint-type"]').should('contain', 'POST')
                cy.get('[data-cy="endpoint-slug"]').should('contain', "/category")
            })
            cy.get('[data-cy="each-endpoint-div"]').eq(1).within(()=>{
                cy.get('[data-cy="endpoint-type"]').should('contain', 'GET')
                cy.get('[data-cy="endpoint-slug"]').should('contain', "/category/{category_id}]")
            })
            cy.get('[data-cy="each-endpoint-div"]').eq(2).within(()=>{
                cy.get('[data-cy="endpoint-type"]').should('contain', 'POST')
                cy.get('[data-cy="endpoint-slug"]').should('contain', "/category/add")
            })
            cy.get('[data-cy="each-endpoint-div"]').eq(3).within(()=>{
                cy.get('[data-cy="endpoint-type"]').should('contain', 'POST')
                cy.get('[data-cy="endpoint-slug"]').should('contain', "/category/edit")
            })
            cy.get('[data-cy="each-endpoint-div"]').eq(4).within(()=>{
                cy.get('[data-cy="endpoint-type"]').should('contain', 'DELETE')
                cy.get('[data-cy="endpoint-slug"]').should('contain', "/category/delete")
            })
        }); //end all endpoint are present
        
        it("will check that clicking view modules will show the list", ()=>{
            cy.intercept('POST', 'api/endpoint', {status: 200, data: endpoints}).as('getEndpoints');
            ///edit should hav enone
            cy.get('[data-cy="each-endpoint-div"]').eq(0).within(()=>{
                cy.get('[data-cy="view-modules-btn"]')
                    .should('contain', "View Associated Modules").click();
            })
            cy.log(`${String.fromCodePoint(0x1F92F)} By not specifically calling within or .children on this cy.get, we are also verifying that there is only one result visible right now`)
            cy.get('[data-cy="module-list-item') 
                .should('be.visible')
                .children()
                .should('have.length', 24);
            cy.log(`${String.fromCodePoint(0x1F92F)} There are 12 modules, but the way that div is broken up, it has 2 children per, so we need to match 24 children`)
            cy.get('[data-cy="each-endpoint-div"]').eq(3).within(()=>{
                cy.get('[data-cy="view-modules-btn"]')
                    .should('contain', "View Associated Modules").click();
                })
            cy.get('[data-cy="no-modules"]').should('be.visible')
        }) //end checking 'view modules' buttons
    
        it("should have two buttons for each row", ()=>{
            cy.get('[data-cy="edit-btn"]').should('have.length', 5);
            cy.get('[data-cy="view-modules-btn"]').should('have.length', 5)
        })
    })

    context("will mount the create component", ()=>{
        beforeEach("it will mount the create component", ()=>{
            cy.intercept('POST', "api/endpoint/create", {status: 500}).as('createEndpoint')
            cy.intercept('POST', "api/module", modules).as('getModules');
            cy.mount(
                <div className="content-card endpoint-wrapper">
                    <CreateEndpoints
                        allEndpoints={endpoints}
                        setHide={()=>{}}
                        setSuccess={()=>{}}
                    />
                </div>
            )
        })

        it("will make sure all the elements exist",()=>{
            cy.intercept('POST', "api/endpoint", {status: 200}).as('getEndpoints');
            cy.get('[data-cy="name-slug"]').should('exist');
            cy.get('[data-cy="check-boxes"]')
                .get('[type="radio"]')
                .should('have.length', 4);
            cy.get('[data-cy="add-more-btn"]').should('exist');
            cy.get('[data-cy="assign-modules"]').children().should('have.length', 3)
            cy.get('[data-cy="assign-modules"] > h6').should('contain', "Assign to Module")
            cy.get('[data-cy="assign-modules"] > p').should('contain', 'All of the above endpoints')
            cy.get('.rbt').should('exist')
            cy.get('[data-cy="save-endpoints-btn"]').should('exist').and('contain', "Save Endpoints");
            cy.get('[data-cy="cancel-btn"]').should('exist').and('contain', "Cancel Creation");
        })

        it("will make sure there's a warning if we click save with no data",()=>{
            cy.intercept('POST', "api/endpoint", {statusCode: 500}).as('interceptEndpoint');
            cy.get('.error').should('not.exist')
            cy.get('[data-cy="save-endpoints-btn"]').click();
            cy.get('.error').should('exist');
            cy.get('.warning-modal').trigger('keydown', { keyCode: 27}) //press escape to close modal
            cy.get('.error').should('not.exist');
            cy.get('[data-cy="name-slug"]').type("stuff");
            cy.get('[data-cy="save-endpoints-btn"]').click();
            cy.get('.error').should('exist');
            cy.intercept('POST', "api/endpoint/create", {statusCode: 500}).as('interceptCreateEndpoint');
            cy.get('.warning-modal').trigger('keydown', { keyCode: 27}) 
            cy.get('[data-cy="GET"]').invoke('val').should('equal', 'GET');
            cy.get('[data-cy="GET"]').should('exist');
            cy.get('[data-cy="GET"]').click();
            // cy.get('[data-cy="save-endpoints-btn"]').click();
            // cy.get('.modal-content').invoke('text').should('contain', "There was an issue retrieving the endpoint lis")
            // cy.get('.error-modal').trigger('keydown', { keyCode: 27})
        })//end check existing elements
        
        it("will properly handle slashes as needed",()=>{
            cy.intercept('POST', "api/endpoint", {status: 500}).as('getEndpoints');
            cy.get('[data-cy="POST"]').click();
            cy.get('[data-cy="name-slug"]').type("stuff/")
            cy.get('[data-cy="save-endpoints-btn"]').click();
            //checks that it adds the beginning slash and removes trailing
            cy.get('[data-cy="name-slug"]').invoke('val').should('equal', '/stuff')
            //error modal should only appear as a warning if the stuff is missing or it's a duplicate, not for slash correction
            // cy.get('.error-modal').trigger('keydown', { keyCode: 27}) 
            //make sure that it doesn't add an extra slash if one already exists there
            cy.get('[data-cy="name-slug"]').clear()
            cy.get('[data-cy="name-slug"]').type("/stuff")
            // cy.get('[data-cy="save-endpoints-btn"]').click();
            // cy.get('[data-cy="name-slug"]').invoke('val').should('equal', '/stuff')
        })//end handle slashes

        it("will be able to add and remove more endpoints",()=>{
            cy.intercept('POST', "api/endpoint", {status: 200}).as('getEndpoints');
            cy.get('[data-cy="add-more-btn"]').click();
            cy.get('[data-cy="add-more-btn"]').click();
            cy.get('.new-endpoint-row').should('have.length', 3);
            cy.get('[data-cy="name-slug"]').eq(0).type("/cats");
            cy.get('[data-cy="name-slug"]').eq(1).type("/dogs");
            cy.get('[data-cy="name-slug"]').eq(2).type("/paws");
            cy.get('[data-cy="remove-endpoint-btn"]').eq(0).click();
            cy.get('[data-cy="name-slug"]').eq(0).invoke('val').should('equal', '/cats');
            cy.get('[data-cy="name-slug"]').eq(1).invoke('val').should('equal', '/paws');
        })//add and remove endpoints
    }) //end check the create component

    context("it will make sure items being edited can be edited", ()=>{
        beforeEach("it will mount the edit component", ()=>{
            cy.intercept('POST', "api/endpoint/create", {status: 500}).as('createEndpoint')
            cy.intercept('POST', 'api/module', modules).as('getModules');
            cy.mount(
                <div className="content-card endpoint-wrapper">
                    <NewEndpointRow
                        endpoint={endpoints[0]}
                        editEndpoint={true}
                        removedEndpoint={()=>{}}
                        passEndpoints={()=>{}}
                        setSelectedModules={()=>{}}
                    />
                </div>
            )
        })

        it("will make sure eveything has data", ()=>{
            cy.get('[data-cy="name-slug"]').invoke('val').should('equal', "/category");
            cy.get('[data-cy="POST"]')
                .should('be.checked');
        })

        it("will check that the typeahead has the dummy data && it's editable", ()=>{
            cy.get('.tokens-list').children().should('have.length', 3);
            cy.get('.tokens-list').children().eq(0).should('contain', 'Products Dashboard');
            cy.get('.tokens-list').children().eq(1).should('contain', 'Categories');
            cy.get('.tokens-list').children().eq(2).should('contain', 'Discount Dashboard');
            cy.get(':nth-child(2) > .close > [aria-hidden="true"]').click();
            cy.get('.tokens-list').children().should('have.length', 2);
            cy.get('.tokens-list').children().eq(0).should('contain', 'Products Dashboard');
            cy.get('.tokens-list').children().eq(1).should('contain', 'Discount Dashboard');
            cy.get('.rbt-aux > .close > [aria-hidden="true"]').click();
            cy.get('.tokens-list').children().should('have.length', 0);
            cy.get('.form-control').click();
            cy.get('.dropdown-menu').children().should('have.length', 10)
            cy.get('.form-control').type("Home");
            cy.get('.dropdown-menu').children().should('have.length', 1)
            cy.get('#regular-item-0').should("contain", "Home")
            cy.get('#regular-item-0').click();
            cy.get('.tokens-list').children().eq(0).should('contain', 'Home');
        })//will check that the typeahead has dummy data

        it("will edit the endpoint name and type",()=>{
            cy.get('[data-cy="name-slug"]').clear();
            cy.get('[data-cy="name-slug"]').type("RAWR");
            cy.get('[data-cy="name-slug"]').invoke('val').should('equal', 'RAWR');
            cy.get('[for="method-select-0-PUT"]').click(); //making sure the label can be clicked too
            cy.get('[data-cy="PUT"]')
            //manually verify - checked state is being applied, but checking the attribute via cypress isn't working >.<
        })//check that data is editable

    })//end making sure it can be edited
})

