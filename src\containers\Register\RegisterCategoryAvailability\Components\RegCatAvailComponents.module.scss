@import '../../../../assets/css/scss/mixins';
@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/themes';

.edit-cat-avail-wrapper{
    border-radius: 5px;
    padding: 8px;
    margin-top: 1rem;
    margin-bottom: 1rem;
    .subtitle{
        margin-bottom: 3px;
        font-weight: $card-title-font-weight;
    }
    .day-row{
        max-width: 400px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-end;
        margin-bottom: 12px;
    }
    .error-text{
        margin-left: 1rem;
    }
    label{
        margin-top: 8px;
    }
}

.each-day-wrapper{
    border: solid 2px $divider-color;
    margin: 8px;
    min-width:250px;
    max-width:250px;
    border-radius: 10px;
    padding: 5px;
    height: 100%;
}