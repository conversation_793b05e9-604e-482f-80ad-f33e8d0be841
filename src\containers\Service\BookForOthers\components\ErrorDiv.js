import React, { useState, useEffect } from 'react';

export const ErrorDiv = ({
    errors,
    ...props
})=>{


    const [error, setError] = useState(null);

    useEffect(()=>{
        const recursivelyBreakDownObjects=(obj)=>{
            let error;
            if (typeof obj === 'object' && !Array.isArray(obj) && obj !== null) {
                if (Object.keys(obj).length === 1 && Object.values(obj).length === 1) {
                    error = `${Object.keys(obj)} : ${Object.values(obj)}`;
                } else {
                    error = [];
                    Object.keys(obj).forEach(err => {
                        error.push(`${err} : ${recursivelyBreakDownObjects(obj[err]) }`);
                    });
                }
            } else if (Array.isArray(obj) && obj.length > 0) {
                error = obj;
            } else {
                error = [obj];
            }
            console.log(error)
            return error;
        };
    
        if(Array.isArray(errors) && errors.length === 0) setError(null);
        else if (errors) {
            let newErrors = (recursivelyBreakDownObjects(errors));
            setError(newErrors);
        }

    },[errors])


    return(
        <div>
            {/* {error ?
                <p style={{textAlign: "center", marginTop: "1rem"}}>
                    {error?.map((error, i)=>(
                        <span key={`error-${i}`} className="error-text">
                            {error}
                            <br />
                        </span>
                    ))}
                </p>
                :
                null
            } */}
        </div>
    )
}

export default ErrorDiv;