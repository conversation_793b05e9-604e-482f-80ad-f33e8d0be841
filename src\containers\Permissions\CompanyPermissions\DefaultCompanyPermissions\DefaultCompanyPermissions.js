import React from 'react'

const PERMISSION_LEVELS = ["Company Owner", "Admin", "Staff", "Non-Staff Manager", "Patron"]

export const DefaultCompanyPermissions = ({features, ...props}) => {
    
    
    return (
        <div>
            <h4 className="section-title">
                Defaults For All Companies
            </h4>
            <table>
                <tbody>
                    {features?.map((feature)=>(
                        <>
                            <thead>
                                <tr>
                                    <th>
                                        {" "}
                                    </th>
                                    {PERMISSION_LEVELS.map((permission, i)=>(
                                        <th key={`permission-headers-${i}`}>
                                            {permission}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                <tr key={`each-features-${feature.id}`}>
                                    <th>
                                        {features.name}
                                    </th>
                                    {PERMISSION_LEVELS.map((permission, i )=>(
                                        <td key={`permission-head-check-${i}`}>
                                            <input type="checkbox" value={`all-feature-${feature.id}-${permission}`}/>
                                        </td>
                                    ))}
                                </tr>
                                {feature?.modules?.map((module)=>(
                                    <>
                                        <tr key={`feature-${feature.id}-module-${module.id}`}>
                                            <th>
                                                {module.name}
                                            </th>
                                            {PERMISSION_LEVELS.map((permission, i )=>(
                                                <td key={`permission-head-check-${i}`}>
                                                    <input type="checkbox" value={`all-module-${module.id}-${permission}`}/>
                                                </td>
                                            ))}
                                        </tr>
                                    </>
                                ))}
                            </tbody>
                        </>
                    ))}
                </tbody>
            </table>
        </div>
    )
}
