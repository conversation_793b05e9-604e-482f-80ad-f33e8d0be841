import React, { useState, useEffect, useRef } from 'react'

import usePrevious from '../../../../components/common/CustomHooks';

const positiveClass = "positive-row";
const negativeClass = "negative-row";

const FeatureRow = ({feature, handleChecked, ...props}) => {

    const mountedRef = useRef(false);
    const [ checked, setChecked ]=useState(false);
    const [ firstLoad, setFirstLoad ]=useState(true);
    const oldChecked=usePrevious(checked);

    useEffect(()=>{
        mountedRef.current = true;

        return ()=>{mountedRef.current = false}
    },[])

    useEffect(()=>{
        if(feature && feature.default_is_enabled === 1 && mountedRef.current) {
            setChecked(true);
        }else if(feature && mountedRef.current){
            setFirstLoad(false);
        }
    },[feature]);

    //probably because it's loading a bunch of rows at once, the last one is updating the "checked" state too slow for the setFirstLoad to be in the first useEffect
    useEffect(()=>{
        if(checked && firstLoad && mountedRef.current) setFirstLoad(false)
    },[checked, firstLoad])

    useEffect(()=>{
        if(oldChecked!==checked && !firstLoad) handleChecked(feature.id, checked)
    },[checked, oldChecked, handleChecked, feature, firstLoad])

    return (
        <tr className={checked ? positiveClass : negativeClass}>
            <td>
                {feature.name}
            </td>
            <td className="check-td">
                {(feature.default_is_enabled===1 && !checked) || (feature.default_is_enabled===0 && checked) ? <i className="far fa-circle" /> : <i className="far fa-circle hide-space" />}                
                <input 
                    type="checkbox"
                    checked={checked}
                    onChange={(e)=>{setChecked(e.target.checked)}}
                />
            </td>
            <td className="y-n">
                {checked ? 
                    "Yes"
                :
                    "No"
                }
            </td>
        </tr>
    )
}

export default FeatureRow