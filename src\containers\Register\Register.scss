@import '../../assets/css/scss/mixins';
@import '../../assets/css/scss/variables';

.register-container {

    .registerDefinition {
        min-height: 300px;
    }

    .form-label {
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.3rem;
    }
    
    .form-check-label {
        font-size: 0.9rem;
        font-weight: 500;
        min-width: 125px;
    }

    .radio-left {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
    }

    .radio-with-label{
        display: flex;
        flex-direction: column;
    }

    .form-check {
        padding-left: 0;

        .form-check-input {
            margin-top: 0;
        }
    }

    .radio-left > div:nth-child(2) {
        margin-left: 2rem;
    }

    input, .rbt.has-aux{
        max-width: 400px;
    }

    .num-days-display {
        margin-left: 1.5rem;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;

        .form-label {
            margin-bottom: 0;
            font-weight: 400;
        }

        .form-control {
            margin-left: 0.75rem;
            width: 80px;
        }
    }

    .show-register-button-row {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
    }

    .button-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin: 1rem 0;
    }

    /* Cancel & Save buttons */
    .button-row > div:first-child {
        display: flex;
    }

    /* Delete button */
    .button-row > div:nth-child(2) {
        display: flex;
        justify-content: flex-end;
    }

    .jsoneditor {
        height: 600px;
    }
}

@media (min-width: 600px) {
    .register-container {
        .button-row .row {
            width: 100%;
        }

        /* Cancel & Save buttons */
        .button-row > div > div:first-child {
            display: flex;
            justify-content: flex-start;
        }
        
        /* Delete button */
        .button-row > div > div:nth-child(2) {
            display: flex;
            justify-content: flex-end;
        }
    }
}

@media (max-width: 599px) {
    .register-container {
        .radio-left {
            flex-direction: column;
        }
    
        .radio-left > .form-check:nth-child(2) {
            margin-left: 0;
            margin-top: 0.5rem;
        }
    
    }
}

.register-times{
    .react-datepicker__triangle{
         left: 50px !important;
    }
    .react-datepicker__current-month{
        display: block !important;
    }
    .each-date{
        margin-top: 1rem;
        label{
            min-width: 100px;
        }
        input{
            margin-bottom: .5rem;
        }
    }
    .tz-input{
        max-width: 350px;
    }
    i{
        color: $company-dark-error;
        margin-left: 5px;
        cursor: pointer;
    }
    .time-picker{
        max-width: 85px;
    }
    .all-days{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }
    .closed-row{
        @include basic-flex-row;
        input{
            margin-right: 1rem;
        }
    }
    .time-col{
        @include basic-flex-column;
    }
    .all-closed{
        display: flex;
        flex-direction: row;
        .each-closed{
            min-width: 85px;
            margin: .5rem;
        }
    }
}

.register-form-container{
    container: reg-form / inline-size;
    .reg-form-row{
        display: flex;
        flex-direction: row;
    }
    .reg-form-col{
        display: flex;
        flex-direction: column;
        min-width: 400px;
        margin: 1rem;
        border: 1px solid $divider-color;
        padding: 5px;
        border-radius: 5px;
        max-width: 800px;
    }
    @container reg-form (max-width: 800px){
        .reg-form-row{
            display: flex;
            flex-direction: column;
            max-width: 500px;
        }
    }
}