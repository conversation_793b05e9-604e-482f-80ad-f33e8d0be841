[{"id": "12/28/2022", "title": "December 28th, 2022", "subtitle": "Release Version 0.37", "notes": ["Events: Removed Reserved events from showing on the home page", "POS, Printing: Fixed receipt alignment", "Streaming: Added the 'Holiday Showcase' streams", "Waivers: There is now indication on the waiver that only one portion should be filled out, not both, depending on if the user is a minor or not.  <PERSON><PERSON>ver prevents filling out both sections and chooses the proper section according to the user's input.", "Bug Fix: Ensured services bookings that are supposed to select multiple time slots do so", "Bug Fix: OOPs on 'Record Payment' screen is gone", "Bug Fix: Service bookings are properly showing as available/unavailble as they're supposed to", "Bug Fix: When editing an event, children events are automatically populated in the fields so that they correctly save after editing", "Bug Fix: No longer get a 'Passwords Don't Match' error if they do match"]}, {"id": "11/23/2022", "title": "Novemer 23rd 2022", "subtitle": "Release Versions 0.36", "notes": ["Services: Added delete button for services", "Orders: Added the variant name when viewing order details", "Reports: Added new report type, 'Subscriptions'", "Events: Event status is displayed at the top when an event is clicked on in the calendar.  Can easily see 'confirmed', 'cancelleed', etc", "Events: When creating a new event, hovering over an event type will show whether or not the event type can support child events or not", "Bug Fix: Tip amount properly being shown on the screen", "Bug Fix: ensured that every dashboard that has multiple pages (users, groups, events, etc) reset to page one when adding new search parameters", "Bug Fix: Timezone on the grille printer"]}, {"id": "11/01/2022", "title": "November 1st, 2022", "subtitle": "Release Version 0.35", "notes": ["Portal, Orders: Added an order page so that orders can be viewed without having to go into the POS", "Portal, Orders: Cleaned up the buttons on the bottom of the refunds screen", "Portal, Profile: Changed subscription tab on the profile from a table display to cards for easier readability", "Backend, Profile: Changed some of the data retrieval in a way that speeds up the loading of a users' home page and profile", "Backend, Charges: Modified data retrieval for outstanding charges to speed up their load time", "Portal, Profile: Modified the way the profile dealt with the side navigation and and smaller screens", "Portal, Profile: Added a quick-glance for staff to see on the profile if a membership is active, suspended, expired, or cancelled underneath the users' profile picture", "<PERSON><PERSON> Fix, Profile, Family: Fixed a bug where editing a family member would instead redirect to edit the user's data", "Bug Fix: Fixed a bug that prevented the variants from showing on the receipts", "<PERSON>ug Fix, Home: Fixed a bug that was occasionally causing an 'OOPS' screen", "Testing: Added automated tests to the patron cart process to make the system more stable in development"]}, {"id": "10.19.2022", "title": "October 19th, 2022", "subtitle": "Release Version 0.34", "notes": ["POS: Added loading spinner while the whole user is loading to indicate there has been a change", "POS: More work was done to optimize the POS for speed and versatility", "Portal, Refunds: Removed bundles as a refund option as they contain token products that don't yet properly refund", "Portal, Products: Defaulted product dashboard to only show active products on first load", "Portal, Services: Added removed services to the searches for old bookings", "Portal, Services: Hid old services", "Portal, Profile: Added the ability to add a Restart Date to a subscription on a users' profile", "Portal, Reports: Added the option to get settlement reports", "Portal: Added help button for live chat to the portal for admin/staff users", "Bug Fix, POS: Products with no active variants no longer cause an error in the POS", "Bug Fix, Print: Fixed duplicate copy of smoothie orders being printed"]}, {"id": "10.05.2022", "title": "October 5th 2022", "subtitle": "Release Version 0.33", "notes": ["POS: A new order is only being created when a user is selected AND an items is added to the cart, instead of just when a user is selected, to cut down on empty orders", "POS: Functionality of the POS was rewritten to plan for future growth opportunities and to optimize speed in both display and in retrieving information", "POS: Orders from the same day and same register type (i.e. Front Desk 1 and Front Desk 2) can be resumed via the open orders tab in the POS", "POS, Print: Adjusted print sizes on the receipt", "POS: Split Payments adjusted for consistency throughout the process - ensured staff has the ability to go back and correct data", "POS: Made adjustments so POS could be utilized by smaller devices, such as an iPad sized screen", "Portal, Registration: Disallowed registering for new accounts with an email already in use", "Portal, Events: Added a button to hide child events from the search", "Portal, Events: Condensed some of the search inputs into one", "Portal, Refunds: Added a simplified option to refund a whole order rather than having to use the advanced editor", "<PERSON><PERSON> Fix, Portal, Home: Changed the sizing of the container for groups on the home screen as it stretched in ways other containers did not", "<PERSON><PERSON>x, Portal, Groups: Accept invitation link wasn't working if the user was not logged in before clicking it", "<PERSON><PERSON>, <PERSON><PERSON>: When outstanding charges were added to the cart, the wrong user was being applied to it", "Bug Fix, POS: Orders were losing select items on the POS when a new one was added", "Bug Fix, Groups: Some areas where images were loading improperly were causing an 'OOPS' error", "Bug Fix, Discounts: Fixed display of the typeahead for events on the discount page"]}, {"id": "09.21.2022", "title": "September 21st 2022", "subtitle": "Release Version 0.32", "notes": ["Portal, Family: Added a way for family to create new members for their family group in their profile on the family tab", "Portal, Family: Family members are now able to properly edit other members if they have proper group permissions", "Portal, Family: Added the ability to make new accounts for children via a user's account", "Portal, Orders: Eliminated ellipsis in long product names.  Changed the width of the search orders pop-up in the POS to show more information", "Portal: Added limit to registering birthday so that accounts cannot be made for infant ages", "Portal: Added banner on the profiles of users that do not have a compatible birthdate in the system", "Portal: On the groups tab in the user profile, a user may accept invitations to a group", "Portal: A new page was added to be able to view and print events and/or services in a laid out, weekly view", "Portal: Staff, in addition to administrators, can now sort transactions by status", "Portal: Added AM/PM to times on the tranasction tables", "<PERSON><PERSON>, Portal: Minor section on the waiver was creating an error and was fixed", "<PERSON><PERSON>, <PERSON>: 'OOPS' screen on recording outstanding charges has been corrected", "<PERSON><PERSON> Fix, Checkin: If a user had been deleted but was in the checkins list, would cause a crash.  Fixed.", "Bug Fix: Clicking on a user in the POS was not checking them in properly as it did before.", "Bug Fix, POS: Fixed a bug that was adding the addons from a previous order to the current order", "Bug Fix, POS: Fixed an error on the print screen where the logo wasn't showing up.  Fixed spinners on side pane for users that didn't have family to load", "POS: Pos was adapted so that it can be dispalyed as efficiently on a smaller device, such as an iPad for versatility", "POS: Old orders can be resumed via the open order tabs", "POS: Increased the number of checkins that display across the bottom of the POS", "Services: Altered the size of the pop-up the services were in to better fit the present information", "Checkin: More information was added to the page that pops up after checking in a user, include how many subscriptions and outstanding charges they have", "Backend, API: Discounts were applied to recurring subscriptions"]}, {"id": "09.07.2022", "title": "September 7th 2022", "subtitle": "Release Version 0.30", "notes": ["Backend, API, Services: Added the ability to get booking status more easily", "Products: Created specifications for redesigning the products add and edit pages to improve workflow", "Family: Families can no longer see an invited person as if they're a part of the family already.", "Portal, Family: People invited to a family no longer see themselves as being part of that family until they're confirmed.", "Portal: Added banned to the home screen to prompt a user to set their date of birth", "<PERSON>, Event Wizard, Bug Fix: Fixed the non-recurring Event Wizard time display; should also work properly on Apple devices now.", "Portal, Profile: Transaction table now shows what family member made a purchase without having to click on the charge", "Portal, Quality of Life: Some font sizes were increased to improve readability overall.", "Portal, Quality of Life: The menu and header have been reformatted and improved for better consistency, especially on mobile view.", "Portal, <PERSON><PERSON>: Event descriptions that have html tags in them are properly displayed in the patron cart.", "Portal, Profile: When adding an item to the cart from the outstanding charges tab, the correct 'for user' will be displayed in the cart.", "Portal, Services: Services have a more detailed search view for administrators to view and track services", "Portal, Services, Shop: If a services has tokens available to purchase when booking, will show a user that the patron shop has the option available", "Portal, Services, Bug Fix:Managers viewing service bookings won't throw an error in console.", "Portal, Bug Fix: Fixed a bug that prevented the menu from being scrolled when a loaded page wasn't long enough.", "Portal, Bug Fix: Fixed a bug that prevented outstanding charges to be recorded through profile.", "Portal, Bug Fix: Users are properly searched for when typing in the search bar for on the modules page."]}, {"id": "08.23.2022", "title": "August 22nd, 2022", "subtitle": "Release Version 0.29", "notes": ["Quality of Life: A suite of automated tests were created to help ensure system stability and decrease bugs and improve site consistency.", "Portal, Home Page: Upcoming events now shows the events upcoming in the future, not all events.", "Portal, Print: On the full page receipt for a transaction that included a token redemption, the receipt now says that is what it was for.", "Portal, Notes:Made improvements to the note section for admin/staff on the users' profiles.  Only admins can delete notes and staff can only edit their own and see their own regardless of visibility status.  Added cancel button and some visual upgrades to popups.", "Portal, Profile: On the family tab in the user profile, invited-only family members don’t show up, only confirmed members.", "Portal, Service Booking: Token purchases condense into one cart item instead of listing the number of tokens a user is purchasing.", "Portal, Quality of Life: Added another username check to the registration process to show a unique error if the username was already taken.", "Portal, Quality of Life: Added another color for error pop-ups that feel less like a true error and give the user something to fix to make the system work properly (such as a username already being taken).", "POS, Payment: Behind the scenes work was done in preparation for split payments.", "POS: When looking up a user in the POS, staff is able to add outstanding charges from that user into the checkout column.", "POS: Hid the barcode scanner from all registers instead of just some of them.", "Backend: Replaced static images on website with images loaded from the database.", "<PERSON><PERSON>, Backend: Ensured that staff members are unable to see other staff member’s transactions.", "<PERSON><PERSON>x, Backend: Ensured that correct error messages are displayed if attempting to edit the roles of another user.  Made sure permissions and roles were consistent in their functionality in the handling of data as well as in the displaying of data.", "Bug Fix: Fixed an error on viewing product variants that caused an error for IOS/Safari users.", "Bug Fix: Fixed an error on viewing streams that caused an error for IOS/Safari users.", "Bug Fix: Fixed an error on viewing subscriptions on the profile that caused an error for IOS/Safari users.", "Bug Fix, POS: Bug Fix: If refreshing the POS with a user that had a discount in their cart, the discount will be maintained through the refresh.", "<PERSON><PERSON> Fix, POS: Certain old orders were triggering an error screen when trying to print them", "<PERSON><PERSON>, <PERSON>, Profile: Prevented all events from loading on the profile 'Upcoming Event' tab and only shows events in the future.", "Bug Fix, Portal: Fixed text that overlapped on event cards on select screen sizes.", "<PERSON><PERSON>x, Portal, Profile: Fixed a bug where loading seemed to be occuring on the family tab if the user had no family members.", "<PERSON><PERSON> Fix, Portal: Performance training products now show the details correctly again.", "<PERSON><PERSON>, <PERSON>, Tokens: Fixed a bug that caused tokens to not display properly in certain situations.", "<PERSON><PERSON> <PERSON><PERSON>, Portal, Events: Fixed a bug that caused custom questions on event registration to sometimes not load properly or cause errors.", "<PERSON><PERSON>, Portal, Events: Fixed a bug that was causing some events to show for staff but not patrons.", "<PERSON><PERSON>x, Portal, Events: When a user used a direct link to an event and logged in from that link, a user would not be able to properly register and would cause an endless load cycle."]}, {"id": "07.20.2022", "title": "July 20th, 2022", "subtitle": "Release Version 0.28", "notes": ["POS: Added the print button back into the POS.", "Portal: Changes were made to allow for multiple shop styles.", "Portal, Profile: The table on the subscription tab has been reduced in size so that it doesn't force the side navigation to move at larger screen sizes.", "Portal, Profile: On screen sizes less than 1200px, the side navigation doubles up into two columns to cut down on extended screen length.", "Portal, Profile: The outstanding charges tab on a user profile uses the new endpoint for faster load.  This allows for filtering by family charges/not.", "Portal, Profile, Groups: The groups on a user's profile are clickable links.  Patrons are directed to the same group page they can access through home.  Staff is directed to the group edit/details page for the proper group.", "Portal, Profile, Groups: The group tab information was changed from a table to a card to cut down on causing the side navigation to be pushed below at larger screen sizes.", "Portal: Staff are now able to leave notes with the viewability for IT/Support", "Portal, Event Calendar: After clicking on an event on the calendar, more details are displayed and the pop-up was styled in a more usable way.", "Portal, Event Calendar: After selecting a location on the event calendar, the location persists through changes in view.", "Portal, Event Calendar: Changing the type or location filters changes the text in the buttons to indicate what is selected", "Portal, Quality of Life: Visual indication was added to many small parts throughout the site to show when data is loading.", "Portal, Waiver: Change to text on the banner notifying a user to sign their waiver when it's unsigned.", "Portal, Tokens, Wallet: Tabs were added to the wallet so that redeemed, expired, and available tokens are able to be viewed independent of one another.", "Portal, Family: Family can unregister their family from events in addition to themselves.", "Backend, Family: Family will not see other users that are just invited to their family group until they're confirmed members", "API, Backend: Created a new call for outstanding orders to help with load times and trim down other calls.", "<PERSON><PERSON>, <PERSON>, Family: Icons that represent family members no longer appear in a line down the screen.", "<PERSON><PERSON> Fix, Portal: There is no longer a small window of screen sizes that cause event description text to cover part of the bottom details on upcoming events.", "<PERSON><PERSON>, Portal, Profile: On the left hand side of a profile, the groups a user belongs to are now properly displayed.", "Bug Fix, POS: Fixed a bug where the tip was carrying over to another order after the original order with a tip was completed.", "<PERSON><PERSON> Fix, <PERSON><PERSON>: Fixed a bug where the item summaries were not displaying in the cart in certain situations.", "<PERSON><PERSON> Fix, P<PERSON>, Cart: Fixed a bug where recent changes caused new orders to be created when they were devoid of items.  Cuts down on empty orders in the database", "<PERSON><PERSON> Fix, <PERSON><PERSON>: Deleting a service event from the cart no longer deletes other cart contents as well.", "<PERSON><PERSON> Fix, Transactions: When staff had more than one role assigned to them, the transaction page was treating them as having the lowest role they posessed.  This has been corrected and viewing will occur according to the highest role a staff member posesses.", "Bug Fix, Token Shop: Deleted products will no longer affect the UI item prices - Patron cart will now show only the first variant, any additional variants are hiden.  Items prices in the patron cart will now show a single price and not a range.", "Bug Fix, POS: Item prices in the POS will now properly show the range as lowest to highest.", "<PERSON><PERSON>x, Backend: Staff cannot promote a user to staff.", "Initital planning and changes made in preparation for online ordering."]}, {"id": "07.06.2022", "title": "July 7th, 2022", "subtitle": "Release Version 0.27", "notes": ["Portal, Quality of Life: To decrease load time, the user merge page has a smaller amount of results displayed that can be paged through and searched instead of a long list.", "Portal, Quality of Life: Unpaid events now have a color change and a cart icon to help indicate when something is already in the cart or not.", "Portal, Quality of Life: On the home page, the events displayed are making a more optimized call to the backend in order to reduce load times.", "Portal, Registration: The @ and other symbols are disallowed on registration by new users and on POS/user dashboard new users", "Portal: Addon price if 0 will show as $0.00", "Event Calendar: Service Event Blocks were added to the event calendar with an admin-only view, accessible through the Filter By Type drop-down when logged in with an admin account.", "Event Calendar: Meta events were removed from the default view of the calendar. Events can still be searched by those meta events if desired.", "Event Calendar: The event calendar was adjusted to start the week on Monday instead of Sunday.", "Reports: In the report page, there is a report available to get all users.", "Memberships: If a user has their payment profile saved, it is indicated on the subscription table.", "Tokens: Service tokens are properly being assigned to the purchasing user from the patron cart.", "API, Backend: Support was added to the backend to allow for searching and getting smaller responses for pagination of the user merges", "API, Backend: Changes were made on the backend to include multiple categories on products.", "Backend, Printer: The grille printer will not print jobs over the weekend when things are not made to order.", "Backend, Printer: Changes were made on the backend so that if a product’s add-on was marked for the Grille printer, the whole product would print properly.", "Backend, Email: All test emails generated from the DEV environment are sent to a collective source so no emails can mistakenly be sent to patrons during testing", "<PERSON><PERSON>, Back<PERSON>, Email: Emails for services no longer send a duplicate email.", "<PERSON><PERSON> Fix, Tokens: Correct the price display in the service create/edit form dropdown for selecting tokens so it doesn’t show up as $40.000", "<PERSON><PERSON>x, <PERSON><PERSON>s: hide the “you do not have enough tokens” message when the patron is purchasing a service for $0.00", "Bug Fix, Products: Parent categories on products are properly displaying as “none” when there is no parent category", "Bug Fix, Products: Bug Fix: Recent changes to the products created a problem with deleting all the categories. It has now been fixed.", "Bug Fix, Loations: Bug Fix: Locations are properly populating their data when going to edit them.", "Bug Fix, Notes: Bug Fix: When editing notes, the default visibility was “admin” instead of the saved status.", "Bug Fix, Transactions: Bug Fix: When an admin or staff member is on their own transaction page, the breadcrumbs for navigation work properly.", "Split Payments: UI has been created for a user to split a charge between cash and credit card.  Awaiting backend.", "Split Payments: UI has been created to allow for multiple people to split a single purchase for someone.  Awaiting backend.", "Split Payments: UI has been created to allow for multiple people to split up a single order into multiple smaller orders."]}, {"id": "06.22.2022", "title": "June 22nd, 2022", "subtitle": "Release Version 0.26", "notes": ["Public: Events are now able to be filtered so that the events that are occurring at the restaurant can be displayed independent of other events.", "Portal: Admins can now merge and unmerge accounts via the front end UI. Page restricts access from anyone below Admin and restricts admins from merging any accounts with a role higher than patron.", "Portal, Check-In: After a user is checked in, their information is removed from screen after 30 seconds. Changed some wording for check-in screen.", "Portal: The subscription tab on the user profile has the final bill date added to the table for informational purposes. Staff admins can click on a button to change that final bill date.", "Portal, Print: On the order tab in the POS, default orders that load are now the complete orders. Old orders can be printed again on their detail screen", "Portal, Print: Staff may print full page invoices for patrons or they may print their own from their user profile.", "Portal, Events: Add tooltips to event types.", "Bug Fix: Changing the bill interval does not change the dollar amount when editing subscriptions.", "Bug Fix: Multiple small objects/lines that were triggering the “oops” screen have been rectified.", "<PERSON><PERSON>, Backend: Adjusted ability to add members to a group in the backend permissions.", "<PERSON><PERSON>, Backend: Added checking to the backend to ensure that members who are already in a group are not returned to be reinvited to it.", "<PERSON><PERSON>, Backend: Backend fix to prevent error showing up in the error logs.", "<PERSON><PERSON>x, Backend: Backend was modified to allow for family admins to unregister their family from events in addition to themselves.", "Bug Fix, Coupons, POS: Selected coupons will no longer be listed twice on the POS Preview screen.", "<PERSON><PERSON>x, <PERSON><PERSON>: Fix an error that prevented the patron cart to have some of the same order reusability as the POS. Prevents as many empty or incomplete orders from being created.", "<PERSON><PERSON>x, Portal: Added conditional rendering to prevent error boundary.", "<PERSON><PERSON> Fix, <PERSON>: When creating a subscription, tokens were mistakenly marked as a required field. Can now make products without selecting a token.", "<PERSON><PERSON>, Portal: Some events that were not properly showing are fixed and showing up now", "Backend, Portal, Products: Frontend and backend changes to allow for bulk edits. Staff can now edit products in multiples successfully. This allows for things like adding products to printers, changing status, and adding categories", "Backend, Printer: Backend changes to ensure that if print jobs don’t get fulfilled in the kitchen printer, they don’t print the next day.", "<PERSON>, Backend, Printer: Kitchen Printer was modified to only print items assigned to the relative printer."]}, {"id": "06.08.2022", "title": "June 8th, 2022", "subtitle": "Release Version 0.25", "notes": ["Portal: Added more descriptions to for error catchers.", "Portal, Events: When editing an event, the event status dropdown is now populated from the backend.", "Portal, Events: Removed reference from Requiring Membership from the event details when registering for an event.", "Portal, Groups, Quality of Life: Clicking on a row to invite users to a group will no longer redirect to that user’s profile, potentially causing loss of progress/checkmarks. User can still be verified by clicking on their profile button and loading it in a modal (similar to POS).", "Portal, Checkout, Quality of Life: Patrons will be able to finish the checkout process from their cart in a new page. This page allows them to input their details all at the same time rather than having a secondary modal for credit card information", "Portal, Checkout: Service Events properly show up in the cart.", "Portal, Services: Allow service bookings to be set up to be one hour long but start on the top of the hour (8:00) or half-hour (8:30), as the patron wishes. Added new drop-down option on the edit Service page.", "Portal, Quality of Life: Patrons can more easily view their transactions in a page that isn’t constrained by a small portion of the screen. Staff has access to the same larger format page to view patron transactions. Admins have a larger page to do refunds on.", "Portal: On mobile, users are able to click on a displayed icon for a QR code to bring up their code to scan.", "Portal, Printer: The page that manages the kitchen printer refreshes once an hour so as to prevent the page to quit working from being open for extended periods of time.", "Portal, Check-In: Add the tab “Check In History” to the User Profile - allow Staff to view a list of all of that user’s check-ins to the facility.", "Portal, Check-In: Provide a way for staff to check in a patron that does not have their QR code card.", "Portal, Check-In: Display a list of all check-ins from the current day, with time, username and waiver status listed. Staff can click on each user to view their profile.", "Portal, Check-In:Provide a method for patrons to scan a QR code in order to check in to the facility.", "Pos, Check-In: Provide a means for staff to view the most recent check-ins and pull them up on their screens to see more information or make changes for the user", "POS: A new unique ID has been added to every item in an order in the POS. This prevents the bug that caused items to be duplicated by selecting items for the cart and then selecting or changing a user.", "POS: Creating a user in the POS no longer forces the POS to refresh. Creating that new user also selects that new user as the active user in the POS.", "POS: Fixed the sizing on the POS Checkout screen so the title, customer and discounts are more compact, and prevents the total from blocking the Checkout button.", "API, Database: Backend change for a fix to the duplication bug when a user is selected after order items.", "Backend: Fixed an issue with some logins.", "Backend: Added automatic logging to errors so as to more accurately pinpoint where problems are coming from when there's an error.", "Bug Fix: Fix requires_membership being saved as (true) in the event wizard when it shouldn’t be used.", "Bug Fix, Services: Fixed the Safari-specific error on the page to view/respond to a Service booking cancellation request.", "<PERSON><PERSON>x, Portal, Refunds: Hid some text and fields on the refund page from patron view.", "Bug Fix: Fix permission for Company Admin to edit a coupon.", "<PERSON><PERSON> Fix, Group Dashboard: Fixed a bug where after searching a group by ID, clearing that field wouldn’t refresh the search.", "<PERSON><PERSON>x, P<PERSON>, Quality of Life: The cash helper buttons ($1, $5, $10, $20) are now unselectable if it would cause a negative balance of change.", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>: Signing the waiver in the POS no longer causes a refresh of the POS and is updated when signed.", "<PERSON><PERSON>x, Event Wizard: Fixed a bug where in the Event Wizard, the toggle for “one time slot” was being automatically applied, even though it wasn’t selected.", "<PERSON><PERSON>, Event Wizard: Fixed an issue where location names in the Event Wizard were all returning as “Impact NY” instead of their proper names."]}, {"id": "05.25.2022", "title": "May 25th, 2022", "subtitle": "Release Version 0.24", "notes": ["Documentation: Instructions and screenshots for <PERSON><PERSON> to send to his clients who are transitioning to our new booking system.", "Portal: Change the earliest available time for Services to 5:00 AM.", "Portal: Change the display of tokens in the wallet to listing every individual token along with its expiration date.", "Portal, Tokens: Provide a way for staff to view a patron's tokens from the User Profile page.", "Portal, Refunds: Display for Refunds includes who completed the transaction (on completion for initial purchase and refund end). Staff cannot complete refunds on their own accounts and are properly unable to initiate a refund.", "Portal, Refunds: Refunds for tokens have been disabled through the normal refund process. There will later be a separate process for refunding tokens.", "Portal, Registration, Bug Fix: When registering for an account, if a username is already taken, the error is being properly displayed.", "Portal, User Profile: mproved display for some columns on the transaction table.", "<PERSON>, <PERSON><PERSON>, <PERSON>ug Fix: Ensured that the “for user” is properly being displayed in the cart when adding family charges.", "<PERSON>, <PERSON><PERSON>, Bug Fix: Bug Fix: Staff no longer see a cart icon on the “outstanding orders” that a patron would see to add them to their cart. Staff do see the cart icons if they visit their own profile as a patron would.", "Portal, Car<PERSON>, Bug Fix: Fixed a bug with patron cart display.", "Portal, Bug Fix: Group types are populated dynamically from the database on any dropdowns using group types.", "Portal, Bug Fix: Removed service events from any event creation/sorting dropdowns and categories.", "Portal, Bug Fix: Fixed a bug that prevented transaction table to be sorted by “payment method” and “type”", "Portal, Bug Fix: Hitting the enter key on any search bar should no longer cause a page refresh.", "POS: Bug Fix: Sub categories persist when clicking on the same category a second time.", "POS Bug Fix: In the POS, order is cleared from the sidebar when the print screen appears so if there’s an error with print, there’s no interference to POS functionality.", "POS: Products in the POS can easily be duplicated with their addons and further adjusted if need be.", "POS, Quality of Life: Usernames display in the POS user search for further ease of patron identification.", "POS, Portal: Re-enabled the kitchen ticket that disappeared when the grill printer was enabled", "Portal, Printer: On the portal allow assigning a special printer to a product on the product edit page.", "Portal, Printer: Create a page to check for grill tickets to print", "Backend, Printer, API: Backend endpoints to retrieve grill print jobs, to mark them as completed, and to list all printer locations.", "Backend, Printer, Database: Database changes to support printer locations", "Backend, Printer: Backend change to send orders to the correct printers on completion", "Backend, Bug Fix: Subtotal does not change based on tip amount; tip addition is added to the final price.", "Backend, Bug Fix: Service events were showing on the public facing site when they should not be, hide all Service related events from that view.", "Backend, API: Backend change to cut down on extra network traffic and speed up response times for events.", "Backend: Turn off location conflicts for the Team Training Room - temporary solution until we come up with a more elegant way of allowing <PERSON><PERSON> to have multiple sessions in the same space at the same time without conflicts.", "System created to support printing directly to a printer at a remote location."]}, {"id": "05.11.2022", "title": "May 11th, 2022", "subtitle": "Release Version 0.22", "notes": ["Portal, Services: After completing a service booking, the list of patron’s services are now properly refreshing and showing new booked services.", "Portal, Event Wizard: Prevented certain event types from appearing on the event creation wizard that are unique to services.", "Portal, Event Wizard: Removed the “membership required” text from Event Summary when a new event is created.", "Portal, Event Wizard: User (staff/admin) is prompted to select a day in the event wizard if they have not done so before moving on to the next step.", "Portal, Event Editing: Bug Fix: Event parent now correctly showing parent event when editing events", "Portal: Fixed rendering of data after a change to the router.", "Portal, Services: Bug Fix: Cancellation status is being properly shown on service list.", "Portal, User Details, Transactions: Adjusted transaction pagination so as to not negatively affect accounts with many transactions and create horizontal scrolling.", "Portal, Dashboards: Added an error catcher to group pages and other dashboards/pages where they were missing from calls to retrieve and use data.", "Portal, Reports: Added an all option for report dropdowns", "Portal, Registration: Ensured that date of birth was being saved when a new user registers.", "Portal, Registration: Bug Fix: Fixed error on registration redirection", "Portal, Routing, Registration: Added search parameters to the end of a redirection link (such as being redirected from an event’s card).", "Portal, Routing, Quality-of-Life: Made changes to how routes were rendered so as to cut down on the time it takes pages to load and eliminate some screen flashing that may occur.", "Portal, API, UI-UX, Event Types: fix event types drop down list not showing all events configured on server", "Portal, API, Tokens, Services, Products: Added front and backend changes to allow for monthly subscription token bundles. Includes abilities to create token bundles and associate them with services.", "API, Backend: Backend changes to allow for tokens to be easily retrieved in the portal.", "Memberships, Email: email failed membership charge attempts to <PERSON> for remediation", "POS, Cart: Backend changes to allow for name and address on manual card entries to be optional.", "POS: Empty orders are not removed from the POS until logout/closing the order. Also ensured if there is an open order with or without a user selected, that order is maintained on refresh.", "POS: Modified POS to allow for add-on menu to be opened from the items already selected. Fixed bugs in certain circumstances.", "POS: Ensured changes that affect price (add-ons, discounts) rendered right away."]}, {"id": "04.27.2022", "title": "April 27th, 2022", "subtitle": "Release Version 0.21.1", "notes": ["Ensured patrons are required to enter their whole address for checkout", "Removed membership requirements from event creation wizard and from the event pricing in event edit.", "Event Registration form wording adjusted to include emergency contacts instead of parent/guardian.", "Backend changes to handling time zones.", "Staff can now set how many days a service token is valid for, both from the Service wizard and from the Product Variant screen. On the Service Edit screen there is now a button next to the selected token that allows editing the token variant (price and expiration) in a pop-up modal.", "Allow users to cancel service bookings", "Cut down the time it takes for user/{id} pages to load.", "Text changes applied to the digital waiver.", "Allow users to cancel service bookings", "Going to sign in or register from a specific link redirects back to the original link after login/signup", "Creating a new group does not default to department and prompts for input", "Discounts button on the POS when discounts are available for a selected user", "Allow users to purchase packages of tokens and redeem them when booking a Service", "Calendar “Timeline View” defaults to the current day.", "Replace current My Profile page /profile with User Details view with tabs", "In the event creation wizard, prevents moving forward with the process if all information isn’t selected. Gives time-saving tool to select all/deselect all options for dates.", "Add Reports section to POS with sales and product reports for download.", "Bug Fix: A very particular set of circumstances will no longs cause the POS to crash.", "Bug Fix: Editing non-token bundles correctly default to not being a token bundle", "Bug Fix: Resetting filters on the product dashboard correctly returns all results.", "Bug Fix: Backend change to how orders are handled.", "Bug Fix: Service wizard is properly reflecting edits that change a maximum user limit to none.", "Bug Fix: Proper error being displayed with improper login credentials.", "Bug Fix: Fixed minor bugs in the service wizard.", "Bug Fix: POS will not lock up after a failed credit card entry."]}, {"id": "04.14.2022", "title": "April 14th, 2022", "subtitle": "Release Version 0.21", "notes": [" Backend change to prevent the possibility of interrupted credit card transactions not being updated in the system. ", " Remove Name and Address as required fields for entering credit cards on the POS - they are now hidden and optional ", " Fixed error when switching to Week view on the Calendar ", " Change the way email notifications are sent out so transactions on the portal do not have to wait for emails to be sent before completing. ", " Fixed bug where Staff was not able to check out Company Admins on the POS ", " Fixed bug of Company Administrators not able to assign roles to their staff. ", " Change the tabs in the upper right corner of the POS to say Print Order and Search Orders", " Cancel a subscription when the order is refunded. ", " Prevent reloading the page by hitting the enter key when typing in a search on the User Dashboard", " Remove the double scrollbar on the right side of the Upcoming Events page ", " Add a button into the group management dashboard to allow staff to remove users from a group", " Back-end change for transactions.", " Changed some looks and functionality on the refunds page. ", " Fixed bug in loading Products in the Discounts Wizard. ", " Create notification emails for Services ", " Table on user profile includes first, last, and next bill dates. ", " Backend change to product search.", " Creates a page to access group details: group roster, admin roster, events, group id and size. ", " View a user’s current waiver status and download the signed waiver. "]}, {"id": "04.01.2022", "title": "April 1st 2022", "subtitle": "Release Version 0.20", "notes": [" Add new menu to public facing website. ", " Fix a bug: incorrect total on a receipt.  ", " Fixed a bug: undefined ‘name’ on Payment page  ", " More backend changes to support refunds  ", " Staff can see on POS if a user has a signed waiver or needs to sign one. ", " Add a button on the group management page to allow the removal of members from groups", " Move the video demos to a new page  ", " Flush all local data on Log out in portal to avoid problems with Sticky data", " Fix a bug where the patron cart interfered with the POS", " Prevent users from adding the same unpaid event fee to the cart more than once", " Made small visual improvements to the new event registration page", " Remove unused page for paying for events  ", " Create endpoint that allows users to unregisters themselves from events they have not paid for.", " Users can unregister themselves from events they have not paid for.", " As an admin, updating a customer’s information no longer causes an override of your own stored information. ", " A time value was added to transactions table on user’s  profile", " Staff can sign waivers for users via details and via POS. ", " Backend changes to support the refunds functionality  ", " Update Impact public website with new Volleyball information", " Create 'Kitchen Ticket' with larger font-size and no prices", " Create tool for Managers to create services that are bookable in timeslots by patrons using the new shopping cart.", " Define the Add Product Wizard ", " Transactions open order modal which allows admins to initiate refunds or just view order details.", " Added ability to issue refunds from the orders in POS and from the user transaction tab"]}, {"id": "03.17.2022", "title": "March 17th 2022", "subtitle": "Release Version 0.19", "notes": ["Fixed error when entering a price less than $1.00 in the Event Wizard.", "Added common cash denominations as selectors on POS payment", "Change endpoint for processing credit card to include transaction_status_id", "Change endpoint for order/id to include user name. ", "Subtotal is now properly displaying price without admin fee.", "Add link buttons to video demos with tutorials on Release Info page.", "When creating an Event Series with the <PERSON>: make the parent status Confirmed and the children status Reserved. This allows staff more easily to track which is the parent with the registration information for now.", "Changes to the filter dropdowns on the Event Dashboard.", "Make sure home page is not showing child events on the new slider, just the parents.", "Fixed console errors appearing when home page loaded. ", "Fixed a bug where one My Group’s height was being applied to other components.", "The profile image on the homepage is hidden unless viewed on a mobile device.", "The user profile image on the Point of Sale page is now square.", "The email field on the user profile editing page had the wrong name.", "Make sure that user details retain the new value if they are edited and then the page is refreshed.", "Change Success message on 'Pre registration'", "Card info is now automatically saved for use in recurring subscriptions. When purchasing a subscription, the POS will prompt the user to check that the customer understands their payment info will be saved and billed until the subscription is cancelled.", "Backend change to group_type", "On the new location save, the user is redirected to the locations dashboard.", "Add Event Registering to User portal ", "Add ordering for users through portal. ", "Allow for reorganizing of Navigational Elements by an admin on the left to customize the order on the left and top navs."]}, {"id": "03.10.2022", "title": "March 10th 2022", "subtitle": "Release Version 0.18", "notes": ["Back-end change to allow displaying whether a user has signed a waiver or not.", "Create a display for the fillable waiver and show it during new account creation.", "Back-end change to support registering for events via the shopping cart.", "Fix a back-end bug where shipping total was not being returned correctly.", "Password reset email was being applied to the logged in user's account instead of the user selected.", "Fixed a bug where trying to reset a password for a user actually attempts to reset the staff member’s password.", "Added timestamp to regular and kitchen receipts. ", "Back-end change to check if an event type is a service type or not.", "Back-end Fix error where users could not see all events properly on the calendar.", "Fixed back-end bug where sort order was not being followed for modules.", "Update /event endpoint so accommodate new options for service events.", "Fixed the group_type endpoint that was returning an error.", "Sorting for Menu items ", "Backend change to allow for drag and drop reordering of the modules.", "Fix bug where add new user form does not display in POS and User Dashboard.", "Users see completed transactions (status 7). Admins (roles 1-4) have access to a dropdown to filter by other transaction types. Status is displayed on the table for admins.", "Set 'User search' text input to blank string when 'Change User' button is hit on POS Side Bar", "Add date/time to the top of kitchen receipts. ", "Tutorial for Impact Staff for how to set up & edit the discounts (multiple types - fixed/ coupon/etc)"]}, {"id": "02.04.2022", "title": "February 2nd, 2022", "subtitle": "Release Version 0.17", "notes": ["Change the format of User DOB to prevent further iOS errors on the profile and other user pages. This fixes breaking errors in Safari. ", "Users can be successfully checked for invitation when inviting users to a group.", "Change a child event’s default date to be the parent event’s start date.", "Event Wizard correctly uses typeahead to retrieve groups and users on search input.\nEvent Management was converted to paginated search (like user/group dashboards).", "Fixes to the Group page and how it retrieves and shows the user lists.", "Dates “Before”, 'On', and 'After' properly display correct search results.", "Report dates are correctly utilizing time to reflect the beginning and end of the day. <PERSON><PERSON> also gives a visual cue that download is in progress and the user is unable to generate another report until the download is complete.", "Back End: Allow retrieving a list of Group Types. ", "When users upload a profile picture they are able to crop it to a square size. This will also allow larger photos to be uploaded and will be resized automatically.", "Create support for Event series "]}, {"id": "01.25.2022", "title": "January 25th, 2022", "subtitle": "Release Version 0.16", "notes": ["Set 'User search' text input to blank string when 'Change User' button is hit on POS Side Bar", "User notes was not saving notes longer than 255 characters, this has been fixed so that any length of note is saved.", "If a user receives an authentication error / session expired while using the Event Wizard they will now be sent to the login screen and returned to the Wizard when they have logged back in.", "Fix the 'Pending Charges' buttons on the POS - they were not adding items to the cart correctly.", "Fix/Improve Subscription Data info ", "Backend fix to saving images. ", "Adjust preferences for receipt printers.", "Fix 'Invite users' screen on Group Details page to properly paginate and use", "Fix bugs in Calendar: display of event data, prevent changing of month when menu link is clicked, prevent errors from popping up, show date picker when month name is clicked.", "Events Management Page will now have the ability to filter by date, event type, event status, location and ID.", "Change the search words on User lookup to AND instead of OR", "Admin will now have the ability to request reports for various dates and filter by cash, products, categories, payment methods.", "Add 2 new fields to request object in endpoint /group/list", "Make the Book Event button on the Calendar page go to the wizard instead of the old booking page.", "Make Meta events show only at top of of the calendar instead of cluttering up the center of the day and week views.", "Create a new access point to get a list of all groups.", "Groups Dashboard will now properly get a page of data at a time instead of getting the entire list - this will speed up viewing of that page.", "Allow admins to add notes attached to specific Users as per Docs in Confluence.", "Add additional admin role check when inviting users to groups.", "Fix error in so that admins and owners can edit the roles of users of their role or lower."]}, {"id": "01.12.2022", "title": "January 12th, 2022", "subtitle": "Release Version 0.15", "notes": ["Bug fix: Return the correct number of user notes. ", "On Events management module default to retrieve only events that end after yesterday", "Fix “not authorized” error when looking up users who are company admins.", "Update the name and price of the Impact Access Pass to Annual Facility Fee.", "Allow users to view livestream recordings ", "Fix incorrect change being displayed on receipt. ", "Remove registers tab from POS ", "Change user lookup in POS to actually do backend searching - cuts down on the time it takes to perform a search.", "Provide access to Server Error Logs for the dev team to be able to view errors and debug more easily.", "Group events are filtered by the specified group. ", "Allow for Admin to add/edit user profile image ", "Format the confirmation email to be more readable, and include add-ons listed with the product.", "Create new Event Wizard for creation of events - single  events, parent/child events, and recurring events.", "Make user’s profile image on the portal home page editable by the user.", "Create endpoints for generating various reports. "]}, {"id": "12.23.2021", "title": "December 23rd 2021", "subtitle": "Release Version 0.14", "notes": ["Fixed /group/invite endpoint returning 500 error instead of error message", "Manual CC info entry: Re-enabled manual credit card entry on the POS Payment screen. Address fields have been added, which will be filled in automatically with the selected user’s address if it is available.", "Rewrote POS to Use Back End Price Calculations: POS updated to use the proper back-end price calculations that include discounts.", "Display a “Discounts” button to open up a small window displaying all the available discounts for that specific order. Allow the user to select what discount(s) to apply. A user (not the guest account) must be selected to view available discounts.", "Show discounted item prices on the POS and receipts.", "Display the total amount saved on the POS and receipts.", "Note: coupon codes are not yet active, only auto apply discounts.", "Employees receive 20% discount on products. ", "Added admin role check and additional field to group/invite endpoint.", "Added is_signup flag to events table. ", "Created docs for User Group Pages for Patrons: Defining Group Pages for Patrons", "The /group/invite endpoint now accepts multiple users.", "Added Support for Applying Discounts to Order/Order Items on Order Add/Update (API Changes) Back-end updates to support the use of coupons and discounts including the following:  Calculating discounted item prices; Calculating discounted totals and taxes; Selecting valid coupons from the database ", "Users can now be invited to groups through Group Management in the admin portal."]}, {"id": "12.08.2021", "title": "December 8th, 2021", "subtitle": "Release Version 0.13", "notes": ["The admin fee value for each transaction will be stored in its own column in addition to being part of the total transaction amount", "The POS will no longer allow products (excluding products of type Food & Drink and Physical) to be checked out without selecting a customer", "The /user API call now returns whether or not pending events require membership", "Added a page (portal.impactathleticsny.com/set-name-pass) for new users to set their username and password before logging in the first time. It will now be possible to create an email template to link new users who sign up via event registration directly to this page, instead of requiring them to reset their username and password through the normal process.", "Added API calls for discounts/coupons ", "Added discount/coupon creation wizard for admins ", "Added register management for admins ", "Added user wallet view to homepage ", "Users/staff can be assigned to registers ", "Registers can be created/edited by their location ", "Updated portal homepage layout "]}]