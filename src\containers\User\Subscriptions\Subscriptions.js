import React, { useState, useEffect } from 'react';
import { Link } from "react-router-dom";
import Container from 'react-bootstrap/Container';
import Col from 'react-bootstrap/Col';
import Row from 'react-bootstrap/Row';
import Table from '../../../components/common/Table'
import './Subscriptions.css';
import SubHeader from '../../../components/common/SubHeader';
import Memberships from '../../../api/Memberships';
import ChangePassword from '../Profile/ChangePassword';

const Subscriptions = (props) => {

    const [loading,setLoading]=useState(true);
    const [memberships,setMemberships]=useState([]);

    // first load, get memberships from api
    useEffect(() => {
        let mounted = true;

        setLoading(true);
        // get all memberships
        Memberships.get()
        .then(response => {
            if(mounted) {
                setMemberships(response.data);
                setLoading(false);
            }
        }).catch(e => console.error(e));

        // cancel stuff when component unmounts
        return () => {
            mounted = false;
            setLoading(false);
            setMemberships([]);
        }        
    },[]);


    const columns = React.useMemo(
        () => [{
            id: 'table',
            columns: [
                {
                    Header: 'Name',
                    id: 'name',
                    accessor: 'name',
                    className: "align-middle",
                },
                {
                    Header: 'Price',
                    id: 'billing_price',
                    className: "align-middle",
                    accessor: d => (
                        <div>
                            ${d.billing_price.toFixed(2)} /{d.billing_period === 0 ? "month" : "year"}
                        </div>
                    ),
                    sortType: (rowA, rowB, id, desc) => {
                        if (rowA.original[id] > rowB.original[id]) return -1; 
                        if (rowB.original[id] > rowA.original[id]) return 1;
                        return 0;
                    }
                },
                {
                    Header: 'Users',
                    id: 'users',
                    accessor: 'users',
                    className: "align-middle",
                },
                {
                    Header: 'Description',
                    id: 'description',
                    className: "align-middle",
                    accessor: d => (
                        <span dangerouslySetInnerHTML={{ __html: d.description }} />
                    )
                },
                /*{
                    id: 'id',
                    show: false,
                    url:"/users/:id",
                    show:false,
                },*/
            ],
        }],[]
    );
        
    return (
        <Container fluid>
            <SubHeader items = {[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { text: "Manage Subscriptions" },
            ]} />
            <Row className="body">
                <Col>
                <ChangePassword />
                    <div className={`${loading?" loading":""}`}>
                        <Table columns={columns} data={memberships} />
                    </div>
                </Col>
            </Row>
        </Container>
    );
}

export default Subscriptions;