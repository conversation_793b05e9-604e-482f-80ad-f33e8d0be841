** Top level - New Product:
src\containers\Product\NewProduct\NewProduct.js
    This component gets all the general data for passing down (product types/status)
    This component also holds the top level variant, basic info, and bundle objects
    Contains error handler for local required errors
    Gets random data - product types, product statuses, registers for categories
    Gets extra data 
        && Addon Typeahead
        && Category Typeahead
        && Location Printer Typeahead
    ** "resetChildren" is used as a trigger to the child components.  
    When it first loads with a product to edit, it holds a ref to know when all the elements are done loading

    ** Display Details
    src\containers\Product\NewProduct\DisplayDetails\DisplayDetails.js
        Originally shows when a product is visited.  It shows a view version of the product because there's a LOT of info; 
        when just viewing, sometimes all the edit fields are too much.  Is toggled as a hide show from the new product.  
        When entering as a modal for editing a product, this is overridden with the passing of props to NewProduct

    ** Basic Product Details
    src\containers\Product\NewProduct\BasicProductDetails\BasicProductDetails.js
        This holds basic, overall product information.
        Home for the printer and category typeaheads
        
    ** Variants
    src\containers\Product\NewProduct\Variants\Variants.js
        Variants handles the structuring of the variant part of the product.  
        If a field is !undefined, it will add that field to the variant
        This component passes state down to all the types of variants
        
        && EachBasicVariant 
            - this includes the name, description, and basic info on each product
        && SingleVariants
            - if there is only one variant, quick display, eliminates the need for some duplicate information
        && SubscriptionVariant
            - allows for variants but with billing cycles and activation fees
            - allows for a bundle to be added on
            - subscriptions have many custom fields that are even applied to a single variant, as such, subscriptions call single variant and modifications are made there instead of in this
        && TokenVariants
            - only allows for one variant (default)
            - has expires in field
        && ItemizedVariants
            - able to be included in food/physical products
            - allows for SKU/UPC
            - can be marked as shippable
            - allows for measurements and units to be selected
        && AddAVariant
        src\containers\Product\NewProduct\Variants\VariantTypes.js
    
    ** Bundles
        src\containers\Product\NewProduct\BundleOptions\BundleOptions.js

        && BundleOfTokens
        && BundleOfServiceTokens
        src\containers\Product\NewProduct\Bundles\BundleOptions.js

        


When adding a new field to a product, work backwards. 
    - Variant Types: 
        *add fields
    - Variants: 
        *add state/set state
        *in 'getVariant' callback, add the variable from the backend for importing
        *update cleanup useEffect
        *update passing it up useEffect (starts with if(!editingFirstLoad.current.edit))
        *pass into the required variant(variants)
            - note that subscriptions call single variant themselves, since they have many unique fields, even when there is only one variant
    -NewProduct:
        * Only need to make changes in certain circumstances
    -DisplayDetails
        * Add new field conditionally depenent on that variant posessing it (otherwise a lot of variants would could potentially show a lot of unused fields!)