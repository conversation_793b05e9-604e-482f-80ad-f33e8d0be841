import React, { useState } from 'react'
import {<PERSON><PERSON>, <PERSON><PERSON>} from 'react-bootstrap';
import { NewEndpointRow } from './NewEndpointRow';
import { setErrorCatcher, setSuccessToast } from '../../../../utils/validation';

import Permissions from '../../../../api/Permissions';

export const EndpointExists = ({showExists, setShowExists, existing, setSuccess, setError, removeEndpoint}) => {

    const [ endpointForEdit, setEndpointForEdit ]=useState();
    const [ selectedModules, setSelectedModules ] = useState([]);

    const handleDelete =async(id)=>{
        setSuccess(null)
        try{
            let response = await Permissions.Endpoints.delete({id:id});
            handleResponse(response, "Deleted");
        }catch(ex){
            console.error(ex)
        }
    }

    const handleSave = async(id, slug, method)=>{
        setSuccess(null)
        const editObject={id: id}
        if(slug) editObject.slug = slug;
        if(method) editObject.method = method;
        editObject.modules_to_associate = selectedModules?.map((module)=>module.id)
        try{
            let response = await Permissions.Endpoints.edit(editObject);
            handleResponse(response, "Edited");
        }catch(ex){
            console.error(ex)
        }
    }

    const handleResponse=(response, wording)=>{
        if(response.status===200){
            setSuccess(setSuccessToast(`Endpoint ${wording} Successfully`))
            if(removeEndpoint) removeEndpoint(endpointForEdit.new.tempId);
        }
        else if(response.errors){
            setError(setErrorCatcher(response.errors))
        }
    }

    const handleEdit=(endpoint)=>{
        setEndpointForEdit(endpoint)
        setSelectedModules(endpoint.existing.modules)
    }

    const handleFinished=()=>{
        setEndpointForEdit();
        setShowExists(false)
    }

    return (
        <Modal size={'lg'} show={showExists} onHide={()=>setShowExists(false)}>
            <Modal.Header closeButton>
                Endpoint Already Exists!
            </Modal.Header>
            <Modal.Body>
                <div>
                    <p>
                        You're trying to create {existing.length > 1 ? "endpoints" : "an endpoint"} that already exist{existing.length > 1 ? "s": ""}. 
                        You may edit the existing {existing.length > 1 ? "endpoints" : "endpoint"} below.  After editing, they will be removed from your list of new endpoints.
                    </p>
                    <br />
                    {existing.map((match)=>(
                        <p className="existing-endpoint-list" key={`each-existing-${match.existing.id}`}>
                            {match.existing.slug} 
                            <Button onClick={()=>{handleEdit(match)}}>Edit Existing</Button>
                        </p>
                    ))}
                    {endpointForEdit &&
                        <NewEndpointRow 
                            endpoint={endpointForEdit.existing}
                            editEndpoint={true}
                            removeEndpoints={handleDelete}
                            passEndpoints={handleSave}
                            setSelectedModules={setSelectedModules}
                        />
                    }
                    <Button onClick={handleFinished}>Finished With Edits</Button>
                </div>
            </Modal.Body>
        </Modal>
    )
}
