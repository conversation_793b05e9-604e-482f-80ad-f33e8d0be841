import React, { useState, useEffect } from 'react';
import { useH<PERSON><PERSON>, Link } from "react-router-dom";
import { Container, Row, Col, Button, Table, Card } from "react-bootstrap";

import Stack from '../../../components/common/Stack';
import Pagination from '../../../components/common/Pagination';
import { authUserHasModuleAccessMany } from "../../../utils/auth";
import SubHeader from '../../../components/common/SubHeader';
import Registers from '../../../api/Registers';

const CREATE_GROUPS_WIDGET_ID = 87;     // create register groups
const EDIT_GROUPS_WIDGET_ID = 88        // edit register groups

const Dashboard = (props) => {
    let history = useHistory();

    const [loading, setLoading] = useState(true);
    const [registerGroups, setRegisterGroups] = useState([]);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();
    const [totalItems,setTotalItems]=useState(0);
    const [itemsPerPage,setItemsPerPage]=useState(25);
    const [page,setPage]=useState(1);
    const [pages,setPages]=useState([]);
    const [search,setSearch]=useState("");
    const [userHasModulePermission, setUserHasModulePermission] = useState(false);

	useEffect(() => {
        let mounted = true;

        setLoading(true);
        Registers.Groups.get() 
        .then(response => {
             if (mounted)
               setRegisterGroups(response.data);
            setLoading(false);
        })
        .catch(e => console.error(e));

        const checkPermission = async () => {
            try {
                let response = await authUserHasModuleAccessMany([CREATE_GROUPS_WIDGET_ID, EDIT_GROUPS_WIDGET_ID]);
                setUserHasModulePermission(response);
            } catch (error) { console.error(error) }
        }
        checkPermission();

        return () => {
            mounted = false;
            setLoading(false);
            setRegisterGroups([]);
        }
	}, [props]);

    function newButtonHandler() {
        history.push("/p/registers/groups/create");
    };

    return (
      <Container fluid className="full-height">
        {success}
            <SubHeader items = {[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/registers" }, text: "Registers" },
                { text: "Register Groups" },
            ]} />

            <Card className="content-card">
                <Stack direction="horizontal" gap={2}>
                    <h4 className="tm-1 order-2 order-lg-1 section-title">Register Groups</h4>
                    <div className="ms-sm-auto d-flex justify-content-end order-1 order-lg-2 service-dash-btns mb-1">
                        {userHasModulePermission[CREATE_GROUPS_WIDGET_ID] &&
                            <Button variant="primary" onClick={newButtonHandler}>
                                New Register Group
                            </Button>
                        }
                    </div>
                </Stack>
                <div className={`${loading ? " loading" : ""}`}>
                    <Table striped hover>
                        <thead>
                            <tr className="no-select-text">
                                <th>
                                    <span>ID</span>
                                </th>
                                <th>
                                    <span>Name</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {registerGroups?.map(groups => (
                                <tr key={`register-row-${groups.id}`}
                                    onClick={userHasModulePermission[EDIT_GROUPS_WIDGET_ID] ? ()=>{history.push("/p/registers/groups/"+groups.id)} : null}
                                    className={`register-row`}
                                >
                                    <td>{groups.id}</td>
                                    <td>{groups.name}</td>
                                </tr>
                            ))}
                        </tbody>
                    </Table>
                    {pages.length>1?
                        <div className="d-flex justify-content-end">
                            <Pagination
                                itemsCount={totalItems}
                                itemsPerPage={itemsPerPage}
                                currentPage={page}
                                setCurrentPage={setPage}
                                alwaysShown={false}
                            />
                        </div>
                    : null}
                </div>
            </Card>
        {error}
      </Container>
    );
};

export default Dashboard;