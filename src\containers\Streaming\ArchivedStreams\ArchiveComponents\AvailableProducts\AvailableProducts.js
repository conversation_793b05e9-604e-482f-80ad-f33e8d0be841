import React, { useEffect } from 'react'
import { checkAllStreamViewability, groupStreamsByYear } from '../../ArchiveStreamsUtils'
import Accordion from '../../../../../components/common/Accordion'
import StreamButton from './StreamButton'

const AvailableProducts = (props) => {
	const {streamList, setError, setSelectedProductId, selectedProductId, purchased, setPurchased} = props
	
	useEffect(() => {
		checkAllStreamViewability(streamList)
	}, [streamList])
	
	// Prepare streams for display
	const prepareStreamList = () => {
		// Add content property for each stream
		streamList.forEach((stream) => {
			stream.display = 
				<p key={`stream-prod-id-${stream.product_ids.prod}-stream-name-${stream.name}`} className="each-stream-event">
					<span className="event-name">
						{stream.name}
						{stream?.description &&
							<>
								<br />
								<span>{stream?.description}</span>
							</>
						}
					</span>
					<StreamButton 
						stream={stream} 
						setError={setError} 
						selectedProductId={selectedProductId}
						setSelectedProductId={setSelectedProductId}
						purchased={purchased}
						setPurchased={setPurchased}
					/>
				</p>
		})
		
		// Group streams by year
		return groupStreamsByYear(streamList)
	}
	
	const yearGroupedStreams = prepareStreamList();
	
	return (
		<div className="available-products">
			<Accordion 
				items={yearGroupedStreams}
				primary={false}
			/>


			{/* {streamList.filter(stream=>stream.status).map((stream)=>(
				<p key={`stream-prod-id-${stream.product_ids.prod}`} className="each-stream-event">
					<span className="event-name">
						{stream.name}
						{stream?.description &&
							<>
								<br />
								<span>{stream?.description}</span>
							</>
						}
					</span>
					<StreamButton 
						stream={stream} 
						setError={setError} 
						selectedProductId={selectedProductId}
						setSelectedProductId={setSelectedProductId}
						purchased={purchased}
						setPurchased={setPurchased}
					/>
				</p>
			))} */}
		</div>
	)
}

export default AvailableProducts
