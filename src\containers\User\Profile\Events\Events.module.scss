@import '../../../../assets/css/scss/mixins';
@import '../../../../assets/css/scss/variables';

.profile-event-wrapper{
    select{
        @include basic-input-select;
    }
    label{
        @include basic-label;
    
    }
}
.event-display-wrapper{
    border: solid 1px $divider-color;
    padding: 5px;
    margin-bottom: 12px;
    border-radius: 5px;
    background-color: $card-background-color;
    container: event-details / inline-size;
    &.past-event{
        filter: brightness(85%);
    }
    h6{
        margin-top: 1rem;
        margin-bottom: 0;
    }
    hr{
        margin-top: .5rem;
    }
    .each-event{
        .main-details{
            display: flex;
            flex-direction: column;
            p{
                margin-top: 1px;
                margin-bottom: 3px;
                span{
                    min-width: 200px;
                    margin-right: 1rem;
                }
            }
            .detail-div{
                display: flex;
                flex-direction:row;
                max-width: 500px;
                justify-content: space-between;
            }
        }
        .bold{
            font-weight: 700;
        }
        margin-bottom: 8px;
        .order-details{
            max-width: 250px;
        }
        .order-variant{
            display: flex;
            justify-content: space-between;
        }
    }
    .each-order{
        p{
            margin-bottom: .05rem;
            margin-left: .5rem;
        }
    }
    .event-extra-details{
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    //since custom styling can be utilized within these 
    //(and dev has some pretty extreme test cases), we'll be extra safe with our container queries here.
    @container event-details (max-width:400px){
        .description-section, .main-details{
            max-width: 200px;
        }
        .main-details .detail-div{
            display: flex;
            flex-direction: column;
        }
    }
    @container event-details (min-width: 401px) and (max-width:500px){
        .description-section, .main-details{
            max-width: 400px;
        }
    }
    @container event-details (min-width: 501px) and (max-width:800px){
        .description-section, .main-details{
            max-width: 600px;
        }
    }
    @container event-details (min-width:801px){
        .description-section, .main-details{
            max-width: 800px;
        }
    }
    .head-labels{
        @include basic-label;
        margin-top: .5rem;
    }
}