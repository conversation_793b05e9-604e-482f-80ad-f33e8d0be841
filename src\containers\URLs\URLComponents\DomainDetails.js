import React from 'react';

export const DomainDetails =({website, handleShowEdit, theme, styles, websites, canEdit})=>{

    return(
        <div 
            className={styles["each-subdomain-wrapper"]} 
            onClick={canEdit ? 
                ()=>handleShowEdit(website)
                :
                null
            }
        >
            {/* <div className={styles["default"]}>
                
            </div> */}
            <div className={styles["domain"]}>
                {website?.subdomain ?
                    <span>
                        {website?.subdomain}.{website.domain}
                    </span>
                    :
                    <span>
                        {website?.domain}
                    </span>
                }
            </div>
            <div className={styles["website"]}>
                {websites?.filter((site)=>site?.id === website?.website_id)[0]?.name}
            </div>
            <div className={styles["theme"]}>
                {theme ? theme[0]?.name : null}
            </div>
            <div className={styles["home"]}>
                {website?.index_page}
            </div>
            {/* <div className={styles["active"]}>

            </div>
            <div className={styles["dns"]}>

            </div>
            <div className={styles["ssl"]}>

            </div> */}
            <div className={styles["delete"]}>
                {" "}
            </div>
        </div>
    )
}