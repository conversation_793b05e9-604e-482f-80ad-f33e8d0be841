import React, { useState, useEffect } from 'react';
import { Container, Card, Button } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';

import SubHeader from '../../components/common/SubHeader';
import { DetailSubpage } from '../../components/common/DetailSubPage/DetailSubpage';
import Dashboard from './Dashboard/Dashboard';
import Create from './Create';

export const GiftCards = (props) => {

    const location = useLocation();

    const [currentTab, setCurrentTab] = useState(location?.hash || "List");

    const allTabs=[
        {
            id: 1,
            displayName: "GiftCard List",
            hash: "List",
            moduleId: null,
            component: <Dashboard />,
            link: null, 
            icon: ""
        },
        {
            id: 2,
            displayName: "Create",
            hash: "New",
            moduleId: 322,
            component: <Create />,
            link: null, 
            icon: ""
        }
    ]

    return (
        <Container fluid>
            <SubHeader  items = {[
                    { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                    { text: "Gift Card Dashboard" },
                ]} 
                /*tutorials = {[
                    { tutorialSection: "Gift Card", allSectionTutorial: true, navigationTutorial: false, basicInfo: true }
                ]}*/ 
            
            />
            <Card className="content-card">
                <h4 className="section-title">
                    Gift Cards
                </h4>
                <DetailSubpage 
                    allTabs={allTabs}
                    loading={false}
                    currentTab={currentTab}
                />
            </Card>
        </Container>
    );
}

export default GiftCards;