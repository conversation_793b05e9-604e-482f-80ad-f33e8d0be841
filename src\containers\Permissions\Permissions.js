import React, { useState, useEffect, useRef } from 'react';
import { usePara<PERSON>, Link } from "react-router-dom";
import { Container, Card } from 'react-bootstrap';
import SubHeader from '../../components/common/SubHeader';
import { getFeatures } from './PermissionsUtils/PermissionUtils';
import CompaniesAPI from '../../api/Companies';
import UserAPI from '../../api/Users';
import GroupAPI from '../../api/Groups';
import PermissionAPI from '../../api/Permissions';

import './PermissionsUtils/Permissions.scss'
import ErrorCatcher from '../../components/common/ErrorCatcher';
import ModulePermissions from './ModulePermissions';
import Tutorials from '../../components/Tutorials';

// TODO: I would like to split this into 3 wrapper components, one for each type of permission, each passing the correct props to the ModulePermissions component

export const Permissions = () => {

    const { company_id, user_id, group_id } = useParams();

    const [ error, setError ] = useState(null);
    const [ success, setSuccess ]= useState(null);
    const [ features, setFeatures ] = useState(null);
    const [ company, setCompany ] = useState(null);
    const [ user, setUser ] = useState(null);
    const [ group, setGroup ] = useState(null);
    const [ loading, setLoading ] = useState(false);
    const [ loadError, setLoadError ] = useState(null);
    const [ permissionLevels, setPermissionLevels ] = useState(null);
    const adminDash = JSON.parse(localStorage.getItem("adminDash"));
    const ownerDash = JSON.parse(localStorage.getItem("ownerDash"));

    const mountedRef = useRef(false);
    
    useEffect(()=>{
        mountedRef.current = true

        const getFeaturesWithModules=async()=>{
            try{
                let response = await getFeatures();
                if(response.data && mountedRef.current) setFeatures(response.data)
                else setError(<ErrorCatcher error={response.errors}/>)
            }catch(ex){
                console.error(ex)
            }
        }

        // we are loading this here just so we can do it once for the entire page and not have to constantly make api calls for the list
        const getPermissionLevels = async () => {
            try {
                let response = await PermissionAPI.PermissionLevels.get();
                if (response.data && mountedRef.current) setPermissionLevels(response.data);
                else setError(<ErrorCatcher error={response.errors}/>)
            } catch(ex) {
                console.error(ex)
            }
        }

        getFeaturesWithModules();
        getPermissionLevels();

        return () => mountedRef.current = false;
    },[]);

    useEffect(()=>{

        const getCompany = async() => {
            setLoading(true);
            try{
                CompaniesAPI.get({"id": company_id})
                .then(response => {
                    if (response.data && mountedRef.current) {
                        setCompany(response.data[0]);
                        setLoading(false);
                    }
                    else {
                        setError(<ErrorCatcher error={response.errors}/>);
                        setLoading(false);
                    }
                });
            }catch(ex){
                console.error(ex)
            }
        }

        if (company_id) getCompany();
    },[company_id]);

    useEffect(()=>{
        const getUser = async() => {
            setLoading(true);
            try{
                UserAPI.get({"id": user_id})
                .then(response => {
                    if (response.data && mountedRef.current) {
                        if (response.data.length>0) {
                            setUser(response.data[0]);
                        } else {
                            setLoadError("User not found");
                        }
                        setLoading(false);
                    } else {
                        setLoadError(response.errors);
                        setLoading(false);
                    }
                });
            }catch(ex){
                console.error(ex)
            }
        }
        if (user_id) getUser();
    },[user_id]);

    useEffect(()=>{
        const getGroup = async() => {
            setLoading(true);
            try{
                GroupAPI.get({"id": group_id})
                .then(response => {
                    if (response.data && mountedRef.current) {
                        if (response.data.length>0) {
                            setGroup(response.data[0]);
                        } else {
                            setLoadError("Group not found");
                        }
                        setLoading(false);
                    }
                    else {
                        setLoadError(response.errors);
                        setLoading(false);
                    }
                });
            }catch(ex){
                console.error(ex)
            }
        }
        if (group_id) getGroup();
    },[group_id]);

    // create the ["Individual", "Refunds", "Advanced"]crumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/" }, text: "Home" }
    ];

    // if adminDash is true, add the admin dashboard breadcrumb
    if (adminDash) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" });
    }

    // if ownerDash is true, add the owner dashboard breadcrumb
    if (ownerDash) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/owner" }, text: "Permission Dashboard" });
    }

    // show current page breadcrumb
    breadcrumbs.push({ text: "Permissions" });

    // create tutorials array
    const _subSection = user_id ? 
        ["Default", "DefaultRoles", "User", "UserRoles"]
        :
        group_id ?
            ["Default", "DefaultRoles", "Group", "GroupRoles"]
        :
            ["Default", "DefaultRoles", "Company", "CompanyRoles"];

    const tutorials = [
        { 
            tutorialSection: "Assigning Permissions", 
            subSection: _subSection,
            allSectionTutorial: false, 
            basicInfo: true, 
            navigationTutorial: false 
        }
    ];


    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} tutorials={tutorials} />
            <Card className="content-card permissions-wrapper">
                {error}
                {success}
                {loadError ?
                    <div>{loadError}</div>
                :
                loading || !features ?
                    <div>Loading...</div>
                :
                    <div className="permissions-content">
                        {/* this extra div is required for the css hide when screen is too small */}
                        <div> 
                          {user_id ?
                              <ModulePermissions features={features} setError={setError} permissionLevels={permissionLevels} user={user} />
                          :
                          group_id ?
                              <ModulePermissions features={features} setError={setError} permissionLevels={permissionLevels} group={group} />
                          :
                              <ModulePermissions features={features} setError={setError} permissionLevels={permissionLevels} company={company} />
                          }
                        </div>
                    </div>

                }

            </Card>
        </Container>
    )
}
