import React, { useState, useEffect, useRef } from 'react';
import { Button } from 'react-bootstrap';
import { setSuccessToast, setErrorCatcher } from '../../../../utils/validation';

import Permissions from '../../../../api/Permissions';

export const ExternalMenuItem = ({activeItem, companyId, ...props}) => {

    const mountedRef = useRef(false)
    const [url, setUrl]=useState("");
    const [text, setText]=useState("");
    const [icon, setIcon]=useState("");
    const [disabled, setDisabled]=useState(false);
    const [localError, setLocalError]=useState("")

    useEffect(()=>{
        mountedRef.current = true;

        //we want to unmount and reset everything because if we go to edit a different item that doesn't import all the same fields, there may be some bleed over
        return ()=>{
            mountedRef.current = false
            setUrl("");
            setText("");
            setIcon("");
            setDisabled(false);
            setLocalError("");
        }
    },[])

    useEffect(()=>{
        if(activeItem && mountedRef.current){
            setUrl(activeItem.module.url);
            setText(activeItem.text);
            setIcon(activeItem.icon);
        }
    },[activeItem])

    useEffect(()=>{
        let disabled=false;
        let error = "";
        if(!url || !text){
            disabled=true;
            //leaving the error off this one as they're marked required, so the user isn't constantly seeing an error when they first start
        }else if(icon.length > 45 || text.length > 45 || url.length > 256 ){
            disabled = true;
            error = "You have exceeded the character limit for one or more fields"
        }else if (url.trim().length === 0){ //this will detect if it's only whitespace
            disabled = true;
            error = "You cannot have a blank URL"
        } else if (!url.includes('http') && !url.includes('://')){
            disabled = true;
            error="You must use a valid url, with http:// or https://"
        }else {
            disabled = false;
            error = ""
        }
        if(mountedRef.current){
            setDisabled(disabled)
            setLocalError(error)
        }
    },[text, icon, url]);

    const saveMenuLink = async (e)=>{
        e.preventDefault();
        props.setSuccess();
        props.setError();

        let response;

        let moduleItem = {
            name: `${text} - Link`,
            url: url.replace(/ /g, ''), //will trim excess whitespace
            module_type_id: 3,
            company_id: companyId ? companyId : null,
            default_menu_item: {
                icon: icon,
                text: text,
            }
        }
        if(activeItem) moduleItem.id = activeItem.module.id;

        try{
            if(activeItem) response = await Permissions.Modules.update(moduleItem);
            else {
                response = await Permissions.Modules.create(moduleItem)
            }
            if(response.status ===200){
                cleanUp();
                props.setSuccess(setSuccessToast("External Link Added Successfully"))
                if(response.data) props.onClose(false, response.data.id);
                else props.onClose(true, null)
            }
            else if(response.errors){
                props.setError(setErrorCatcher(response.errors))
            }
        }catch(ex){console.error(ex)}
    }

    const cleanUp=()=>{
        setUrl("");
        setText("");
        setIcon("");
        setDisabled(false);
        setLocalError("");
    }

    const handleClose = ()=>{
        cleanUp();
        props.onClose(false);
    }

    return (
        <div className="external-link">
            <form onSubmit={saveMenuLink}>
                <h4 className="section-title">
                    Add External Menu Link
                </h4>
                <div className="input-sets">
                    <p>
                        <label htmlFor="full-url">
                            Full URL<span className="required-star">*</span>:
                        </label>
                        <input
                            required
                            id="full-url"
                            value={url}
                            onChange={(e)=>setUrl(e.target.value)}
                        />
                    </p>
                    <p className={`character-count ${text.length > 256 ? "error-text" : ""}`}>
                        {url.length} / 256
                    </p>
                    <p>
                        <label htmlFor="displayed-text">
                            Displayed Text<span className="required-star">*</span>:
                        </label>
                        <input
                            required
                            id="displayedt-text"
                            value={text}
                            onChange={(e)=>setText(e.target.value)}
                        />
                    </p>
                    <p className={`character-count ${text.length > 45 ? "error-text" : ""}`}>
                        {text.length} / 45
                    </p>
                    <p>
                        <label htmlFor="icon">
                            Icon:
                        </label>
                        <input
                            id="icon"
                            value={icon}
                            onChange={(e)=>setIcon(e.target.value)}
                        />
                    </p>
                    <p className={`character-count ${text.length > 45 ? "error-text" : ""}`}>
                        {icon.length} / 45
                    </p>
                    {localError && 
                        <p className="mb-4 error-text-ctr">
                            {localError}
                        </p>
                    }
                </div>
                <div className="menu-settings-btn-row">
                    <Button onClick={handleClose} variant="danger">Cancel</Button>
                    <Button type="submit" disabled={disabled}>{activeItem ? "Edit Menu Link" : "Add Menu Link"}</Button>
                </div>
            </form>
        </div>
    )
}

export default ExternalMenuItem;