import React, {useState, useEffect, useCallback, useRef, useMemo, useLayoutEffect} from 'react';
import { Button } from 'react-bootstrap';

import ErrorCatcher from "../../components/common/ErrorCatcher";
import Pos from '../../api/Pos';


export const DisableRegisterButton = ({ registerInfo, triggerRefresh=()=>{}, displayError=()=>{} }) => {

    const mountedRef = useRef(false);

	useEffect(() => {
		mountedRef.current = true;

        return () => {
            mountedRef.current = false;
        }
	// eslint-disable-next-line react-hooks/exhaustive-deps
	},[]);

    const setRegisterIsDisabled = useCallback((isDisabled) => {
        Pos.register.edit({
            id: registerInfo.id,
            is_disabled: isDisabled
        })
        .then( async response => {
            if (mountedRef.current && !response.errors) {
                triggerRefresh();
            } else if (response.errors) {
                displayError(<ErrorCatcher error={response.errors} />);
            }
        });
    },[registerInfo, triggerRefresh, displayError]);

    const handleDisableRegisterBtn = () => {
        setRegisterIsDisabled(1);
    }

    const handleEnableRegisterBtn = () => {
        setRegisterIsDisabled(0);
    }

    if (!registerInfo) {
        return <></>;
    }

    return (
        <>
            {!registerInfo.is_disabled ?
                <Button variant="danger" className="disable-button" onClick={() => setRegisterIsDisabled(1)}>Turn Off Ordering</Button>
            :
                <>
                    <div className="register-disabled-warning">
                        <div>Register is OFF<span className="hide-on-small-screens"> - No new orders can be placed</span></div>
                    </div>
                    <Button variant="success" className="disable-button" onClick={() => setRegisterIsDisabled(0)}>Turn On Ordering</Button>
                </>
            }
        </>
    )
}