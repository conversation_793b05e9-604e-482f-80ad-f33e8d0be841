import React from 'react'

//src\containers\MediaManager\MediaManagerWrappers\ResultsWrapper.js

const GridMedia = ({allMedia, setActiveMedia, activeMedia, multiSelect}) => {
    let _activeMedia = activeMedia;
    if (!_activeMedia) _activeMedia = [];
    else if (!Array.isArray(_activeMedia)) _activeMedia = [_activeMedia];
    return (
        <div className="grid-media cp">
            {allMedia?.map(media =>(
                <span 
                    key={`grid-item-${media.id}`}
                    className={`medium-thumb ${_activeMedia.find(m=>m.id === media.id) ? "active-selected" : ""}`}
                    onClick={()=>setActiveMedia(prev=>{
                        if (multiSelect) {
                            let _prevValue = prev;
                            if (!_prevValue) _prevValue = [];
                            else if (!Array.isArray(_prevValue)) _prevValue = [_prevValue];
                            if (_prevValue.find(m=>m.id===media.id)) return _prevValue.filter(m=>m.id!==media.id);
                            else return [..._prevValue, media];
                        } else return media;
                    })}
                >
                    {media.media_type===1 && media.url ? // image
                        <img src={media.url} alt={`a thumnail representing the media ${media.name}`}/>
                        :
                        <span>
                            {media.icon}
                        </span>
                    }
                </span>
            ))}
        </div>
    );
}

export default GridMedia;