import React, { useEffect, useState, Suspense } from "react";
import { useParams, useLocation, Link } from "react-router-dom";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import Container from "react-bootstrap/Container";
import Row from "react-bootstrap/Row";
import Col from "react-bootstrap/Col";
import Card from "react-bootstrap/Card";

import RegisterForm from './RegisterForm';
import SubHeader from "../../../components/common/SubHeader";

import "../Register.scss";

const Edit = (props) => {

    const location = useLocation();
    const { id } = useParams();

    const [name, setName] = useState("Edit");

    return (
        <Container fluid className="register-container edit">
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/registers" }, text: "Registers" },
                { text: name }
            ]} />
        <Row className="body">
            <Col>
            <Card className="content-card">
                <h2>Edit Register</h2>
                <RegisterForm
                    id={id}
                    referer={location.pathname}
                    setDisplayName={setName}
                />
            </Card>
            </Col>
        </Row>
        </Container>
    )
};

export default Edit;
