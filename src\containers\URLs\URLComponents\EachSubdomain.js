import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON>, Modal } from 'react-bootstrap';
import { ThemesTypeahead, WebsitesTypeahead, UrlPagesTypeahead  } from '../../../components/Typeahead';

export const EachSubdomain =({
    siteboss=true, 
    edit=true, 
    website, 
    deleteAvailable=true,
    handleSubmit,
    styles, 
    companyId,
    formRef,
    localErrors,
    handleDelete, 
    canDelete
})=>{

    const [showConfirmDelete, setShowConfirmDelete]=useState(false);

    const localHandleDelete = (id)=>{
        handleDelete(id);
        setShowConfirmDelete(false);
    }

    return(
        <form id="urlForm" onSubmit={(e) =>handleSubmit(e, website.id)} className={styles["edit-column-wrapper"]}>
            {/* <div>
                <label htmlFor="default">
                    Default
                </label>
                <input 
                    id="default"
                    name="default"
                    type="checkbox"
                />
            </div> */}
            <div>
                <label htmlFor ="subdomain">
                    Subdomain
                    {siteboss && 
                        <span className="required-star">
                            {" "}*
                        </span>
                    }
                </label>
                <input 
                    name="subdomain"
                    id="subdomain"
                    defaultValue={website && edit ? website.subdomain : ""}
                />
            </div>
            <div>
                <fieldset disabled={siteboss ? true : false}>
                    <label htmlFor="domain">
                        Domain
                    </label>
                    <input 
                        name="domain"
                        id="domain"
                        defaultValue={website && edit ? website.domain : ""}
                    />  
                </fieldset>
            </div>
            <div>
                <label htmlFor="website">
                    Website
                    <span className="required-star">
                        {" "}*
                    </span>
                </label>
                <WebsitesTypeahead 
                    id="website"
                    name="website"
                    companyId={companyId}
                    passSelection={(selection)=>{
                        if(selection.length > 0) formRef.current.website_id = selection[0]?.id
                        else formRef.current.website_id = null;
                    }}
                    initialDataIds={website && edit ? [website.website_id] : ""}
                    async={false}
                    multiple={false}
                    />
            </div>
            <div>
                <label htmlFor="website_theme_id">
                    Theme
                    <span className="required-star">
                        {" "}*
                    </span>
                </label>
                <ThemesTypeahead 
                    name="website_theme_id"
                    id="website_theme_id"
                    initialDataIds={website && edit ? [website.website_theme_id] : ""}
                    async={false}
                    multiple={false}
                    passSelection={(selection)=>{
                        if(selection.length > 0) formRef.current.website_theme_id = selection[0]?.id
                        else formRef.current.website_theme_id = null;
                    }}
                />
            </div>
            <div>
                <label htmlFor="index_page">
                    Home Page
                </label>
                <UrlPagesTypeahead 
                    name="index_page"
                    id="index_page"
                    passSelection={(selection)=>{
                        if(selection.length >0) formRef.current.index_page = selection[0]?.slug
                        else formRef.current.index_page = null;
                    }}
                    initialDataOdd={edit && website ? {label: "UrlPages", data: website, filter: "index_page"} : ""}
                    websiteId={website?.website_id}
                    formatForLabel={(page)=>page?.title}
                    async={false}
                    multiple={false}
                />
            </div>
            <div>
                {localErrors.length > 0 && localErrors.map((error)=>(
                    <p key={error} className="error-text">
                        {error}
                    </p>
                
                ))}
            </div>
            {/* <div className={styles["active"]}>
                {sbCurrentUser &&
                    <input 
                        type="checkbox" 
                        name="active"
                        id="active"
                    />
                }
            </div>
            <div className={styles["dns"]}>
                {sbCurrentUser &&
                    <input 
                        type="checkbox" 
                        name="dns"
                        id="dns"
                    />
                }
            </div>
            <div className={styles["ssl"]}>
                {sbCurrentUser &&
                    <input 
                        type="checkbox" 
                        name="ssl"
                        id="ssl"
                    />  
                }
            </div> */}
            <div className={styles["final-row"]}>
                <div className={styles["delete"]}>
                    {deleteAvailable && canDelete ? <Button variant = "danger" onClick={()=>setShowConfirmDelete(true)} >Delete</Button> : ""}
                    {showConfirmDelete &&
                        <Modal show={showConfirmDelete} size="sm" contentClassName={styles["confirm-modal"]}>
                            <Modal.Header closeButton>
                                Are You Sure!?
                            </Modal.Header>
                            <Button variant="danger" onClick={()=>localHandleDelete(website.id)}>
                                Confirm Deletion
                            </Button>
                        </Modal>
                    }
                </div>
                <div>
                    {" "}
                </div>
                <div>
                    {edit ? 
                        <Button type="submit">
                            Save
                        </Button>
                    :""}
                </div>
            </div>
        </form>
    )
}