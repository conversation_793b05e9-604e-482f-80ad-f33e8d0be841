import React, { useState, useEffect, useCallback } from 'react';
import { Con<PERSON>er, Card, Button } from 'react-bootstrap';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import DatePicker from 'react-datepicker'
import { format } from 'date-fns';

import SubHeader from '../../../components/common/SubHeader';
import Registers from '../../../api/Registers';
import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';
import { convertTimeString, checkMissingStartOrEnd, checkDupeDates, checkIsOneGreaterTwo } from '../../../utils/pos';
import '../Register.scss';

const defaultAvailable = {
    days: [
        {
           "date":"Sunday",
           "start": null,
           "end": null,
           "closed": false
        },
        {
            "date":"Monday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Tuesday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Wednesday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Thursday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Friday",
            "start": null,
            "end": null,
            "closed": false
        },
        {
            "date":"Saturday",
            "start": null,
            "end": null,
            "closed": false
        }
    ],
    extra_closed: [],
    timezone: "America/New_York"
}

const dateNumbers={
    "Sunday": 0,
    "Monday":1,
    "Tuesday": 2,
    "Wednesday": 3,
    "Thursday": 4,
    "Friday": 5,
    "Saturday": 6
}

export const RegisterHours = ({ ...props})=>{

    const id = useParams()

    const [ success, setSuccess ]=useState(null)
    const [ error, setError ]=useState(null)
    const [ registerAvail, setRegisterAvail ]=useState(null);
    const [ registerDef, setRegisterDef ]=useState(null);
    const [ hasChanged, setHasChanged ]=useState(false);

    const getRegister=useCallback(async()=>{
        try{
            setError(null);
            setSuccess(null)
            let response = await Registers.get(id)
            if(response.status === 200){
                setRegisterDef(response.data[0])
                let available = response?.data[0]?.register_definition?.availability
                if(available){
                    available?.days?.forEach((day)=>{
                        if(day.start) day.start=convertTimeString(day.start)
                        if(day.end) day.end=convertTimeString(day.end)
                    })
                    setRegisterAvail(available);
                } 
                else {
                    setRegisterAvail(defaultAvailable)
                }
            }
            else if (response.errors) setError(setErrorCatcher(response.errors))
            else setError(setErrorCatcher("There was a problem getting the register details"))
        }catch(ex){
            console.error(ex);
            setError(setErrorCatcher(ex));
        }
    },[id])

    useEffect(()=>{
        if(id) getRegister();

        return()=>{
            setError(null);
            setSuccess(null);
            setRegisterAvail(null)
        }
    //eslint-disable-next-line react-hooks/exhaustive-deps
    },[getRegister])

//#region Handlers
    const handleTimeChange = (time, date, when)=>{
        if(error) setError(null)
        let dateNumber = dateNumbers[date]
        let allowed = true;
        
        if(time && when === "end"){
            allowed = checkIsOneGreaterTwo(time, registerAvail.days[dateNumber]?.start);
        }else if(time && when === "start"){
            allowed = checkIsOneGreaterTwo(registerAvail.days[dateNumber]?.end, time);
        }

        if(!allowed) setError(setErrorCatcher("Your end time cannot be earlier than your start time.", true))
        else if(allowed){
            registerAvail.days[dateNumber][when] = time
            setHasChanged(!hasChanged); //changing objects won't register as changes without destructuring and stuff so we'll just tell the component to rerender with a simpler state change  
        }
    }

    const handleCheck = (e, date)=>{
        e.preventDefault();
        let dateNumber = dateNumbers[date]
        registerAvail.days[dateNumber].closed = e.target.checked ? true : false;
        if(e.target.checked){
            registerAvail.days[dateNumber].start = null;
            registerAvail.days[dateNumber].end = null;
        }
        setHasChanged(!hasChanged);
    }

    const handleTimeZone=(e)=>{
        e.preventDefault();
        registerAvail.timezone = e.target.value;
        setHasChanged(!hasChanged)
    }

    const handleRemoveDate=(date)=>{
        let indexOf = registerAvail.extra_closed.findIndex((closedDate)=>date === closedDate)
        registerAvail.extra_closed.splice(indexOf, 1)
        setHasChanged(!hasChanged)
    }

    const handleNewDate=(date)=>{
        //make sure the date isn't already in the list
        setError(null);
        let dateIncluded = checkDupeDates(date, registerAvail?.extra_closed);
        if(dateIncluded){
            setError(setErrorCatcher("This date is already listed", true))
        }else{
            let dateFormat = format(new Date(date), 'MM/dd');
            registerAvail.extra_closed.push(dateFormat);
            setHasChanged(!hasChanged);
        }
    }

    const handleSave = async (e) =>{
        e.preventDefault();
        setSuccess(null);
        setError(null);
        let hasError = false
        for(let i = 0; i < registerAvail?.days?.length; i ++){
            if(!hasError){
                hasError = checkMissingStartOrEnd(registerAvail?.days[i].start, registerAvail?.days[i].end)
            }else break;
        }

        if(hasError) setError(setErrorCatcher("Each day with one time must have a second time!"))

        if(!hasError){
            let dayCopy = formatDates(registerAvail.days)
            registerDef.register_definition.availability={...registerAvail};
            registerDef.register_definition.availability.days = dayCopy;
            if(registerDef.register_definition.hasOwnProperty("change_id")) registerDef.register_definition.change_id = ++registerDef.register_definition.change_id
            else registerDef.register_definition.change_id = 1;
            
            //this is a new error from the backend and it adds these fields when it creates a register.  If they're null, remove them
            if(registerDef.hasOwnProperty("register_group_id") && !registerDef.register_group_id) delete registerDef.register_group_id;
            if(registerDef.hasOwnProperty("register_group_name") && !registerDef.register_name) delete registerDef.register_group_name;
            
            try{
                let response = await Registers.edit(registerDef);
                if(response.status === 200 ) {
                    setSuccess(setSuccessToast("Register Updated Successfully"))
                    setRegisterAvail(null);
                    setRegisterDef(null);
                    getRegister();
                }
                else if(response.error) setError(setErrorCatcher(response.error))
                else (setError(setErrorCatcher("There was a problem editing the register")))
            }catch(ex){
                console.error(ex);
            }
        }
    }
    
    const formatDates=(original)=>{
        let dates = JSON.parse(JSON.stringify([...original])) //if we don't make a copy, we'll end up with invalid time values after the call.  
        for(let i = 0; i < dates.length; i++ ){
            if(dates[i]?.end) dates[i].end = format(new Date(dates[i].end), "HH:mm") 
            if(dates[i]?.start) dates[i].start = format(new Date(dates[i].start), "HH:mm") 
        }
        return dates;
    }
//#endregion Handlers

    return(
        <Container fluid key={hasChanged}>
            <SubHeader 
                items={[
                    {linkAs: Link, linkProps: {to: "/p/home"}, text: "Home"},
                    {linkAs: Link, linkProps: {to: "/p/registers"}, text: "Register Dashboard"},
                    {text: "Register Hours"}
                ]}
            />
            <Card className="content-card register-times">
                {success}
                {error}
                <h4 className="section-title">
                    Register Hours
                </h4>
                {registerAvail ?
                    <form onSubmit = {handleSave}>
                        <p>
                            Put in the start times and end times you want for your register! If you leave it blank, it will indicate there is no start and end time (an always available register, like an online shop or a 24/7 diner).  
                        </p>
                        <hr />
                        {registerAvail.timezone &&
                            <div className="time-col">
                                <label htmlFor="timezone-input" className="tz-input">
                                    Timezone
                                </label>
                                <input 
                                    name="timezone-input"
                                    value={registerAvail.timezone}
                                    onChange={handleTimeZone}
                                />
                                <hr />
                            </div>
                        }
                        
                        <strong>
                            Other Closed Days
                        </strong>
                        <DatePicker 
                            onChange={handleNewDate}
                        />
                        <div className="all-closed">
                            {registerAvail?.extra_closed?.map((extra)=>(
                                <div key={`extra-closed-${extra}`} className="each-closed">
                                    <span>
                                        {extra}
                                        <i className="fas fa-times-circle" 
                                            onClick={()=>handleRemoveDate(extra)}
                                        />
                                    </span>
                                </div>
                            ))}
                        </div>
                        <hr />
                        <div className="all-days">

                            {registerAvail?.days?.map((day)=>(
                                <div key={`each-day-${day.date}`} className="each-date">
                                    <p>
                                        <strong>
                                            {day.date}
                                        </strong>
                                    </p>
                                    <div>
                                        <div className="closed-row">
                                            <input 
                                                type="checkbox" 
                                                onChange={(e)=>handleCheck(e, day.date)}
                                                defaultChecked={day?.closed ? true : false}    
                                            />
                                            <label htmlFor="closed-check">
                                                Closed?
                                            </label>
                                        </div>
                                        <fieldset disabled={day?.closed ? true : false} >
                                            <div className="time-col">
                                                <label htmlFor="start-time">
                                                    Start Time
                                                </label>
                                                <DatePicker 
                                                    name="start-time"
                                                    onChange={(time)=>handleTimeChange(time, day.date, "start")}
                                                    selected={day.start ? new Date(day?.start) : null}
                                                    className="time-picker"
                                                    showTimeSelect
                                                    showTimeSelectOnly
                                                    timeIntervals={15}
                                                    dateFormat="hh:mm aa"
                                                />
                                            </div>
                                            <div className="time-col">
                                                <label htmlFor="end-time">
                                                    End Time
                                                </label>
                                                <DatePicker 
                                                    name="end-time"
                                                    onChange={(time)=>handleTimeChange(time, day.date, "end")}
                                                    selected={day.end ? new Date(day?.end) : null}
                                                    className="time-picker"
                                                    showTimeSelect
                                                    showTimeSelectOnly
                                                    timeIntervals={15}
                                                    dateFormat="hh:mm aa"
                                                />
                                            </div>
                                        </fieldset>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <Button type="submit">Save Hours</Button>
                    </form>
                :
                    <>
                        <SkeletonTheme color="#e0e0e0" >
                            <Skeleton height={30} style={{marginBottom:"1rem"}} />
                            <Skeleton height={12} count={5} />
                        </SkeletonTheme>
                    </>
                }      
            </Card>
        </Container>
    )
}