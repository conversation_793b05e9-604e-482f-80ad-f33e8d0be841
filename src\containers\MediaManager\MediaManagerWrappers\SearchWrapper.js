import React, { useState, useEffect } from 'react';
import { Row, Col, Container, Form } from 'react-bootstrap';

import Filter from '../MediaManagerComponents/Filter'
import Tags from '../MediaManagerComponents/Tags'

const SearchWrapper = ({allTags, hideFilters, setHideFilters, onFilterChange, onAddTag}) => {
    const [selectCompany, setSelectCompany] = useState([]);
    const [selectedMediaTypes, setSelectedMediaTypes] = useState([1]);
    const [search, setSearch] = useState('');
    const [selectedTags, setSelectedTags] = useState([]);
    const [includeAllOrAny, setIncludeAllOrAny]=useState("all_tags");

    useEffect(()=>{
        let _filter = {};
        if (selectedMediaTypes) {
            if (selectedMediaTypes.length === 1) _filter.media_type = selectedMediaTypes?.[0] || null;
            else if (selectedMediaTypes.length > 1) _filter.media_type_ids = selectedMediaTypes;
        } else _filter.media_type = 1; // defaults to images
        if (selectCompany?.[0]) _filter.company_id = selectCompany[0];
        if (search) _filter.search = search;
        if (selectedTags.length > 0){
            if (includeAllOrAny === "all_tags") _filter.all_tags = selectedTags;
            else if (includeAllOrAny === "any_tags") _filter.any_tags = selectedTags;
        }

        if (Object.keys(_filter).length === 0) _filter = null;
        
        onFilterChange(_filter);

    }, [selectedTags, search, selectedMediaTypes, selectCompany, includeAllOrAny, onFilterChange]);

    return (
        <>
            {!hideFilters &&
                <Container fluid className="search-wrapper">
                    <h6>Filter By</h6>
                    <Row>
                        <Col sm={12}>
                            <Filter 
                                setSelectCompany={setSelectCompany}
                                setSelectMediaTypes={setSelectedMediaTypes}
                                selectedMediaTypes={selectedMediaTypes}
                                search={search}
                                setSearch={setSearch}
                            />
                        </Col>
                    </Row>
                    <Row className="my-2">
                        <Col sm={12}>
                            <Tags 
                                setSelectedTags={setSelectedTags}
                                allTags={allTags}
                                selectedTags={selectedTags}
                                addTag={onAddTag}
                            />
                        </Col>
                    </Row>
                    <Row>
                        <Col sm={12}>
                            <Form.Group controlId="all_tags">
                                <Form.Check 
                                    type="radio" 
                                    name="includeAllOrAny" 
                                    label="Include all selected tags" 
                                    value="all_tags" 
                                    onChange={()=>setIncludeAllOrAny("all_tags")}
                                    className='p-0'
                                />
                            </Form.Group>
                            <Form.Group controlId="any_tags">
                                <Form.Check 
                                    type="radio" 
                                    name="includeAllOrAny" 
                                    label="Match any of the selected tags" 
                                    value="any_tags" 
                                    onChange={()=>setIncludeAllOrAny("any_tags")}
                                    className='p-0'
                                />
                            </Form.Group>
                        </Col>
                    </Row>
                </Container>
            }
        </>
    )
}

export default SearchWrapper