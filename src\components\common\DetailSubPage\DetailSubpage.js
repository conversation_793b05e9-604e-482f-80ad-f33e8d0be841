import React,{ useState, useEffect, useCallback, Suspense, lazy } from 'react';
import { ListGroup, ListGroupItem } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { useHistory, useLocation } from 'react-router-dom';
import ReactTooltip from 'react-tooltip';

import { authUserHasModuleAccessMany } from '../../../utils/auth';
import './DetailSubpage.scss';


/**This will create the standard kind of subpage, like the event page, user profile, etc.  
 * This elimates the problem introduced way back when of funky names being on top 
 * and hashes not working on all but the user profile.  That was used to pattern this off of.  
 * It is compatible with components, permissions, and links. 
 * 
 * allTabs object items should be formatted as {id: "", displayName: "", hash: "", moduleId: "", component: <>, link: null, icon: ""}
 * */
export const DetailSubpage=({allTabs=[], itemId=0, loading, currentTab, loadPartHandler, ...props})=>{

    const history = useHistory();
    const location = useLocation();
    const [ modulePermission, setModulePermission ]=useState(null);
    const [ localTab, setLocalTab ]=useState();
    const [ noPermission, setNoPermission]=useState(false);

    const checkTabPermission = useCallback((permissions, tabHash)=>{
        if(!location.hash || location.hash !== tabHash || (location.hash === tabHash && !localTab)){
            if(tabHash[0]==="#") tabHash = tabHash.replace("#", "");
            let properTab = allTabs.filter((tab)=>tabHash === tab.hash);
            let moduleId = properTab[0]?.moduleId;
            if(permissions){
                if(((moduleId && permissions[moduleId]) || !moduleId)) setLocalTab(properTab[0]);
                else setNoPermission(true)
            }
        }
    },[location.hash, allTabs, localTab])
    
    const checkPermission = useCallback(async ()=>{
        setNoPermission(false)
        try{
            let moduleIds= allTabs?.map(tab => tab?.moduleId).filter(id => id !== null);
            let response = await authUserHasModuleAccessMany(moduleIds)
            if(response && currentTab){
                checkTabPermission(response, currentTab)
                setModulePermission(response);
            } 
        }catch(ex){
            console.error(ex)
        }
    },[allTabs, checkTabPermission, currentTab])

    useEffect(()=>{
        if(allTabs?.length > 0 && !modulePermission) {
            checkPermission()
        }
    },[allTabs, checkPermission, modulePermission])
    
    useEffect(()=>{
        const unlisten = history.listen((location)=>{
            if(allTabs){
                let hash = location.hash?.substring(1);
                checkTabPermission(modulePermission, hash)
            }
        })

        return unlisten
    //we don't want triggering over all tabs or permissions, just using them if they're there
    //this is just listening for the location to change (the change of hashes)    
    //eslint-disable-next-line 
    },[history, location.hash])

    let changeTabHandler=(hash, alternateClick)=>{
        history.push(location.pathname + "#" + hash) //by doing this, we trigger our listener that's also listening for any manually input link
        if(alternateClick) alternateClick();
    }

    return(
        <div className="detail-sub-page-wrapper">
            {loading ? 
                    <SkeletonTheme color="#e0e0e0">
                        <Skeleton height={30} style={{marginBottom:"1rem"}} />
                        <Skeleton height={12} count={5} />
                    </SkeletonTheme>
            :
                <div className="sub-page"> 
                    <div className="sub-detail-component">
                        {localTab &&
                            <div>
                                <h4 className="section-title">
                                    {localTab?.displayName}
                                </h4>
                                <hr />
                                {noPermission ? 
                                    <div>
                                        <p>
                                            Sorry! You do not have permission to access this tab. If you think this is an error, contact your administrator! 
                                        </p>
                                    </div>
                                :
                                    <>
                                        {localTab?.component}
                                    </>
                                }
                            </div>
                        }
                    </div>
                    <ListGroup className="subpage-subnav" variant="flush">
                        {allTabs?.map((tab, i)=>{
                            if(tab.moduleId === null || (modulePermission && modulePermission[tab.moduleId])){
                                let href= '#'+tab.hash;
                                let clickAction =()=>{ changeTabHandler(tab.hash, tab.alternateClick)}
                                if(tab.link){
                                    href = tab?.link?.replace(':id', itemId);
                                    clickAction =()=>{history.push(href, {from: `item detail id:${itemId}`})}
                                }
                                return(
                                    <ListGroup.Item 
                                        action
                                        key={`each-tab-item-${tab.id}`}
                                        href={href}
                                        onClick={clickAction}
                                        className={localTab?.hash === tab.hash ? "active-hash-item subnav-item" : "subnav-item"}
                                        data-cy={tab.hash}
                                    >
                                        <span className="li-icon" data-tip={tab.displayName} data-for={`${tab.displayName}-${tab.id}`}>
                                            {tab.icon ? 
                                                <i className={tab.icon}  />
                                            :
                                                <span><strong>{tab?.displayName?.charAt(0)}</strong></span>
                                            }
                                            <ReactTooltip 
                                                className="stubborn-tooltip"
                                                style={{left: "inherit", top:"-30px"}}
                                                type="dark" 
                                                id={`${tab.displayName}-${tab.id}`}
                                            />
                                        </span>
                                        {" "}
                                        <span className="li-text"> 
                                            {tab.displayName}
                                        </span>
                                    </ListGroup.Item>
                                )
                            }
                            else return <React.Fragment key={`null-react-fragment-${i}`}></React.Fragment>
                        })}
                    </ListGroup>
                </div>
            }
        </div>
    )
}