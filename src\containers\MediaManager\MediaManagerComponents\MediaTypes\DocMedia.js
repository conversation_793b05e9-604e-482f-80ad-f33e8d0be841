import React, {useState, useCallback} from 'react'
import { setErrorCatcher } from "../../../../utils/validation";
import { Document, Page } from 'react-pdf/dist/esm/entry.webpack5'

//src\containers\MediaManager\MediaManagerComponents\MediaPreview.js

const DocMedia = ({activeMedia, isNew=false, upload, ...props}) => {
    
    const [numPages, setNumPages ]=useState(null);
    const [ pageNumber, setPageNumber ] = useState(1);
    const windowWidth = window.innerWidth;

    const onDocumentLoadSuccess = ({ numPages }) => {
        setNumPages(numPages)
    }

    const sendHandler = useCallback(data=>{
        upload(data);
    },[upload]);

    const handleDoc = (files) =>{
        const file = files[0];
        let newDoc = new FormData();
        newDoc.append('file', file, activeMedia?.description)
        if(file.size > 2000000){
            setErrorCatcher("Document is too large!", true)
        }else{
            sendHandler(newDoc);
        }
    }

    return (
        <div className="doc-media">
            {!isNew && activeMedia && 
                <>
                    {activeMedia?.url?.includes(".pdf") ? 
                        <Document 
                            file={activeMedia?.url}
                            onLoadSuccess={onDocumentLoadSuccess} 
                            noData={activeMedia?.icon}
                        >
                            <Page 
                                height={windowWidth <= 1500 ? 500 
                                    : windowWidth <= 1950 ? 400 : 600}
                                renderAnnotationLayer={false}
                                pageNumber={pageNumber} 
                            />
                        </Document>
                    :
                        // Not sure which/if these will work, depends on the type of 
                        // <iframe 
                        //     title={activeMedia.name}
                        //     src={activeMedia?.url}
                        // /> 
                        <embed 
                            src={activeMedia?.url}
                            type="text/html"
                        />
                    }
                </>
            }
            {isNew &&
                <p>
                    <label 
                        className="btn btn-secondary"
                        htmlFor="doc-upload"
                    >
                        Upload Document
                    </label>
                    <input 
                        className="hidden"
                        id="doc-upload"
                        name="doc"
                        accept=".doc, .docx, .application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document, .pdf, .xml, .txt"
                        type="file"
                        onChange={(e)=>handleDoc(e.target.files)}
                    />
                </p>
            }
        </div>
    )
}

export default DocMedia