@import '../../assets/css/scss/variables';
@import '../../assets/css/scss/mixins';
@import '../../assets/css/scss/themes';

$media-list-border: solid 1px $divider-color;

.media-header-row{
    @include flex-row-space-between;
}

.media-manager-wrapper{
    @include basic-flex-row;
    flex-wrap: wrap;
}

.media-manager-wrapper, .new-media-wrapper{
    position: relative;
    @media (min-width: 1500px){
        flex-wrap: nowrap;
    }
    .search-wrapper{
        /*@include basic-flex-column;
        max-width: 275px;*/

        h5{
            align-items: flex-start;
        }
        select, input:not([type=radio]){
            margin: $form-control-margin;
        }
        .all-or-any{
            //width: 250px;
            display:flex;
            flex-direction: column;
            flex-wrap: nowrap;
            p{
                @include basic-flex-row;
            }
        }
    }
    .tags{
        .badge-chip{
            text-transform: inherit;
            transition: all .2s ease-in-out;
            text-decoration: none;
            color: inherit;

            i{
                font-size: calc($chip-font-size - 0.1rem);
                font-weight: $chip-font-weight;
                margin-left:5px;                  
            }

            &.minus{
                background-color: $tertiary-light-color;
                color: $primary-font-color;
            }
            &.plus{
                background-color: $primary-light-color;
                color: $primary-font-color;
            }
        }
    }
    .results-wrapper{
        /*min-width: 500px;
        margin-left: 1rem;
        margin-right: 1rem;
        @media (max-width: 500px){
            min-width: 300px;
        }
        @media (min-width: 1900px) and (max-width: 2300px){
            max-width: 900px;
        }*/

        .active-selected{
            border-radius: $card-border-radius;
            background-color: $primary-hover-color;
            color: $primary-inverse-color;
            border: $button-border;
            border-color: $button-border-color;
        }
        .results-header{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin-bottom: 1rem;
            i{
                font-size: 1rem;
                margin-right: 5px;
                padding: 0.5rem;
            }        
        }

        .small-thumb{
            width: 65px;
            img, i{
                height: 45px;
                width: 45px;
                margin: 4px 12px 4px 8px;
            }
            img{
                object-fit: cover;
                border-radius: $card-border-radius;
            }
            i{
                font-size: 2.5rem;
            }
        }

        .grid-media{
            @include basic-flex-row;
            flex-wrap: wrap;
            .medium-thumb{
                height: 150px;
                width: 150px;
                margin: 3px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .medium-thumb:has(i){
                border: 1px solid $primary-color;
            }
            img{
                height: 150px;
                width: 150px;
                padding: 2px;
                object-position: 50% 50%;
                object-fit: cover;
            }
            i{
                font-size: 6rem;
            }
        }
    }
    .selected-media-wrapper{
        /*min-width: 300px;
        max-width: 300px;
        margin-left: 1rem;
        //when the selection is up top instead of to the side of results
        @media (max-width: 1500px){
            max-width: unset;
        }
        //allow larger screens to have a larger result area
        @media (min-width: 2100px){
            max-width: 600px;
        }*/

        .upload-btns{
            p{
                font-size: $small-font-size;
            }
        }
        .media-details{
            input.info{
                border:0 !important;
                padding:0 !important;
            }
            .element{
                display: inline-block;
                min-width: 205px; 
            }
            button {
                color:inherit;
            }
        }
    }
    .img-media{
        @include basic-flex-column;
        button{
            @include basic-button;
            background-color: $secondary-color;
        }
        .border-outline{
            border: 2px solid $tertiary-color;
            width: fit-content;
        }
    }
    .audio-media{
        @include basic-flex-column;
        button{
            @include basic-button;
            background-color: $secondary-color;
        }
    }
    .doc-media{
        @include basic-flex-column;
        button{
            @include basic-button;
            background-color: $secondary-color;
        }
    }
}