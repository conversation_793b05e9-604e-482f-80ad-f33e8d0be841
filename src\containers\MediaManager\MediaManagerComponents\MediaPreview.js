import React, { useState, useEffect, useCallback } from 'react';

import ImageMedia from './MediaTypes/ImageMedia';
import AudioMedia from './MediaTypes/AudioMedia';
import DocMedia from './MediaTypes/DocMedia';

import APIUsers from '../../../api/Users';

const MediaPreview = ({ activeMedia, setActiveMedia, setRefreshMedia, multiSelect }) => {
    const detectDeviceType = () => /Android|iPhone|iPad|iPod|IEMobile/i.test(navigator.userAgent) ? true : false;

    const isMobile = detectDeviceType();

    const uploadHandler = useCallback(async data => {
        if (data){
            data.append('id', activeMedia?.id);
            try{
                const response = await APIUsers.updateMedia(data);
                if (response.errors && response.status !== 200){
                    console.log("error uploading");
                } else {
                    if (response?.data?.[0]) {
                        setActiveMedia(prev=>{
                            if (multiSelect){
                                let temp = [];
                                if (prev) {
                                    if (!Array.isArray(prev)) temp.push(prev);
                                    else temp = [...prev];
                                }
                                temp = temp.map(m=> m.id === response.data[0].id ? response.data[0] : m);
                                return temp;
                            } else return response.data[0];
                        });
                        setRefreshMedia(true);
                    }
                }
            } catch(ex){console.error(ex)}
        }
    }, [activeMedia, setActiveMedia, setRefreshMedia, multiSelect]);    

    return (
        <div className="image-preview">
            {activeMedia?.media_type === 1 &&
                <ImageMedia isMobile={isMobile} activeMedia={activeMedia} upload={uploadHandler}/>
            }
            {activeMedia?.media_type === 7 &&
                <AudioMedia activeMedia={activeMedia} upload={uploadHandler}/>
            }
            {activeMedia?.media_type === 5 &&
                <DocMedia activeMedia={activeMedia} upload={uploadHandler}/>
            }
            {activeMedia?.media_type === 4 &&
                // <video controls >
                //     <source src={activeMedia?.url} />
                // </video>
                <iframe
                    src={activeMedia?.url}
                    title="media-preview"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowFullScreen
                />
            }
        </div>
    )
}

export default MediaPreview;