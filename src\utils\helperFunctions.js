import { format, formatISO, startOfToday, set as setTime } from 'date-fns'

//#region ExternalData

const createDownloadCSV=(data)=>{
    const cvsString = data?.map((row)=>row?.join(',')?.join('\n'));

    const blob = new Blob([cvsString], {type: 'text/csv;charset=utf-8;'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'data.csv';

    document.body.appendChild(link);
    link.click();

    document.body.removeChild(link)
}

export const imageToBlob = (src, fileName) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
    
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
    
        canvas.toBlob((blob) => {
            if (blob) {
            const file = new File([blob], fileName, { type: blob.type, size: blob.size });
            resolve(file);
            } else {
            reject('Failed to convert image to Blob.');
            }
        });
        };
        img.onerror = (err) => reject(err);
        
        img.src = src;
    });
}

//#endregion ExternalData

//#region User Utilities

/** This is to get phone numbers on for receipts, primarily for online orders.  Used on multiple receipts. 
 * 
 * Order should have a memo or a user that has .mobile_phone or .home_phone
*/
export const figureOutPhone=(order)=>{
    let phone;
    if(order?.memo && order?.memo?.includes("phone_number:")) {
        phone = order.memo.split(":")[1]
    } //user put this one in at checkout, use it - they may be using a temp number, who knows 
    else if(order?.user?.mobile_phone) phone = order?.user?.mobile_phone;
    else if(order?.user?.home_phone) phone = order?.user?.home_phone;
    else phone = ""
    return phone;
}

//#endRegion User Utilities

//#region Services

export const findMinMax = (dateTimes) => {
    let minHour = null;
    let maxHour = null;
    dateTimes.forEach((block, block_index) => {
        let start_hour = parseInt(block.timeslots[0].start_time.split(":")[0]);
        let end_time = block.timeslots[block.timeslots.length-1].end_time.split(":")
        let end_hour = parseInt(end_time[0]) + (parseInt(end_time[1])===0 ? 0 : 1);
        if (minHour===null || start_hour < minHour) minHour = start_hour;
        if (maxHour===null || end_hour > maxHour) maxHour = end_hour;
    });
    return {
        min: minHour,
        max: maxHour
    }
}

export const findDateRanges = (blocks, rangeStart, rangeEnd, minMax)=>{
    let day = new Date(rangeStart);
    let list = [];
    while (day <= new Date(rangeEnd)) {
        // does this date exist in the service blocks? use for loop so we can break out of it
        for (let i=0; i<blocks.length; i++) {
            // don't include events that happened in the past
            if (blocks[i].day_of_week===day.getDay() && day>=startOfToday()) {
                // if there is a block for that day of the week add to the search params
                // day should already be 00:00:00 but just in case let's set these to 0 so we never have problems with the search range
                let start = setTime(day, { hours: minMax.min, minutes: 0, seconds: 0 });
                let end = setTime(day, { hours: minMax.max, minutes: 0, seconds: 0 });
                list.push({
                    start_datetime: formatISO(start),
                    end_datetime: formatISO(end),
                });
                // we don't need to check the other blocks for that same day
                break;
            }
        }
        day.setDate(day.getDate()+1);
    }
    return list;
}

export const adjustEventConflictData = (location, events) => {
    if (!events || events.length===0) return null;
    let found = events.find(event => event.location_id===location);
    return (found!=='undefined') ? found.datetime_ranges : null;
}

//#endregion Services
