import React, {useState, useEffect, useRef } from 'react'
import { Con<PERSON>er, <PERSON>, Button } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { Link } from 'react-router-dom';
import SubHeader from '../../../../components/common/SubHeader';
import FeatureRow from '../../CompanyPermissions/DefaultCompanyPermissions/FeatureRow';
import { getFeatures, handleFeatureChecks } from '../../PermissionsUtils/PermissionUtils';
import { setErrorCatcher, setSuccessToast } from '../../../../utils/validation';
import Permissions from '../../../../api/Permissions';

import '../CompanyFeatures.scss'

export const DefaultFeatures = () => {

    const mountedRef = useRef(false);
    const [ loading, setLoading ] = useState(true);
    const [ success, setSuccess ] = useState();
    const [ error, setError ] = useState();

    const [ features, setFeatures ] = useState([]);
    //this will track changes to deal with a smaller array and alter for call on submission
    const [ currentChecks, setCurrentChecks ] = useState([]); 
    const adminDash = JSON.parse(localStorage.getItem("adminDash"))

    useEffect(()=>{
        mountedRef.current = true

        const getFeaturesGet=async()=>{
            const features = await getFeatures();
            if(mountedRef.current && features.data){
                setFeatures(features.data);
            }
            else if (mountedRef.current && features.errors){
                setError(setErrorCatcher(features.errors))
            }
            setLoading(false)
        }

        getFeaturesGet();

        return ()=> mountedRef.current = false
    },[]);

    const handleChecked=(id, checked)=>{
        let currentIdsAndChecks = handleFeatureChecks(id, checked, currentChecks);
        setCurrentChecks(currentIdsAndChecks);
    }

    const handleSubmit=async(e)=>{
        e.preventDefault();
        setSuccess();
        setError();
        let error=[]
        let success=[]

        const tempFeatures = filterWhatNeedsUpdating();
        for(let i = 0; i < tempFeatures.hasChanged.length; i++){
            let result = await sendTheEdit(tempFeatures.hasChanged[i])
            if(result.error){
                error.push(result.error)
            }else if(result.success){
                success.push(result.success)
            }
        }
        afterSubmission(success, error, tempFeatures.hasChanged, tempFeatures.names)
    }

    const sendTheEdit=async(feature)=>{
        let success;
        let error;
        try{
            let response = await Permissions.Features.edit(feature)
            if(response.data && response.status===200){
                success = feature.id + " - Success"
            }
            else if(response.errors){
                error = feature.id + " - " + response.errors
            }
        }catch(ex) {
            console.error(ex)
            error = feature.id + " - " + ex
        }
        return {success: success, error: error}
    }

    //filter it out so we're only making the calls necessary
    const filterWhatNeedsUpdating=()=>{
        let hasChanged = [];
        let names=[];
        for(let i = 0; i < currentChecks.length; i++){
            let match = features.find((feature)=>feature.id === currentChecks[i].id)
            if(match.default_is_enabled === 0 && currentChecks[i].checked) {
                hasChanged.push({
                    id: match.id,
                    default_is_enabled: 1
                })
                names.push(match.name)
            }
            if(match.default_is_enabled === 1 && !currentChecks[i].checked){
                hasChanged.push({
                    id: match.id,
                    default_is_enabled: 0
                })
                names.push(match.name)
            }
        }
        return {hasChanged: hasChanged, names: names};
    }
    
    const afterSubmission=async(success, error, changed, names)=>{
        if(success?.length === changed?.length){
            setLoading(true)
            setSuccess(setSuccessToast(`Feature defaults for${names.map((each)=>` ${each?.toLowerCase()}`)} all Changed Successfully`))
            const features = await getFeatures();
            if(mountedRef.current && features.data) setFeatures(features.data);
            else if (mountedRef.current && features.errors) setError(setErrorCatcher(features.errors));
        }
        else setError(setErrorCatcher(error));
        setLoading(false)
    }

    // create a breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/" }, text: "Home" }
    ];

    if (adminDash) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" });
    }

    breadcrumbs.push({ text: "Default Features" });

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} />
            <Card className="content-card company-features">
                <div className="default-features-wrapper">
                    <h4 className="section-title">
                        Assign Default Features
                    </h4>
                    {success}
                    {error}
                    {loading ? 
                        <SkeletonTheme color="#e0e0e0">
                            <div className="mt-3">
                                <Skeleton height={16} count={8} />
                            </div>
                        </SkeletonTheme>
                    :
                        <>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Default Permission</th>
                                        <th>{" "}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {features?.length > 0 && features?.map((feature)=>(
                                        <FeatureRow key={`feature-${feature.id}`} feature={feature} handleChecked={handleChecked}/>
                                    ))}
                                </tbody>
                            </table>
                            <Button onClick={handleSubmit}>Save</Button>
                        </>
                    }
                </div>
            </Card>
        </Container>
    )
}
