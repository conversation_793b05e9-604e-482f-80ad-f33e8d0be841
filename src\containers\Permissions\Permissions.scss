@import '../../assets/css/scss/variables';
@import '../../assets/css/scss/themes';


.permissions-container {

    .card {
        padding: 0.3rem 0;
    }

    .checkbox-table {
        width: 100%;
        max-width: 900px;
        border-spacing: 0 10px;

        tr:not(.select-all-row) td {
            padding-top: 6px;
            padding-bottom: 6px;
        }

        tr.select-all-row td {
            padding-top: 2px;
            padding-bottom: 2px;
        }

        // all the other columns
        tr td,
        thead tr th {
            width: 14%;
            text-align: center;
        }

        // first column
        tr td:nth-child(1),
        thead tr th:nth-child(1) {
            width: 30%;
            text-align: left;
        }

        .select-all-row {
            td:nth-child(1) {
                text-align: right !important;
                font-size: 0.9rem;
            }
        }

        .center-all {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }

        tr.select-all-row:nth-of-type(1) td {
            padding-top: 8px;
        }

        tr.select-all-row.last-row {    // nth-last-of-type just NOT working
            border-bottom: $neutral-border;

            td {
                padding-bottom: 5px;
            }
        }

        tr.body-row-first td {
            padding-top: 10px;
        }
    }

    .checkbox-table.small-width {
        max-width: 500px;
    }

    .row-cell {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .status-dot {
        visibility: hidden;
        height: 14px;
        width: 20px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;

        i {
            font-size: 7px;
            color: $primary-color;
            padding-right: 5px;
        }
    }

    .status-dot.enabled {
        visibility: visible;
    }

    .dropdown-arrow {
        visibility: hidden;
        height: 14px;
        width: 20px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;

        .label {
            font-size: 10px;
            line-height: 10px;
            color: $primary-color;
            text-transform: uppercase;
            padding-top: 4px;
        }

        i {
            font-size: 17px;
            color: $primary-color;
            padding-left: 5px;
            padding-right: 5px;
        }
    }

    .dropdown-arrow.enabled {
        visibility: visible;
        cursor: pointer;
    }

    .permission-level-row {
        margin-top: 8px;
    }

    .permission-popup {
        border: $neutral-border;
    }

}

.permissions-modal .modal-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

@media (max-width: 750px) {
    .permissions-wrapper .permissions-content div {
        display: none;
    }
    .permissions-wrapper .permissions-content:after {
        content: "Please use a screen width of at least 750 px to assign permissions.";
    }
}