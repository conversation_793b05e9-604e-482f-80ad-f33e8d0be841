import React from 'react';
import { Button } from 'react-bootstrap';
import { format } from 'date-fns';

import ProductCard from '../../../../containers/POS/Items/Products';
import { ErrorDiv } from './ErrorDiv';

export const SelectTokens = ({
    checkServiceAndUserTokens,
    userTokens,
    activeService,
    selectedBookings,
    setSelectedProduct,
    selectedUser,
    handleError,
    matchedTokens,
    slots,
    handleBooking,
    errors,
    ...props
})=>{

    //if a service takes more than one token, notify staff if there is a cheaper option
    const checkForCheapestToken=(service)=>{
        if(service?.products?.length > 1){
            let token = service?.products.reduce((acc, product) => {
                const currentPrice = product.variants[0].price; //tokesn should only ever have one variant
                const accPrice = acc.variants[0].price;
                return currentPrice < accPrice ? product : acc;
            })?.id;
            return token;
        }
    }

    const handleSelectedProduct=(token)=>{
        checkServiceAndUserTokens(userTokens, activeService?.products, selectedBookings)
        setSelectedProduct(token);
    }

    return(
        <>
            {userTokens?.length > 0&& selectedUser && 
                <div>
                    {selectedUser?.first_name} currently has the following tokens:
                    <ul>
                        {userTokens?.map((tokens)=>(
                            <li key={`each-token-${tokens.id}`}>
                                {tokens?.number_of_tokens} {tokens.product_name} token{tokens?.number_of_tokens > 1 ? "s " : " "} 
                                that {tokens?.soonest_exp ? `expire starting on ${format(new Date(tokens?.soonest_exp), "MM/dd/yyyy")}` : "do not expire"}
                            </li>
                        ))}
                    </ul>
                    
                </div>
            }
            {matchedTokens?.matched?.length > 0 &&
                <div>
                    <p>
                        <span>The user needs a total of {slots?.length} tokens.</span>
                        <span>
                            The user has {matchedTokens?.matched?.length} type of token{matchedTokens?.matched?.length > 1 ? "s" : ""} 
                            that match{matchedTokens?.matched?.length > 1 ? "" : "es"} the service:
                        </span>
                    </p>

                    {matchedTokens?.matched.map((token, index) => (
                        <p key={index}>
                            The user has {token?.product_name} that can be used for this service. They have {token?.number_of_tokens}/{slots?.length} tokens/required.
                            {token?.number_of_tokens >= slots?.length ?
                            <p>
                                <span> They have enough tokens to cover the booking.</span>
                                <Button onClick={()=>handleBooking(token)}>Use These Tokens to Book</Button>
                            </p>
                            :
                                <span> They do not have enough tokens to cover the booking.</span>
                            }
                        </p>
                    ))}
                </div>
            }
            {matchedTokens?.matched?.length === 0 &&
                <span>
                    The user has no matching tokens that can be used for this service.
                </span>
            }
            {selectedBookings && selectedBookings?.length > 0 &&
                <>
                    {matchedTokens.text}
                </>
            }
            {activeService && activeService?.products?.map((token)=>{
                let price;
                let cheapestId = checkForCheapestToken(activeService);
                if(token.id === cheapestId) price = `$${token.variants[0].price} (This is the cheapest option)`;
                else price = token.variants[0].price;
                return(
                    <div>
                        <ProductCard
                            key={`token-${token.id}`}
                            item={token}
                            type={0}
                            name={token?.name}
                            price={price}
                            click={()=>handleSelectedProduct(token)}
                        />
                    </div>
                )
            })}
            {/* <p>
                <label htmlFor="override-tokens-true">Override Tokens</label>
                <Tooltip 
                    text="If you select this option, the user will just be added to the booking and not need to purchase a token."
                >
                    <i className="far fa-question-circle ms-1"/>
                </Tooltip>
                <input 
                    type="radio" 
                    id="override-tokens-true" 
                    name="override-tokens" 
                    value={1}
                    onChange={(e)=>console.log(e.target.value)}
                    label="Override"
                />
                <input 
                    type="radio" 
                    id="override-tokens-false" 
                    name="override-tokens" 
                    value={0}
                    onChange={(e)=>console.log(e.target.value)}
                />
            </p> */}
            <ErrorDiv errors={errors} />
        </>
    )
}