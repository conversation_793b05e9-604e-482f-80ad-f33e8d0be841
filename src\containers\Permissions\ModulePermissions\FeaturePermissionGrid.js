import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { Button, OverlayTrigger } from 'react-bootstrap';
import { useSelector } from 'react-redux'

import { permissionRoleIncoming, permissionUserGroupIncoming, permissionRoleOutgoing, permissionUserGroupOutgoing, assignModules, permissionsDifferentFromDefault } from '../PermissionsUtils/PermissionUtils';
import PermissionsAPI from '../../../api/Permissions';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Checkbox from '../../../components/common/Checkbox';
import Toast from '../../../components/Toast';
import { PermissionLevelSelector } from './PermissionLevelSelector';
import PopoverTrigger from '../../../components/common/PopoverTrigger';

import '../Permissions.scss';

// this is the format the data is saved in specifically so we can reference by module_id and role_id to check the boxes appropriately and quickly
// but it does require some processing to get it into this format, and also to get it out of this format to send to the API
let testData = {
    139: {
        name: "Company",
        4: {
            is_allowed: true,
            permission_levels: [4,5,6],
            is_disabled: false,
            is_changed: false,
        },
        5: {
            is_allowed: true,
            permission_levels: [],
            is_disabled: false,
            is_changed: true,
        },
        defaults: {
            4: {
                is_allowed: true,
                permission_levels: [4,5,6],
            },
            5: {
                is_allowed: true,
                permission_levels: [4,5,6],
            }
        }
    }
}

/*    This component is used to display the default permissions for all company if companyId is null
 *    or for a specific company if companyId is not null
 */
export const FeaturePermissionGrid = ({
    feature,
    roles,
    setError,
    needsSave,
    setNeedsSave,
    triggerSave,
    setTriggerSave,
    setIsSaving,
    roleCompanyOwner,
    allPermissionLevels,
    hiddenRoles=[],
    companyId=null,
    userId=null,
    groupId=null
}) => {

    const mountedRef = useRef(false);

    const currentUserCompanyId = useSelector(state =>state.company.id)
    const [moduleData, setModuleData] = useState(null);
    const [triggerLoad, setTriggerLoad] = useState(true);
    const [success, setSuccess] = useState();
    const [smallWidth, setSmallWidth] = useState(false);
    const [hideRoleNameHeader, setHideRoleNameHeader] = useState(false);
    const [hasCustomSettings, setHasCustomSettings] = useState(false);

    const handleCheckboxChange = useCallback((identifier, newValue) => {

        const turnCheckboxOnOff = (newModuleData, moduleId, roleId, isChecked) => {
            // if company owner permission is being turned off, then we need to turn off all of the other roles for that module
            if (roleId===roleCompanyOwner && !isChecked) {
                // loop through all roles and set them to unchecked and disabled
                roles.forEach((role) => {
                    newModuleData[moduleId][role.id].is_allowed = isChecked;
                    if (+role.id!==roleCompanyOwner) {
                        newModuleData[moduleId][role.id].is_disabled = true;
                    }
                });
            } else {
                // if the company owner permission is being turned on then enable all the roles for that module
                if (roleId===roleCompanyOwner && isChecked) {
                    roles.forEach((role) => {
                        newModuleData[moduleId][role.id].is_disabled = false;
                    });
                }
            }
            // set the selected checkbox's value
            newModuleData[moduleId][roleId].is_allowed = isChecked;
            // set is_changed
            newModuleData[moduleId][roleId].is_changed = permissionsDifferentFromDefault(newModuleData[moduleId], roleId);
            // if unchecking a box, then uncheck all the permission levels
            if (!isChecked) {
                newModuleData[moduleId][roleId].permission_levels = [];
            }
            return newModuleData;
        }

        const selectAllCheckboxesForRole = (newModuleData, roleId, selectAll) => {
            Object.keys(newModuleData).forEach((module_id)=>{
                // only if the module is turned on for company owner - or we're adjusting company owner permissions
                if (roleId===roleCompanyOwner || newModuleData[module_id][roleCompanyOwner].is_allowed) {
                    newModuleData = turnCheckboxOnOff(newModuleData, module_id, roleId, selectAll);
                }
            });
            setModuleData(newModuleData);
        }
    
        const selectCheckboxForModule = (newModuleData, moduleId, roleId, isChecked) => {
            newModuleData = turnCheckboxOnOff(newModuleData, moduleId, roleId, isChecked);
            setModuleData(newModuleData);
        }

        const selectPermissionLevelsForModule = (newModuleData, moduleId, roleId, newPermissionLevels) => {
            // set the selected checkbox's value
            newModuleData[moduleId][roleId].permission_levels = newPermissionLevels;
            // set is_changed
            newModuleData[moduleId][roleId].is_changed = permissionsDifferentFromDefault(newModuleData[moduleId], roleId);
            // if adding permission_levels then check the main permission box
            newModuleData[moduleId][roleId].is_allowed = newPermissionLevels.length>0;

            setModuleData(newModuleData);
        }

        setNeedsSave(true);
        let [type, type_id, role_id] = identifier.split('-');

        if (!isNaN(role_id)) role_id = parseInt(role_id);
        let newModuleData = JSON.parse(JSON.stringify(moduleData)); // this is NECESSARY to create a deep copy or we get some really weird behavior

        if (type==='selectall') {
            selectAllCheckboxesForRole(newModuleData, role_id, newValue);
        } else if (type==='module') {
            selectCheckboxForModule(newModuleData, parseInt(type_id), role_id, newValue);
        } else if (type==='permission') {
            selectPermissionLevelsForModule(newModuleData, parseInt(type_id), role_id, newValue);
        }
    },[moduleData, roles, setNeedsSave, roleCompanyOwner]);

    const handleReturnToDefaults = useCallback((e) => {
        let newModuleData = JSON.parse(JSON.stringify(moduleData)); // this is NECESSARY to create a deep copy or we get some really weird behavior

        Object.keys(newModuleData).forEach((module_id)=>{
            // only if the module is turned on for company owner
            if (newModuleData[module_id][roleCompanyOwner].is_allowed) {
                Object.keys(newModuleData[module_id]).forEach((role_id)=>{
                    // if is_changed is true, then we need to reset the permission to the default
                    if (newModuleData[module_id][role_id].is_changed) {
                        newModuleData[module_id][role_id].is_allowed = newModuleData[module_id].defaults[role_id].is_allowed;
                        newModuleData[module_id][role_id].permission_levels = newModuleData[module_id].defaults[role_id].permission_levels;
                        newModuleData[module_id][role_id].is_changed = false;
                    }
                });
            }
        });
        setModuleData(newModuleData);
        setNeedsSave(true);
    },[moduleData, roleCompanyOwner, setNeedsSave]);

    const handleSubmit = useCallback(async (e=null) => {
        setIsSaving(1);
        let data = null;
        if (userId) {
            data = permissionUserGroupOutgoing(moduleData, userId);
        } else if (groupId) {
            data = permissionUserGroupOutgoing(moduleData, groupId, 'group');
        } else {
            data = permissionRoleOutgoing(moduleData, roles, companyId);
        }
        //You can currently only look up users and groups for whatever company you're logged in as.  If this changes in the future, the company id will need to be drawn from somewhere else.
        if(userId || groupId) data.permissions[0].company_id = currentUserCompanyId;
        if (data!==null) {
            try{
                let response = await assignModules(data);
                if(response.data && mountedRef.current) {
                    setSuccess(<Toast>Permissions saved successfully!</Toast>);
                    setNeedsSave(false);
                    setIsSaving(2);
                }
                else {
                    setError(<ErrorCatcher error={response.errors}/>);
                    setNeedsSave(false);
                    setIsSaving(2);
                }
            }catch(ex){
                console.error(ex)
            }
        } else {
            setError(<ErrorCatcher error="There was an error processing the data to save."/>);
        }
    },[moduleData, roles, companyId, setError, setNeedsSave, setIsSaving, userId, groupId, currentUserCompanyId]);

    const getNumChecked = useCallback((role_id) => {
        if (!moduleData) return [0,0];
        let numChecked = 0;
        let numTotal = 0;
        let moduleIds = Object.keys(moduleData);
        moduleIds.forEach((module_id)=>{
            if (moduleData[module_id][role_id].is_disabled) {
                return;
            }
            numTotal++;
            if (moduleData[module_id][role_id].is_allowed) {
                numChecked++;
            }
        });
        return [numChecked, numTotal];
    }, [moduleData]);

    // useEffect(() => {
    //     console.log('moduleData', moduleData);
    // }, [moduleData]);

    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current = false
    },[])

    // reset the grid if the feature_id changes
    useEffect(() => {
        if (feature) setTriggerLoad(true);
    }, [feature]);

    useEffect(() => {
        if (roles.length===1) {
            setHideRoleNameHeader(true);
            setSmallWidth(true);
        }
    },[roles]);

    useEffect(() => {
        if (triggerSave>0) {
            setTriggerSave(0);
            handleSubmit();
        }
    }, [triggerSave, handleSubmit, setTriggerSave, moduleData]);

    useEffect(()=>{

        const getModules = async (feature_id) => {
            let params = {
                "feature_ids": [feature_id],
                "include_permissions": true,
                "company_id": companyId,
                "module_types": [1,2,3]
            };
            let keyId, type = null;
            if (userId) {
                params.user_id = userId;
                keyId = userId;
                type = 'user';
            } else if (groupId) {
                params.group_id = groupId;
                keyId = groupId;
                type = 'group';
            }
            try{
                let response = await PermissionsAPI.Modules.get(params);
                if (response.data) {
                    // also process all of the permission data to put it into the grid
                    if (keyId) {
                        setModuleData(permissionUserGroupIncoming(response.data, keyId, type));
                    } else {
                        setModuleData(permissionRoleIncoming(response.data, roles, companyId));
                    }
                }
            }catch(ex){
                console.error(ex)
            }
        }

        if (triggerLoad) {
            getModules(feature.id);
            setTriggerLoad(false);
        }
    }, [feature, triggerLoad, roles, companyId, userId, groupId]);

    useEffect(() => {
        // loop through everything and see if anything is non-default
        let hasChanges = false;
        if (moduleData) Object.keys(moduleData).forEach((module_id)=>{
            // only if the module is turned on for company owner
            if (moduleData[module_id][roleCompanyOwner]?.is_allowed) {
                Object.keys(moduleData[module_id]).forEach((role_id)=>{
                    // if is_changed is true, then we need to set hasCustomSettings to true and stop looping
                    if (moduleData[module_id][role_id].is_changed === true) {
                        hasChanges = true;
                        return;
                    }
                });
            }
            if (hasChanges) return;
        });
        setHasCustomSettings(hasChanges);
    },[moduleData, roleCompanyOwner]);

    return (
        <>
            <table className={`checkbox-table ${smallWidth ? 'small-width' : ''}`}>
                {!hideRoleNameHeader &&
                    <thead>
                        <tr>
                            <th>
                                {" "}
                            </th>
                            {roles.map((role, i)=>{
                                if (hiddenRoles.includes(role.id)) return <React.Fragment key={`permission-headers-${i}`}></React.Fragment>;
                                return (
                                    <th key={`permission-headers-${i}`}>
                                        {role.name}
                                    </th>
                                )
                            })}
                        </tr>
                    </thead>
                }
                <tbody>
                    {/* header row */}
                    <SelectAllRows
                        roles={roles}
                        featureId={feature.id}
                        handleCheckboxChange={handleCheckboxChange}
                        hiddenRoles={hiddenRoles}
                        getNumChecked={getNumChecked}
                    />
                    {/* all the modules */}
                    {typeof moduleData !== 'object' &&
                        <tr key={`loading`}>
                            <td>loading symbol</td>
                        </tr>
                    }
                    {moduleData && Object.keys(moduleData).map((module_id, j)=>{
                        if (companyId && moduleData[module_id][roleCompanyOwner].is_allowed===false) return <React.Fragment key={`row-${module_id}`}></React.Fragment>;
                        return (
                            <React.Fragment key={`row-${module_id}`}>
                                <GridRow
                                    handleCheckboxChange={handleCheckboxChange}
                                    module={moduleData[module_id]}
                                    moduleId={module_id}
                                    featureId={feature.id}
                                    className={j===0 ? `body-row-first` : ''} // because first-of-type isn't working properly
                                    roles={roles}
                                    hiddenRoles={hiddenRoles}
                                    showDots={companyId!==null || userId!==null}
                                    allPermissionLevels={allPermissionLevels}
                                    roleCompanyOwner={roleCompanyOwner}
                                />
                            </React.Fragment>
                        )
                    })}
                </tbody>
            </table>
            <div className={!!companyId ? "button-row" : ""}>
                <Button variant="primary" onClick={handleSubmit} disabled={needsSave===false}>Save Permissions</Button>
                {!!companyId &&
                    <Button variant="light" onClick={handleReturnToDefaults} disabled={!hasCustomSettings}>Return to Defaults</Button>
                }
            </div>
            {success}
        </>
    )
}

const SelectAllRows = ({roles, featureId, handleCheckboxChange, hiddenRoles=[], showSelectAll=true, showDeselectAll=true, getNumChecked}) => {

    return (
        <tr key={`features-select-${featureId}`} className={`select-all-row last-row`}>
            <td>
                Select All
            </td>
            {roles.map((role, i)=>{
                if (hiddenRoles.includes(role.id)) return <React.Fragment key={`permission-roles-${i}`}></React.Fragment>;
                let [numChecked, numTotal] = getNumChecked(role.id);
                // if all the modules are turned off for this role, then it should show a checkbox
                // if some of the modules are checked for this role then it should show a partial
                // if all the modules are checked for this role then it should show an unchecked box
                return (
                    <td key={`permission-roles-${i}`}>
                        <div className="center-all">
                            <Checkbox
                                id={`selectall-${featureId}-${role.id}`}
                                checked={numChecked>0}
                                partial={numChecked!==numTotal}
                                onChange={handleCheckboxChange}
                                partialIcon="fas fa-minus"
                            />
                        </div>
                    </td>
                )
            })}
        </tr>
    )
}

const GridRow = ({
    module,
    roles,
    handleCheckboxChange,
    featureId,
    moduleId,
    className,
    allPermissionLevels,
    hiddenRoles=[],
    showDots=false,
    roleCompanyOwner=null
}) => {

    return (
        <tr key={`feature-${featureId}-module-${moduleId}`} className={className}>

            <td>
                {module.name}
            </td>

            {roles?.map((role) => {
                let checked = module[role.id]?.is_allowed || false;
                let disabled = module[role.id]?.is_disabled || false;
                let modulePermissionLevels = role.id!==roleCompanyOwner && Array.isArray(module?.permission_levels) ? module?.permission_levels : [];
                let currentPermissionLevels = Array.isArray(module[role.id]?.permission_levels) ? module[role.id]?.permission_levels : [];
                let showOptions = (!disabled && modulePermissionLevels.length>0) || false;
                // dot turns on if checked!=default or permissionLevels are different from default
                let isDifferentFromDefault = (showDots && !disabled && permissionsDifferentFromDefault(module, role.id));

                if (hiddenRoles.includes(role.id)) return <React.Fragment key={`module-${moduleId}-${role.id}`}></React.Fragment>;
                return (
                    <React.Fragment key={`module-${moduleId}-${role.id}`}>
                        <GridCell
                            moduleId={moduleId}
                            roleId={role.id}
                            checked={checked}
                            partial={currentPermissionLevels?.length>0}
                            disabled={disabled}
                            currentPermissionLevels={currentPermissionLevels}
                            showOptions={showOptions}
                            handleCheckboxChange={handleCheckboxChange}
                            idValue={`${moduleId}-${role.id}`}
                            showDot={isDifferentFromDefault}
                            allPermissionLevels={allPermissionLevels}
                            availablePermissionLevels={modulePermissionLevels}
                        />
                    </React.Fragment>
                )
            })}
        </tr>
    )
}

const GridCell = ({idValue, checked, disabled, availablePermissionLevels, currentPermissionLevels, showOptions, showDot, partial, handleCheckboxChange, allPermissionLevels}) => {

    return (
        <td>
            <div className="row-cell">
                <div className={`status-dot ${showDot ? 'enabled' : ''}`}><i className="far fa-circle"></i></div>
                <Checkbox
                    id={'module-'+idValue}
                    checked={checked}
                    disabled={disabled}
                    onChange={handleCheckboxChange}
                    partial={partial}
                />
                <PopoverTrigger
                    placement="bottom"
                    closeButton={true}
                    popoverContent={
                        <PermissionLevelSelector
                            id={'permission-'+idValue}
                            allPermissionLevels={allPermissionLevels}
                            availablePermissionLevels={availablePermissionLevels}
                            checkedPermissionLevels={currentPermissionLevels}
                            onSelect={handleCheckboxChange}
                        />
                    }
                >
                    <div className={`dropdown-arrow ${showOptions ? 'enabled' : ''}`}>
                        <i className="far fa-caret-down"></i>
                        <span className="label">Options</span>
                    </div>
                </PopoverTrigger>
            </div>
        </td>
    )
}
