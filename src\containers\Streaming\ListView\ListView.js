import React from 'react'
import {Button} from 'react-bootstrap'

export const ListView = ({streamEvent, currentTime, openModal}) => {

    return (
        <div>
            { streamEvent?.days.map((day, i) =>{

                const localDate = new Date(day.date).toLocaleString(
                    'en-US',{
                        month: "long",
                        day: "2-digit",
                        year: "numeric"
                    }
                )

                return (
                    <div key={`stream-event-${day}-${i}`} className="stream-schedule">
                        <div>
                            <h5>{localDate}</h5> 
                        </div>
                        <ul>
                            {day.schedule?.map((game, j) => {
                                let current=currentTime.getTime()

                                let gameStart =new Date(day.date + "T" + game.start_time).getTime()
                                let convertedStart = new Date(gameStart).toLocaleString(
                                    'en-US',{
                                        hour: '2-digit',
                                        minute: '2-digit',
                                        hour12: true
                                    }
                                )
                                let gameEnd =new Date(day.date + "T" + game.end_time).getTime();
                                let convertedEnd = new Date(gameEnd).toLocaleString(
                                    'en-US',{
                                        hour: '2-digit',
                                        minute: '2-digit',
                                        hour12: true
                                    }
                                );

                                return(
                                    <li key={"sched-item-" + j}>
                                        <span className="d-flex justify-content-center">
                                            {game.descriptor ? game.descriptor : "No Description Provided"}
                                        </span>    
                                        <div>
                                            <span>
                                                {convertedStart.toString()} - {convertedEnd.toString()}
                                            </span>
                                            <span>
                                                {/* disable button if no link, or if livestream (html link) that has not yet started or already finished */}
                                                <Button
                                                    //times are compared in unicode
                                                    disabled={ !game.link || ( game.link.split(".").pop() === "html" && ( gameStart > current || gameEnd < current )) }
                                                    onClick={() => openModal(game.name, game.link)}
                                                >
                                                    {game.name}
                                                </Button>
                                            {game.link2 &&
                                                <Button
                                                    //times are compared in unicode
                                                    disabled={ ( game.link2.split(".").pop() === "html" && ( gameStart > current || gameEnd < current )) }
                                                    onClick={() => openModal(game.name + " Alt", game.link2)}>
                                                        Alternate View
                                                </Button>
                                            }
                                            </span>
                                        </div>
                                    </li>
                                )
                            })}
                        </ul>
                    </div>
                )
            })}
        </div>
    )
}
