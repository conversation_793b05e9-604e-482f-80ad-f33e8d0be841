import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Container, Card, Button } from 'react-bootstrap';
import { format } from 'date-fns-tz'

import { convertTimeString } from '../../../utils/pos'
import SubHeader from '../../../components/common/SubHeader';

const defaultSubheader=[
    {linkAs: Link, linkProps: {to: "/p/home"}, text: "Home"},
    {text: "Sorry! Closed"}
]

export const ClosedHours = ({ 
    subHeaderItems=defaultSubheader,
    hours,
    customText="",
    customTitle="Sorry!",
    registerName="this register",
    fullPage=true,
    closed=true,
    ...props
})=>{

    const [ loading, setLoading ]=useState(true);
    const [ hoursCopy, sethoursCopy ]=useState([]);

    useEffect(()=>{
        if(hours){
            setLoading(true)
            let hoursCopy = JSON.parse(JSON.stringify(hours))
            hoursCopy?.days?.forEach((day)=>{ 
                if(!day.start && !day.end && !day.closed) day.closed = false;
                else if(!day.closed) {
                    day.closed = false;
                    if(day.start && typeof(day.start)==="string")day.start = convertTimeString(day.start, hours.timezone);
                    if(day.end && typeof(day.end)==="string")day.end = convertTimeString(day.end, hours.timezone);
                }
            });
            sethoursCopy(hoursCopy)
            setLoading(false);
        }
    },[hours])


    const hourBlock=(<>
        <h4 className="section-title">
            {customTitle}
        </h4>
        <div>
            <div>
                {customText ? 
                    <p>
                        {customText}
                    </p>
                    :
                    <p>
                        Currently, {registerName} is closed! The hours of availability are as follows: 
                    </p>
                }
            </div>
            <div>
                {!loading && hoursCopy && hoursCopy?.days?.map((day)=>(
                    <ul key={`day-hours-${day.date}`}>
                        <strong>
                            {day?.date} - {" "}
                        </strong>
                        {day?.closed ?
                            <span>
                                CLOSED 
                            </span>
                        :
                            <>
                                {day.start ?
                                    <span>
                                        {format(new Date(day?.start), 'h:mm a' )} - {format(new Date(day?.end), "h:mm a z")}
                                    </span>
                                    :
                                    <span>
                                        NO HOURS SET
                                    </span>

                                }
                            </>
                        }
                    </ul>
                ))}
            </div>
            {hoursCopy && hoursCopy?.extra_closed?.length > 0 && 
                <>
                    <hr />
                    <p>
                        These are the additional days that {registerName} is closed:
                    </p>
                    {hoursCopy?.extra_closed?.map((closed)=>{
                        let closedFormat = format(new Date(closed), 'MMMM do')
                        let compareDate = format(new Date(), 'MMMM do')
                        return(
                            <p key={`extra-closed-days-${closed}`} className={closedFormat === compareDate ? "strong" : ""}>
                                {closedFormat}
                            </p>
                        )
                    })}
                </>
            }
        </div>    
    </>)
    

    return(
        <>
            {fullPage ? 

                <Container fluid style={{marginTop: "2rem"}}>
                    {/* We are using just a button to go back here instead of history (or the subheader) because we NEED a full refresh in order to check the POS again.  Otherwise, we end upt with the details of this pos's availability as the one in the cart header */}
                    <Card className="content-card">
                        <Button onClick={()=>window.location.href="/p/home"}>
                            Home
                        </Button>
                        {hourBlock}
                    </Card>
                </Container>
            :
                <>
                    {hourBlock}
                </>
                
            }

        </>
    )
}