import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Container, Card, Button } from 'react-bootstrap';
import { useHistory, Link } from 'react-router-dom';
import SubHeader from '../../../../components/common/SubHeader';
import Tutorials from '../../../../components/Tutorials'
import { getFeatures } from '../../PermissionsUtils/PermissionUtils';
import { setErrorCatcher, setSuccessToast } from '../../../../utils/validation';
import { SortOrderTree } from '../../../../components/common/SortOrderTree/SortOrderTree';
import { CompanyTypeahead } from '../../../../components/Typeahead/CompanyTypeahead';

import Permissions from '../../../../api/Permissions';

import '../Features.scss';

export const SortFeatures = () => {
  
    const mountedRef = useRef(false);
    const history = useHistory();

    const [ loading, setLoading ]=useState(true);
    const [ error, setError ]=useState();
    const [ success, setSuccess ]=useState();
    const [ reset, setReset ]=useState(false);

    const [ newOrder, setNewOrder ]=useState([]);
    const [ featureList, setFeatureList ]=useState([]);
    // const [ selectedCompany, setSelectedCompany ]=useState(null);
    const adminDash = JSON.parse(localStorage.getItem("adminDash"))

    const getFeatureList=useCallback(async()=>{
        setError();
        setSuccess();
        try{
            // let params;
            // if(selectedCompany) params = {company_id: selectedCompany[0].id}
            let response = await getFeatures();
            if(response.data && mountedRef.current){
                let sorted = response.data.sort((a,b)=>(a.sort_order < b.sort_order ? -1 : a.sort_order > b.sort_order))
                for(let i = 0; i < sorted.length; i++){
                    sorted[i].index = i+1;
                }
                setFeatureList(sorted)
            } 
            else if(response.errors) setError(setErrorCatcher(response.errors))
        }catch(ex){console.error(ex)}
        setLoading(false)
    },[])

    useEffect(()=>{
        mountedRef.current = true;

        return ()=>{
            mountedRef.current = false;
        }
    },[]);

    useEffect(()=>{
        if(mountedRef.current) getFeatureList();
    },[getFeatureList]);

    const handleNewOrder=(newFeatureOrder)=>{
        setNewOrder(newFeatureOrder)
    }

    const saveSortOrder= async()=>{
        setError();
        setSuccess();
        let callData = newOrder.map((item)=>({module_id: item.id, sort_order: item.sort_order}))
        try{
            let response = await Permissions.Features.sort({sort:callData})
            if(response.status===200 && mountedRef.current) setSuccess(setSuccessToast("Sort Order Saved"))
            else if(response.errors) setError(setErrorCatcher(response.errors))
        }catch(ex) {console.error(ex)}
    }

    const handleCancel=()=>{
        setReset(true)
    }

    // create breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" }
    ];

    // if adminDash is true, add the admin dashboard breadcrumb
    if (adminDash) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" });
    }

    // add feature dashboard breadcrumb and sort features breadcrumb
    breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/features/dashboard" }, text: "Feature Dashboard" });
    breadcrumbs.push({ text: "Sort Features" });

    // create tutorials array
    const tutorials = [
        {
            tutorialSection: "Features",
            allSectionTutorial: false,
            subSection: "Sort",
            navigationTutorial: false,
            basicInfo: false
        }
    ];

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} tutorials={tutorials} />
            <Card className="content-card sort-features-wrapper">
                <div className="heading-row">
                    <h4 className="section-title">
                        Sort Features
                    </h4>
                    <Button onClick={()=>{history.push('/p/features/new')}}>New Feature</Button>
                </div>
                {success}
                {error}
                {/* <div className="company-select">
                    <label htmlFor="company-select">
                        Filter:
                    </label>
                    <CompanyTypeahead 
                        name="company-select"
                        psasSelection={(selection)=>setSelectedCompany(selection)}
                        multiple={false}
                    />
                </div> */}
                {featureList && !loading &&
                    <>
                        <SortOrderTree 
                            data={featureList}
                            optionalDetails={{
                                title: "Selected Feature Details",
                                type: "info",
                                requiresActive: true,    
                                data: [
                                    {name: 'name', label: "Name"},
                                    {name: 'sort_order', label: "Original Sort Order"},
                                    {name: 'description', label: "Description"}
                                ]
                            }}
                            passState={handleNewOrder}
                            reset={reset}
                            setReset={setReset}
                        />
                        <p>
                            This order will apply on this Feature's dashboard and on the Permissions pages.
                        </p>
                        <div className="btn-row">
                            <Button onClick={saveSortOrder}>Save New Order</Button>
                            <Button variant="secondary" onClick={handleCancel}>Cancel</Button>
                        </div>
                    </>
                }
            </Card>
        </Container>
    )
}
