import React, { useState, useEffect, useCallback } from 'react';
import { Container, Row, Col, Button, Form, InputGroup } from 'react-bootstrap';
import Tags from './Tags';
import usePrevious from '../../../components/common/CustomHooks';
import { formatFileSize } from '../../../utils/cms';
import APIUsers from '../../../api/Users';

const MediaDescription = ({ activeMedia, setActiveMedia, setRefreshMedia, allTags, onAddTag, multiSelect }) => {
    const [editDescription, setEditDescription] = useState(false);
    const [description, setDescription] = useState(activeMedia?.description || "");
    const [metadata, setMetadata] = useState(activeMedia?.metadata ? (typeof activeMedia.metadata === "string" ? JSON.parse(activeMedia.metadata) : activeMedia.metadata) : null);
    const [editTags, setEditTags] = useState(false);
    const [tags, setTags] = useState([...(activeMedia?.tags || [])]);
    const [errors, setErrors] = useState({});

    const oldActive = usePrevious(activeMedia);

    const updateMedia = useCallback(async (data) => {
        try {
            const res = await APIUsers.updateMedia(data);
            if (res?.data?.[0]) {                
                setActiveMedia(prev=>{
                    if (multiSelect){
                        let temp = [];
                        if (prev) {
                            if (!Array.isArray(prev)) temp.push(prev);
                            else temp = [...prev];
                        }
                        temp = temp.map(m=> m.id === res.data[0].id ? res.data[0] : m);
                        return temp;
                    } else return res.data[0];
                });
                setRefreshMedia(true);
            }
            return res?.data || null;
        } catch (e) {
            console.error(e);
            return null;
        }
    }, [setActiveMedia, setRefreshMedia, multiSelect]);

    useEffect(() => {
        if (activeMedia && activeMedia !== oldActive) {
            setDescription(activeMedia?.description || "");
            setMetadata(activeMedia?.metadata ? (typeof activeMedia.metadata === "string" ? JSON.parse(activeMedia.metadata) : activeMedia.metadata) : null);
            setTags([...activeMedia?.tags] || []);
        } else if (!activeMedia && activeMedia !== oldActive) {
            setDescription("");
            setMetadata(null);
            setTags([]);
        }
    }, [activeMedia, oldActive]);

    const saveDescription = async () => {
        const res = await updateMedia({description: description, id: activeMedia.id});
        if (res) setEditDescription(false);
        else setErrors({description: "Error saving description"});
    };

    const saveTags = async (e) =>{
        if (e.preventDefault){
            e.preventDefault();
            onAddTag();
        } else {
            const res = await updateMedia({tags: e, id: activeMedia.id});
            if (res) setEditTags(false);
            else setErrors({tags: "Error assigning tags"});
        }
    }

    return (
        <Container fluid className="media-details">
            <Row>
                <Col sm={12}>
                    <Form.Group controlId="name">
                        <Form.Label>Description</Form.Label>
                        <InputGroup>
                            <Form.Control 
                                type="text" 
                                name="description" 
                                size="sm"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)} 
                                aria-label="media-description"
                                className={editDescription ? null : "info"}
                                readOnly={!editDescription}
                            />
                            <InputGroup.Append>
                                {!editDescription ? 
                                    <Button variant="link" onClick={()=>setEditDescription(!editDescription)}><i className="far fa-edit" /></Button>
                                :
                                    <Button variant="link" onClick={saveDescription}><i className="far fa-save" /></Button>
                                }
                            </InputGroup.Append>
                        </InputGroup>
                        {errors?.description &&
                            <Form.Text>
                                <small className="error-text">{errors.description}</small>
                            </Form.Text>
                        }
                    </Form.Group>
                    <hr/>
                </Col>
                <Col sm={12}>
                    <Form.Group controlId="tags">
                        <Form.Label>Tags</Form.Label><br/>
                        {tags?.length > 0 && <p className="element">{tags.map(a=>a.name).join(', ')}</p>}
                        {tags?.length === 0 &&
                            <small className="element">No associated tags</small>
                        }
                        <i className="far fa-edit" onClick={() => setEditTags(!editTags)}/>
                        {errors?.tags &&
                            <Form.Text>
                                <small className="error-text">{errors.tags}</small>
                            </Form.Text>
                        }
                    </Form.Group>
                </Col>
                {editTags &&
                    <Col sm={12}>
                        <Tags
                            key="media-tags"
                            selectedTags={tags}
                            setSelectedTags={setTags}
                            allTags={[...allTags]}
                            addTag={saveTags}
                            editTags={true}
                        />
                    </Col>
                }
                {metadata &&
                    <>
                        <hr/>
                        <Col sm={12}>
                            <Form.Group controlId="name">
                                <Form.Label>Dimensions</Form.Label><br/>
                                <small className="element">
                                    {metadata?.height && metadata?.width && (metadata.height + "x" + metadata.width + "px")}
                                </small>
                            </Form.Group>
                            <hr/>
                        </Col>
                        <Col sm={12}>
                            <Form.Group controlId="name">
                                <Form.Label>Size</Form.Label><br/>
                                <small className="element">
                                    {/* {metadata?.size && formatFileSize(metadata.size)} */}
                                </small>
                            </Form.Group>
                        </Col>
                    </>
                }
            </Row>
        </Container>
    );
}

export default MediaDescription;