@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/mixins';


//currently, most the classes as repetative, 
//but created so that if need be now or down the line, further customization is easier.
//The definitions for @include are in mixins imported above
.variant-component-wrapper{
    position: relative;
    padding:20px 5px 20px 20px;
    margin: 1rem .25rem 1.5rem 2.5rem;
    border-radius: 10px;
    &:nth-child(odd){
        border-left: 5px solid $secondary-color;
    }
    &:nth-child(even){
        border-left: 5px solid $tertiary-color;
    }
    .last-col{
        position: absolute;
        right: 20px;
        top: 57px;
        @media (max-width: 750px){
            top: 10px;
        }
    }
}
.each-basic-wrapper{
    @include product-flex-col;
    margin-bottom: 1rem;
    .product-name-row{
        display: flex;
    }
    .product-flex-row{
        @include product-flex-row;
    }
    .product-flex-col{
        @include product-flex-col;
    }
    label{
        @include basic-label;
    }
    select, input{
        @include basic-input-select;
        width: 225px;
        @media (max-width: 420px){
            width: 175px;
        }
    }
    @media (max-width: 1600px){
        .product-flex-row{
            flex-wrap: wrap;
        }
    }
}
.subscription-type-wrapper{
    @include product-flex-row;
    margin: 0 3.5rem 1rem 3.5rem;
    padding: 20px;
}
.subscription-var-wrapper{
    @include product-flex-col;
    .product-flex-col{
        @include product-flex-col;
    }
    .product-flex-row{
        @include product-flex-row;
        flex-wrap: wrap;
    }
    label{
        @include basic-label;
    }
    input, select{
        @include basic-input-select;
        width: 225px;
        @media (max-width: 420px){
            width: 175px;
        }
    }
    .last-col{
        margin-top: 2.1rem;
    }
    @media (max-width: 1300px){
        max-width: 800px;
        flex-wrap: wrap;
    }
}
.single-variant-wrapper{
    @include product-flex-row;
    flex-wrap: wrap;
    .product-flex-col{
        @include product-flex-col;
        label{
            @include basic-label;
        }
        input, select{
            @include basic-input-select;
            width: 225px;
            @media (max-width: 420px){
                width: 175px;
            }
        }
    }
    @media (max-width: 1300px){
        flex-direction: column;
    }
    .react-numeric-input{
        input{
            box-sizing: none;
            @include basic-input-select;
        }
        b{
            display: none;
        }
    }
}
.token-variant-wrapper{
    @include product-flex-row;
    .product-flex-col{
        @include product-flex-col;
    }
    label{
        @include basic-label;
    }
    input, select{
        @include basic-input-select;
        width: 225px;
        @media (max-width: 420px){
            width: 175px;
        }
    }
    @media (min-width: 751px) and (max-width: 1300px){
        flex-wrap: wrap;
    }
    @media (max-width: 750px){
        flex-direction: column;
    }
}
.measure-sku-wrapper{
   @include product-flex-col;
   padding-top: 5px;
    .product-flex-row{
        @include product-flex-row;
        margin-right:2rem;
    }
    .product-flex-col{
        @include product-flex-col;
        min-width: 250px;
    }
    label{
        @include basic-label;
        width: 235px;
    }
    .sku-input{
        @include basic-input-select;
        width: 225px;
        @media (max-width: 420px){
            width: 175px;
        }
    }
    .sku-col{
        @include product-flex-col;
    }
    .checkmark-top-row{
        margin-top: 1rem;
    }
    input[type="checkbox"]{
        margin-right: 2rem;
    }
    @media (max-width: 1600px){
        .product-flex-row{
            flex-wrap: wrap;
        }
        .sku-col{
            @include product-flex-row;
        }
    }
    @media (min-width: 751px) and (max-width: 1300px){
        .sku-input{
            margin-left: 15px;
        }
    }
    @media (max-width: 750px){
        .sku-col{
            flex-direction: column;
        }
    }
}
.itemized-var-wrapper{
    @include product-flex-col;
    padding-bottom: 5px;
    border-bottom: 3px solid $divider-color;
    .product-flex-col{
        @include product-flex-col;
    }
    .product-flex-row{
        @include product-flex-row;
    }
    label{
        @include basic-label;
    }
    .inventory-details-wrapper{
        @include product-flex-row;
    }
    .shippable-wrapper{
        @include product-flex-row;
    }
    input, select{
        @include basic-input-select;
        width: 225px;
        @media (max-width: 420px){
            width: 175px;
        }
    }
    @media (min-width: 751px) and (max-width: 1800px){
        .product-flex-row{
            flex-wrap: wrap;
        }
    }
    @media (max-width: 750px){
        .product-flex-row{
            flex-direction: column;
        }
    }
}
.measurement-details-wrapper{
    @include product-flex-row;
    .product-flex-col{
        @include product-flex-col;
        margin-left: 1rem;
    }
    .product-flex-row{
        display: flex;
        flex-direction: row;
        justify-content: center;
    }
    .measure-value, 
    .measure-select{
        font-family: $form-control-font-family;
        font-size: $form-control-font-size;
        border: $form-control-border;
        font-weight: $form-control-font-weight;
        line-height: $form-control-line-height;
        color: $form-control-color;
        background-color: $form-control-background-color;
        border-color: $form-control-border-color;
        padding: $form-control-padding;
        &::placeholder{
            color: $form-control-placeholder-color;
            font-weight: $form-control-placeholder-font-weight;
            font-size: $form-control-placeholder-font-size;
            line-height: $form-control-placeholder-line-height;
        }
        &:focus-visible{
            outline: none;
        }

    }
    .measure-value{
        padding: 5px;
        border-radius: 0 $form-control-border-radius $form-control-border-radius 0;
        max-width: 150px;
        margin-bottom: 8px;
    }
    .measure-select{
        width: 80px;
        padding: 7px;
        border-radius: $form-control-border-radius 0 0 $form-control-border-radius;
        border: 2px solid $company-neutral-dark;
        margin-bottom: 8px;
        margin-left: 10px;
        -moz-appearance: none;
        -webkit-appearance: none;
    }
    @media (max-width: 1300px){
        .product-flex-col{
            margin: 0;
        }
    }
    @media (max-width: 750px){
        flex-direction: column;
    }
}
.feature-variant-wrapper{
    @include product-flex-row;
    input[type="checkbox"]{
        margin-left: 2rem;
        margin-top: 1rem;
    }
    .feature-typeahead{
        width: 265px;
        input{
            margin-left: 10px;
            max-width: 225px;
        }
    }
}
