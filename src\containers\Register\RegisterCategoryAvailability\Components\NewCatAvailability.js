import React, { useState, useEffect } from 'react';
import { Button } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import { CategoryTypeahead } from '../../../../components/Typeahead';
import { checkMissingStartOrEnd, checkDupeDates, checkIsOneGreaterTwo } from '../../../../utils/pos';
import { format, setHours, setMinutes } from 'date-fns'

import styles from './RegCatAvailComponents.module.scss';

const editDate=(time)=>{
    if(time){
        let date = new Date();
        let split = time.split(":")
        date = setHours(new Date(date), +split[0])
        date = setMinutes(new Date(date), +split[1])
        return date
    }else return null;
}

export const NewCatAvailability =({dateNumbers, registerHours, handleNew=null, editItem=null,   ...props})=>{

    const [ startTime, setStartTime ] = useState(null);
    const [ endTime, setEndTime ] = useState(null);
    const [ selectedCategory, setSelectedCategory ] = useState(null);
    const [ dayOfWeek, setDayOfWeek ] = useState(null);
    const [ specificDate, setSpecificDate ]=useState(null);
    const [ saving, setSaving ]=useState(false)
    const [ localCorrections, setLocalCorrections] =useState({errors: [], category: null, day:null})
    const [ overrideHours, setOverrideHours ]=useState(null)

    useEffect(()=>{
        if(editItem){
            setStartTime(editDate(editItem?.item?.start));
            setEndTime(editDate(editItem?.item?.end));
            let tempDate = editItem?.day?.date
            if(tempDate && tempDate?.includes("day")) setDayOfWeek(tempDate);
            else if(tempDate) setSpecificDate(new Date(tempDate));
        }
    },[editItem])

//#region ErrorHandling

    const checkAllErrors=()=>{
        let hasErrors = false
        let dayErrors=false;
        let errors = {errors: [], category: null, day: null}
        let timeErrors = checkTimeErrors(errors);
        if(!editItem) dayErrors = checkDay(errors);
        let categoryErrors = checkCategory(errors);
        if(timeErrors || categoryErrors || dayErrors) hasErrors = true;
        setLocalCorrections(errors);
        return hasErrors; 
    }

    const checkTimeErrors = (allErrors)=>{
        let errors = [];
        let isMissing = checkMissingStartOrEnd(startTime, endTime); 
        if(isMissing) errors.push("If you have selected one time, you must select the other")
        let isWrong = checkIsOneGreaterTwo(startTime, endTime); 
        if(isWrong) errors.push("Your end time must be later than your start time.");
        if(errors.length > 0) {
            allErrors.errors = errors
            return true;
        }
    };

    const checkCategory=(allErrors)=>{
        let errorMsg = "You need to select a category"
        if(!selectedCategory || (Array.isArray(selectedCategory) && selectedCategory.length < 1)) {
            allErrors.category=errorMsg
            return true
        }
    };

    const checkDay=(allErrors)=>{
        let dayMsg="You need to pick either a specific date or day of the week";
        let doubleMsg = "You have to pick a day of the week OR specific date, not both."
        if(!specificDate && !dayOfWeek) {
            allErrors.day = dayMsg;
            return true;
        }else if (specificDate && dayOfWeek){
            allErrors.day = doubleMsg;
            return true;
        } 
    };

    const crossCheckHours=(start, end)=>{
        //convert everything to minutes for easy comparison since we're using 24hour intervals;
        let hours = registerHours[+dayOfWeek];
        let started = convertToMinutes(start || "0:00");
        let ended = convertToMinutes(end || "23:45");
        let opened = convertToMinutes(!hours?.closed && hours?.start ? hours.start : "00:00");
        let closed = convertToMinutes(!hours?.closed && hours?.end ? hours.end : "23:45");
        if(started < opened || end < opened || started > closed || ended > closed) return false;
        else return true; 
    }

    const convertToMinutes=(time)=>{
        let [hour, min] = time.split(":");
        let total = (+hour*60) + +min
        return total;
    }

//#endregion ErrorHandling

    const handleTimeChange=(time, type)=>{
        let start = type === "start" ? time : startTime;
        let end = type==="end" ? time : endTime;
        if(type === "start")setStartTime(start);
        if(type === "end") setEndTime(end);
    }

    const handleNewDate=(date, type)=>{
        if(type==="specific") setSpecificDate(date || null);
        if(type==="day") {
            if(date==="blank") setDayOfWeek(null);
            else setDayOfWeek(date);
        }
    }

    const handleSave=()=>{
        setSaving(true);
        let localErrors = checkAllErrors();
        if(!localErrors) {
            let start=null;
            let end=null;
            if(startTime)start=format(new Date(startTime), "HH:mm");
            if(endTime) end = format(new Date(endTime), "HH:mm");
            let isWithin;
            if(dayOfWeek) {
                isWithin = crossCheckHours(start, end);
                if(!isWithin)setOverrideHours("needed")
            }
            let wholeObject = {
                start: start,
                end: end,
                cat_id: selectedCategory[0].id,
                cat_name: selectedCategory[0].name 
            }
            setSaving(false);
            if(editItem) {
                wholeObject.tempId = editItem?.item?.tempId;
                handleNew("edit", {item: wholeObject, day: {dayOfWeek: dayOfWeek, specificDate: specificDate}})
            }
            else{
                wholeObject.dayOfWeek = dayOfWeek;
                wholeObject.specificDate = specificDate;
                handleNew("add", wholeObject);
            } 
        }
        setSaving(false);
    }

    const handleOverrideClick=()=>{
        setOverrideHours("confirmed")
        handleSave()
    }

    return(
        <div className={styles["edit-cat-avail-wrapper"]}>
            <p className={styles.subtitle}>
                {editItem ? "Edit" : "Create"} a category availability slot.        
            </p>
            {!editItem &&
                <div className={styles["day-row"]}>
                    <div className="site-col">
                        <label>
                            Day of the Week:
                        </label>
                        <select onChange={(e)=>handleNewDate(e.target.value, "day")} disabled={specificDate ? true : false}>
                            <option value={"blank"}>Select A Day</option>
                            {Object?.keys(dateNumbers)?.map((each, i)=>(
                                <option key={`date-dropdown-${i}`} value={i}>{each}</option>
                            ))}
                        </select>
                    </div>
                    <div>
                        - OR -
                    </div>
                    <div className="site-col">
                        <label>
                            Specific Date
                        </label>
                        <DatePicker 
                            onChange={(date)=>handleNewDate(date, "specific")} 
                            name="specific-date"
                            selected={specificDate}
                            disabled={dayOfWeek ? true : false}
                        />
                    </div>
                </div>
            }
            <div>
                {dayOfWeek && registerHours && 
                    <p>
                        {registerHours[dayOfWeek]?.closed ? 
                            <span>
                                This register is closed on {registerHours[dayOfWeek]?.date}s
                            </span>
                            : 
                            <>
                                {registerHours[dayOfWeek]?.start && registerHours[dayOfWeek]?.end ?
                                    <span>
                                        Register hours on the chosen day are {registerHours[dayOfWeek]?.start} - {registerHours[dayOfWeek]?.end}
                                    </span>
                                    :
                                    <span>
                                        The register has no assigned hours on the chosen day and is available all day.
                                    </span>
                                }
                            </>
                        }
                    </p>
                }
            </div>
            <div>
                <label>Category</label>
                <CategoryTypeahead 
                    multiple={false} 
                    passSelection={(selection)=>{setSelectedCategory(selection)}}
                    initialData={editItem ? [{id: editItem?.item?.cat_id, name: editItem?.item?.cat_name}] : null}  
                    async={true}
                    paginated={true}  
                />
            </div>
            <div className={styles["day-row"]}>
                <div className="site-col">
                    <label>
                        Start Time:
                    </label>
                    <DatePicker 
                        name="start-time"
                        onChange={(time)=>handleTimeChange(time, "start")}
                        selected={startTime}
                        className="time-picker"
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        dateFormat="hh:mm aa"
                    />
                </div>
                <div className="site-col">
                    <label>
                        End Time:
                    </label>
                    <DatePicker 
                        name="end-time"
                        onChange={(time)=>handleTimeChange(time, "end")}
                        selected={endTime}
                        className=""
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        dateFormat="hh:mm aa"
                    />
                </div>
            </div>
            <Button onClick={handleSave} disabled={saving}>
                Save New Availability
            </Button>
            <div className={styles["error-text"]}>
                <p className="error-text">
                    {localCorrections?.errors?.length ?
                        <>
                            {localCorrections.errors?.map((error, i)=>(
                                <span key={`local-error-${i}`}>
                                    {error}
                                    <br />
                                </span>
                            ))}
                        </>
                    :
                        null
                    }
                    {localCorrections?.category &&
                        <span>
                            {localCorrections?.category}
                            <br />
                        </span>
                    }
                    {localCorrections?.day &&
                        <span>
                            {localCorrections?.day}
                            <br />
                        </span>
                    }
                    {overrideHours === "needed" &&
                        <div>
                            <p>
                                You've selected hours outside of the available hours for this day on this register.  There may be reasons to do this intentionally, but it's generally not advised.  If you still wish to continue, confirm below.  
                            </p>
                            <Button onClick={()=>{handleOverrideClick()}}>
                                Confirm
                            </Button>
                            <Button variant="warn" onClick={setOverrideHours(null)}>
                                Cancel
                            </Button>
                        </div>
                    }
                </p>            
            </div>
        </div>
    )
}

export default NewCatAvailability