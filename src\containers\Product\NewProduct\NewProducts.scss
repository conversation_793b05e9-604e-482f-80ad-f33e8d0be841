@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/themes';

.new-product-wrapper{
    .fake-btn-outline{
        @include basic-button;
        background-color: $primary-light-color;
        color: $primary-font-color;
        border: 1px solid $primary-color;

    }
    .associated-tokens{
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 8px;
        .small{
            margin-bottom: 8px;
        }
    }
    margin: 0 2rem 0 2rem;
    label{
        @include basic-label;
    }
    .services-attached{
        margin-left: 2.5rem;
        p{
            margin-left: .5rem;
        }
    }
    .new-product-header{
        display: flex;
        justify-content: center;
        h4{
            margin-top: 1rem;
            color: $header-font-color;
            font-weight: $header-font-weight;
            font-family: $header-font-family;
        }
        label{
            margin-right: .5rem;
        }
        select{
            @include basic-input-select;
            width: 250px;
            @media (max-width: 420px){
                width: 175px;
            }
        }
    }
    .product-add-headers{
        padding: .75rem;
        background-color: $neutral-hover-background-color;
        border-radius: $card-border-radius $card-border-radius 0 0;
        margin-bottom: 1rem;
        h4{
            margin: 0;
            color: $header-font-color;
            font-weight: $header-font-weight;
            font-family: $header-font-family;
        }
    }
    .center-header{
        text-align: center;
    }
    .product-section{
        border: 1px solid $divider-color;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-radius: $card-border-radius
    }
    .required{
        color: $error-color;
        padding-left: 5px;
        padding-right: 5px;
        font-size: $primary-font-size;
    }
    .new-var-btn{
        display: flex;
        justify-content: center;
    }
    .footer-btn-group{
        display: flex;
        justify-content: space-between;
        padding-left: 3rem;
        button{
            margin-left: 1rem;
        }
        @media (max-width: 750px){
            flex-direction: column-reverse;
            button{
                margin-left: 0;
            }
        }
    }
    .local-errors{
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 4px;
        color: $company-secondary;
        font-size: $primary-font-size;
    }
    .warnings{
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 4px;
        color: $company-bright-error;
        font-size: $primary-font-size; 
        input{
            margin-left: 1rem;
        }
        label{
            font-weight: $bold-font-weight;
        }
    }
    @media (min-width: 750px) and (max-width: 1300px){
        max-width: 800px;
    }
    @media (max-width: 749px){
        max-width: 500px;
    }
}
.basic-product-wrapper{
    margin-left: 3.5rem;
    margin-right: 3.5rem;
    display: flex;
    justify-content: center;
    .categories-div{
        border: 1px solid $divider-color;
        padding: 10px;
        border-radius: 10px;
    }
    label{
        font-weight: $bold-font-weight;
        margin-right: 10px;
    }
    input:not([type='checkbox']), select{
        width: 250px;
        padding: $form-control-padding;
        font-family: $form-control-font-family;
        font-size: $form-control-font-size;
        font-weight: $form-control-font-weight;
        line-height: $form-control-line-height;
        color: $form-control-color;
        background-color: $form-control-background-color;
        border: $form-control-border;
        border-radius: $form-control-border-radius;
        &::placeholder{
            color: $form-control-placeholder-color;
            font-weight: $form-control-placeholder-font-weight;
            font-size: $form-control-placeholder-font-size;
            line-height: $form-control-placeholder-line-height;
        }
        &:disabled{
            background-color: $dropdown-item-disabled-background-color;
        }
        &:focus-visible{
            outline: none;
        }
    }
    .name-desc{
        display: flex;
        flex-direction: column;
        .description{
            display: flex;
            flex-direction: column;
            textarea{
                width: 800px;
                height: 300px;
                padding: 3px;
                margin: 10px;
                resize: none;
                font-family: $form-control-font-family;
                font-size: $form-control-font-size;
                font-weight: $form-control-font-weight;
                line-height: $form-control-line-height;
                color: $form-control-color;
                background-color: $form-control-background-color;
                border: $form-control-border;
                border-radius: $form-control-border-radius;
                &::placeholder{
                    color: $form-control-placeholder-color;
                    font-weight: $form-control-placeholder-font-weight;
                    font-size: $form-control-placeholder-font-size;
                    line-height: $form-control-placeholder-line-height;
                }
                &:disabled{
                    background-color: $dropdown-item-disabled-background-color;
                }
                &:focus-visible{
                    outline: none;
                }
            }
        }
    }
    .status-tax-print{
        display: flex;
        flex-direction: column;
        margin-left: 50px;
    }
    .preview-img{
        max-height: 200px;
        max-width: 100%;
        padding-bottom: 10px;
        object-fit: scale-down;
    }
    .product-flex-col{
        @include product-flex-col;
    }
    @media (min-width: 750px) and (max-width: 1600px){
        .name-desc{
            .description{
                textarea{
                    width: 500px
                }
            }
        }
    }
    @media (min-width: 750px) and (max-width: 1300px){
        flex-direction: column;
        margin-left: 1.5rem;
        margin-right: 1.5rem;
    }
    @media (max-width: 749px){
        flex-direction: column;
        margin-left: .75rem;
        margin-right: .75rem;
        .name-desc{
            .description{
                textarea{
                    max-width: 300px;
                }
            }
        }
        .status-tax-print{
            margin-left: 5px;
        }
    }
    @media (max-width: 400px){
        .name-desc{
            .description{
                textarea{
                    max-width: 200px;
                }
            }
        }
    }
}