import React, {useEffect, useState, useCallback, useRef} from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {Row, Col, Form, Button} from 'react-bootstrap';

import AllTags from '../../../MediaManager/MediaManagerComponents/Tags';

import APITags from '../../../../api/Tags';
import { selectCurrentEvent } from '../../../../store/selectors';
import * as actions from '../../../../store/actions';
export const Tags = (props) => {
    const dispatch = useDispatch();
    const currentEvent = useSelector(selectCurrentEvent);

    const ref = useRef(null);

    const [tags, setTags] = useState([]);
    const [selectedTags, setSelectedTags] = useState(currentEvent?.tags || []);
    const [error, setError] = useState(null);

    const addSelectedPlusToTag=useCallback(tempTags=>{
        for(let i = 0; i < tempTags.length; i++){
            tempTags[i].checked = false;
            const st = selectedTags.find(a => +a.id === +tempTags[i].id);
            if (st) tempTags[i].checked = st.checked;
        }
        return tempTags;
    },[selectedTags]);

    const getTags=useCallback(async()=>{
        const response = await APITags.get();
        if (response?.data){
            const modifiedTags = addSelectedPlusToTag(response.data);
            setTags(modifiedTags);
        }
    },[addSelectedPlusToTag]);    

    const addTagHandler= async () =>{
        const tagName = ref.current.value;
        if (tagName){
            const response = await APITags.create({name: tagName});
            if (response?.data){
                getTags();
                ref.current.value = "";
                ref.current.focus();
            }
        } else setError("Please enter a tag name");
    }

    useEffect(() => {
        dispatch(actions.setCurrentEventWizard({ tags: selectedTags }));
    }, [dispatch, selectedTags]);

    useEffect(()=>{
        getTags();
    },[getTags]);

    return (
        <Row>
            <Col className="wizard">
                <span className="title">Event Tags</span>
                <Row className="mt-4">
                    <Col>
                        <Form.Group controlId={`event-tag-add`}>
                            <Form.Label>New Tag</Form.Label>
                            <Form.Control type="text" name="tag" ref={ref} />
                            {error && <Form.Text className="text-danger">{error}</Form.Text>}
                        </Form.Group>
                        <Button variant="primary" onClick={addTagHandler}>Add Tag</Button>
                    </Col>
                    <Col>
                        <AllTags allTags={tags} selectedTags={selectedTags} setSelectedTags={setSelectedTags} addTag={null}>
                            <p className="small">
                                <label>Click on a tag to add it to the event</label>
                            </p>
                        </AllTags>
                    </Col>
                </Row>
            </Col>
        </Row>
    );    
}