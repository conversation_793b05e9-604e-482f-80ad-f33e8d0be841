/// <reference types="cypress" />

import DisplayDetails from "./index";
import {Button} from 'react-bootstrap';
import '../NewProducts.scss'
import './DisplayDetails.scss'

describe('The Display Page for the products will mount and load data properly.  Fixture data to include many variants of different types and with different data to check them all',()=>{
    let product;
    let variants;
    let user;
    let printLocation;

    before("it will load fixture data", ()=>{
        cy.fixture('User/userResponse.json').then((data)=>{
            user = data;
        });
        cy.fixture('Product/product.json').then((data)=>{
            product = data;
        });
        cy.fixture('Product/variants.json').then((data)=>{
            variants = data;
        })
        cy.fixture('Product/printLocation.json').then((data)=>{
            printLocation = data
        })
    }); //before

    beforeEach("it will put a fake user in local storage", ()=>{
        window.localStorage.setItem('user',
            JSON.stringify({
                menu:[],
                roles: user.adminUser.data[0].roles,
                profile: user.adminUser.data[0],
                token: "bearer eyJASDKJ239849skdfjJKFJ.eyasdkj*9"
            })
        );
        cy.viewport(1920, 1080);
    }); //end beforeEach

    
    it("will mount the display component and compare the data to the fixed data coming in",()=>{
        let loadedProduct = product.newYumProduct.data.products[0]

        cy.intercept('POST', '/api/location', printLocation.printLocation58).as('getPrinters');
        cy.intercept('GET', '/api/product/variant/767?**', variants.variant767).as('get767');
        cy.intercept('GET', '/api/product/variant/768?**', variants.variant768).as('get768');
        cy.intercept('GET', '/api/product/variant/752?**', variants.variant752).as('get752');
        cy.intercept('GET', '/api/product/variant/161?**', variants.variant161).as('get161');
        cy.intercept('GET', '/api/product/variant/688?**', variants.variant688).as('get688');
        cy.intercept('GET', '/api/product/variant/770?**', variants.variant770).as('get770');
        cy.intercept('GET', '/api/product/variant/769?**', variants.variant769).as('get769');

        cy.mount(<DisplayDetails product={loadedProduct} importedBtn={<Button >Edit Product</Button>} />);
        cy.wait('@get767');
        cy.wait('@get768');
        cy.wait('@get752');
        cy.wait('@get161');
        cy.wait('@get688');
        cy.wait('@get770');
        cy.wait('@get769');
        cy.wait('@getPrinters')

        cy.log(`${String.fromCodePoint(0x1F92F)} check product Data`);
        cy.get('[data-cy="product-name"]')
            .should('contain', "YUUUUUMMMM");
        cy.get('[data-cy="product-type"]')
            .should('contain', "Food & Drink");
        cy.get('[data-cy="product-status"]')
            .should('contain', "Active");
        cy.get('[data-cy="product-description"]')
            .should('contain', 'this product must always be said in a Furby voice');
        cy.get('[data-cy="product-printers"]')
            .should('contain', 'Kitchen Grill');
        cy.get('[data-cy="product-categories"]')
            .should('contain', 'Restaurant')
            .and('contain', 'Smack Shack');

        cy.log(`${String.fromCodePoint(0x1F92F)} cehck variant 767`);
        cy.get('[data-cy="variant-767"]');
        cy.get('[data-cy="variant-767"] > .prod-display-each-variant')
            .children()
            .should('have.length', 10);
        cy.get('[data-cy="variant-767"]').within(()=>{
            cy.get('[data-cy="variant-name"]')
                .should('contain', "Default - 767")
                .and('contain', "2.50");
            cy.get('[data-cy="variant-date-available"]')
                .should('contain', "11/30/2022");
            cy.get('[data-cy="variant-shippable"]')
                .should('contain', "Yes");
            cy.get('[data-cy="variant-sku"]')
                .should('contain', 'abcdef');
            cy.get('[data-cy="variant-upc"]')
                .should('contain', "123456");
            cy.get('[data-cy="variant-height"]')
                .should('contain', "3.000");
            cy.get('[data-cy="variant-width"]')
                .should('contain', "4.000");
            cy.get('[data-cy="variant-length"]')
                .should('contain', "2.000");
            cy.get('[data-cy="variant-weight"]')
                .should('contain', "1.000");
            cy.get('[data-cy="variant-addons"]')
                .should('contain', "Cats Included")
            cy.get('[data-cy="variant-status"]')
                .should('contain', "Active");
        })

        cy.log(`${String.fromCodePoint(0x1F92F)} check variant 768`);
        cy.get('[data-cy="variant-768"]');
        cy.get('[data-cy="variant-768"] > .prod-display-each-variant')
        .children()
        .should('have.length', 4);
        cy.get('[data-cy="variant-768"]').within(()=>{
            cy.get('[data-cy="variant-name"]')
                .should('contain', "Wedding - 768")
                .and('contain', "4.50");
            cy.get('[data-cy="variant-date-available"]')
                .should('contain', '11/27/2022');
            cy.get('[data-cy="variant-shippable"]')
                .should('contain', "No");
            cy.get('[data-cy="variant-addons"]')
                .should('contain', "Cats Included")
                .and('contain', "Catnip");

        })

        cy.log(`${String.fromCodePoint(0x1F92F)} check variant 752`);
        cy.get('[data-cy="variant-752"] > .prod-display-each-variant')
            .children()
            .should('have.length', 6);
        cy.get('[data-cy="variant-752"]').within(()=>{
            cy.get('[data-cy="variant-name"]')
                .should('contain', "default - 752")
                .and('contain', "1.00");
            cy.get('[data-cy="variant-status"]')
                .should('contain', "Not Available");
            cy.get('[data-cy="variant-activation-fee"]')
                .should('contain', "1.00");
            cy.get('[data-cy="variant-bill-interval"]')
                .should('contain', 'm');
            cy.get('[data-cy="variant-date-available"]')
                .should('contain', '11/30/2022');
        })

        cy.log(`${String.fromCodePoint(0x1F92F)} check variant 161`);
        cy.get('[data-cy="variant-161"] > .prod-display-each-variant')
            .children()
            .should('have.length', 3);
        
        cy.log(`${String.fromCodePoint(0x1F92F)} check variant 688`);
        cy.get('[data-cy="variant-688"] > .prod-display-each-variant')
            .children()
            .should('have.length', 3);
        
        cy.log(`${String.fromCodePoint(0x1F92F)} check variant 770`);
        cy.get('[data-cy="variant-770"] > .prod-display-each-variant')
            .children()
            .should('have.length', 3);

        cy.log(`${String.fromCodePoint(0x1F92F)} check variant 769`);
        cy.get('[data-cy="variant-769"] > .prod-display-each-variant')
            .children()
            .should('have.length', 9);
    })
})