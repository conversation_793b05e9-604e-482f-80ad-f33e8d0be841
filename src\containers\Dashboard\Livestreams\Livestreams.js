import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { But<PERSON>, Modal } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';
import format from 'date-fns/format';

import Users from '../../../api/Users';
import Products from '../../../api/Products';

import { determineEnvironment } from '../../../containers/Streaming/ArchivedStreams/ArchiveStreamsUtils'
import { addToCart } from '../../../utils/thunks';

import '../../POS/Events/ListCards/RegEventListCards.scss';
import Streams from '../../Streaming/streams.json'

export const Livestreams = props => {
    let history = useHistory();
    let dispatch = useDispatch();

    const user = useSelector(state => state.auth.user);
    const [loading, setLoading] = useState(false);
    const [pagePart, setPagePart] = useState();
    const [purchased, setPurchased] = useState();
    const [product, setProduct] = useState([]);
    const [showDisclaimer, setShowDisclaimer]=useState(false);

    const currentEnvironment = determineEnvironment();
    const product_ids = {"prod": 2315, "qa": 1541, "dev": 1304};
    const product_id = product_ids[currentEnvironment];
    
    
    useEffect(() => {
        let mounted = true;

        setLoading(true);
        Users.purchasedProduct({user_id: user?.profile?.id, product_id: [product_id]})
        .then(response => {
            if(mounted && !response.errors) {
                setPurchased(response.data[product_id]);
            }
            setLoading(false);
        }).catch(e => console.error(e));

        // cancel stuff when component unmounts
        // not sure this is even necessary
        return () => {
            mounted = false;
            setLoading(false);
        }        
    },[product_id, user]);

    useEffect(() => {
        let mounted = true;

        if(purchased === 0) {
            Products.get({id: product_id})
            .then(response => {
                if(mounted && response.data?.products) {
                    let item = response.data.products[0];
                    if (item) {
                        setProduct([{
                            category_id: item.categories[0]?.id,
                            discounts: 0,
                            hash: null,
                            id: item.id,
                            product_id: product_id,
                            product_type_id: item.product_type_id,
                            is_taxable: item.is_taxable,
                            original_price: +item.product_variants[0].price,
                            parent_id: null,
                            product_name: item.name,
                            product_price: +item.product_variants[0].price,
                            qty: 1,
                            type: 1,
                            variant_id: item.product_variants[0].id,
                            variant_name: item.product_variants[0].name
                        }]);
                    }
                } else console.error(response.errors);
            }).catch(e => console.error(e));
        }

        return () => mounted = false;
    },[purchased, product_id]);

    const clickHandler = useCallback(() => {

        if (purchased) {
            history.push("/p/livestreams");
        } else {
            dispatch(addToCart(product));
        }
    },[purchased, product, dispatch, history]);

    useEffect(() => {
        setPagePart(
            <>
                <div className="d-flex flex-row justify-content-between" style={{maxWidth: "800px"}}>
                    <span>
                        <sub className="cp" onClick={()=>setShowDisclaimer(true)}>
                            Please Note (click here)
                        </sub>
                        <h5 className="mx-3">{Streams[0]?.name}</h5>
                    </span>
                    <Button onClick={clickHandler}>{ purchased ? "Watch" : "Purchase Livestream Access - $" + parseFloat(product?.[0]?.product_price)?.toFixed(2) }</Button>                          
                </div>
                <p className="mx-3">
                    {Streams[0]?.description}
                </p>
            </>
        );
    }, [purchased, product, clickHandler]);

    return (
        <React.Fragment>
            { pagePart }

            <Modal show={showDisclaimer} onHide={()=>setShowDisclaimer(false)} size="lg">
                <Modal.Header closeButton>
                    <p>
                        Disclaimer
                    </p>
                </Modal.Header>
                <Modal.Body>
                <p>
                    Thank you for utilizing Impact Athletic Center's Live Streaming services for viewing your favorite athlete's athletic competitions.
                </p>
                <p>
                    Our high-quality Live Stream offers you AD free viewing for the duration of your event as well as the ability to view the event afterwards in our Archived Live Streams.
                </p>
                <p>
                    Archived Live Streams from an event will be available to you one week following the event.  You will not have the ability to rewatch any part of the event until the Live Stream has been added to the Archives.  
                </p>
                <p>
                    Should you experience AD's while viewing the Live Stream, it is recommended that you check the settings on your device as Impact Athletic Center does not give permission for AD's to interrupt our Live Stream.  
                </p>
                <p>
                    For the best viewing experience, it is recommended that you watch the Live Stream on a device connected to and supported by a Wi-Fi network.
                </p>
                <p>
                    Impact Athletic Center works with the tournament host to pre-set the schedule and court assignments prior to the start of the event.  Should the tournament change the game schedule, court assignments, or starts games earlier or later, Impact Athletic Center will be unable to adjust the pre-set schedule.  However, the Live Stream is guaranteed to stream the tournament in its entirety and we ask that customers are flexible and patient as you navigate to the court streaming the game you wish to watch. 
                </p>
                <p>
                    **Volleyball Tournaments: Due to the location of Court 5 in the facility, Impact Athletic Center is unable to directly stream Court 5.  Court 5 can be viewed by watching Court 3 or Court 6 on the Live Stream.**
                </p>
                </Modal.Body>
            </Modal>
        </React.Fragment>
    );
}