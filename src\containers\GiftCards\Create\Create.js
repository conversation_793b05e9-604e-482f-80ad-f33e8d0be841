import React,{useEffect, useState, Suspense} from 'react';
import { useLocation, Link  } from "react-router-dom";
import { useSelector } from 'react-redux';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { Container, Row, Col, Card, ListGroup } from 'react-bootstrap';
import SubHeader from '../../../components/common/SubHeader';

import { authUserHasModuleAccess } from "../../../utils/auth";

import BasicInfo from '../BasicInfo';

const VIEW_MODULE_ID = 322;

const Create = (props) => {    

    return (
        <Container fluid>
            <Row>
                <BasicInfo />
            </Row>
        </Container>
    );
}

export default Create;