.table-sort-desc:after,.table-sort-asc:after{
    font-family: 'Font Awesome 5 Pro';
    font-weight:300;
    content: '\f063';
    margin-left:.5rem;
}
.table-sort-asc:after{
    content: '\f062';  
}
.table-sort-none{
    width:16px;
    height:16px;
    margin-left:.5rem;
}

table thead th{
    font-weight:500;
    font-size:.85rem;
    text-transform: uppercase;
    white-space: nowrap;
}
table thead th span{
    user-select: none;
}

.page-item .page-link{
    font-size:.8rem;
    height:100%;
    padding-top:.7rem;
}

.page-item.active .page-link {
    background-color:#536dfe;
}

.page-item{
    position:relative;
}

.pagination-first span:first-child,
.pagination-prev span:first-child,
.pagination-next span:first-child,
.pagination-last span:first-child{
    color:transparent;
}

.pagination-first:after,
.pagination-prev:after,
.pagination-next:after,
.pagination-last:after{
    font-family: 'Font Awesome 5 Pro';
    font-size:.7rem;
    font-weight:500;
    position:absolute;
    top:.75rem;
    left:.7rem;
    color:#536dfe;
    z-index: 999999;
}

.pagination-first:after{
    content:'\f323';
}

.pagination-prev:after{
    content:'\f053';
}

.pagination-next:after{
    content:'\f054';
}

.pagination-last:after{
    content:'\f324';
}

.page-item.disabled:after{
    color:#bdbdbd;
}

.profileMenu{
    font-size:.8rem;
    border-left:1px solid #eee;
    padding:1rem;
    margin-left: 1rem;
    @media (max-width: 1350px){
        border-left: none;
    }
}
.profileMenu i {
    width: 16px;
    text-align: right;
    margin-right: 5px;
}
.event-details-wrapper{
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    @media (max-width: 1350px){
        flex-direction: column;
    }
    .event-details-nav{
        @media (max-width: 1350px){
            display: flex;
            flex-direction: row;
        }
    }
    .list-group-item{
        &.active span{
            @media(max-width: 993px){
                color: transparent !important;
            }
        }
        @media(max-width: 1350px){
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
}

.event-details-page{
    .summary-row{
        label{
            text-decoration: underline;
            font-size: 1rem;
        }
    }
}