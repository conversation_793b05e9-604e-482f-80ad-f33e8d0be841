import React, { useState, useEffect } from 'react'
import { But<PERSON> } from 'react-bootstrap'

import CharacterCounterInput from '../../../../components/common/CharacterCounter/CharacterCounterInput'

import EndPointsTypeahead from '../../../../components/Typeahead/EndPointsTypeahead'

//src\containers\Permissions\Modules\CreateEditModuleForm\CreatEditModuleForm.js

export const ModulePages = ({selectedType, activeModule, modules, ...props}) => {
    
    const [ selectedParent, setSelectedParent ]=useState(0)

    useEffect(()=>{
        if(activeModule?.default_menu_item && activeModule.default_menu_item?.parent_id !== null) setSelectedParent(activeModule.default_menu_item.parent_id);
    },[activeModule])
    
    return (
        <div>
            <div className="input-col">
            {selectedType===1 &&
                <>
                    <label htmlFor="component_url">
                        Component URL
                        {" "}
                        {selectedType===1 &&
                            <span className="required">*</span>    
                        }
                    </label>
                    <span>
                        src/
                        <input 
                            required={selectedType===1 ? true : false}
                            name="component_url"
                            placeholder="containers/Module/New"
                            defaultValue={activeModule?.component_url}
                        />
                    </span>
                </>
            }
            </div>
            <div className="input-col mt-3">
                <h4>Default Menu Item</h4>
                <div className="row-pair bottom">
                    <CharacterCounterInput
                        characterLimit={45}
                        label="Displayed Text"
                        name="text"
                        placeholder="New Module"
                        value={activeModule?.default_menu_item?.text}
                        columnRow = "row"
                    />
                </div>
                <div className="row-pair bottom">
                    <CharacterCounterInput 
                        characterLimit={45}
                        label = "Displayed Icon"
                        name="icon"
                        placeholder="fas fa-plus"
                        value={activeModule?.default_menu_item?.icon}
                        columnRow="row"
                    />
                </div>
                <div className="row-pair bottom">
                    <label htmlFor="parent_id">Parent Menu Item</label>
                    <select className="nudge" name="parent_id" value={selectedParent || 0} onChange={(e)=>setSelectedParent(+e.target.value)}>
                        <option value={0}>No Parent</option>
                        {modules?.filter(module=> module.module_type_id===4)?.map((module)=>(
                            <option key={`module-parent-${module.id}`} value={module.id}>{module.name}</option>
                        ))}
                    </select>
                </div>
            </div>
        </div>  
    )
}
