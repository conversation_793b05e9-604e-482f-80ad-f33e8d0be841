import React, { useCallback, useEffect, useState, useRef } from 'react';

import { Typeahead } from './Typeahead';

import ProductsAPI from '../../api/Products';

import 'react-bootstrap-typeahead/css/Typeahead.css';
import './Typeahead.scss';

/**Basic async typeahead for searching categories.  After a selection is made, prop function will pass the data back up to the parent component.
 * This is a wrapper for the common Typeahead component, this includes only the few pieces specific for this data type.
 * @param {()} multiple to allow multiple selections
 * @param {()} passSelection to pass the array of selected Categories back
 * @param {()} divider to replace the > with a different character
 * @param {()} selectedToStart to set the initial value of the typeahead - array of ids
 * @param {()} type (default of "post") because the post and the get have different rules for addons and different return formats
*/
export const CategoryTypeahead = ({isAddon=false,type="post",...props}) => {

    const [divider, setDivider] = useState(" > ");


    /* FUNCTIONS */

    const makeRequest = useCallback(async (query, perPage, page=1) => {
        let response;
        let responseObj;
        if(type === "post"){
            response = await ProductsAPI.Categories.filter({
                add_on_only: isAddon ? 1 : 0,
                include_parents: true,
                search: query,
                max_records: perPage,
                page_no: page
            });
            responseObj = {
                data: response.data?.categories || null,
                errors: response.errors || null
            }
        }else if (type === "get"){
            response = await ProductsAPI.Categories.get()
            responseObj = {
                data: response.data|| null,
                errors: response.errors || null
            }
        }
        return responseObj;
    },[]);

    const addParent = useCallback((category, currentString='') => {
        if (category?.parent) {
            let parentToAdd = category.parent.name + divider;
            return addParent(category.parent, parentToAdd + currentString);
        }
        return currentString;
    }, [divider]);

    // each item in responseObj.data is an option
    const formatForLabel = useCallback((option) => (
        addParent(option) + `${option?.name}`
    ), [addParent]);


    /* USEEFFECT */

    useEffect(() => {
        if (props?.divider) {
            setDivider(" " + props.divider + " ");
        }
    }, [props.divider]);

    return (
        <Typeahead
            {...props}
            id="category-search"
            formatForLabel={props.formatForLabel ? props.formatForLabel : formatForLabel}
            makeRequest={makeRequest}
            async={props.async}
            paginated={props.paginated}
            placeholder={props.placeholder ? props.placeholder : "Enter a category name..."}
        />
    )
}
