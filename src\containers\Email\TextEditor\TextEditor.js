import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useParams, useHistory } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Container, Card, Breadcrumb } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';

import { setErrorCatcher, setSuccessToast } from '../../../utils/validation';
import { Wysiwyg } from '../../Cms/Builder/Toolbar/Properties/Property/Item/Wysiwyg/Wysiwyg';
import CustomOption from '../../../components/common/Wysiwyg/CustomOptions.js/CustomOptions';
import { authUserHasModuleAccess } from '../../../utils/auth';
import Email from '../../../api/Email';
import styles from './TextEditor.module.scss'

const EDIT_DEFAULT_MODULE_ID = 317;

export const TextEditor = () => {

    const mountedRef = useRef(false);
    const params = useParams();
    const history = useHistory();
    const companyId = useSelector((state)=>state.company.id);
    const [loading, setLoading] = useState(true);
    const [success, setSuccess] = useState();
    const [error, setError] = useState();
    const [variables, setVariables]=useState([]);
    const [filteredVariables, setFilteredVariables]=useState([]);
    const [currentEmail, setCurrentEmail ] =useState("");
    const [templateTypes, setTemplateTypes]=useState([]);
    const [selectedTemplate, setSelectedTemplate]=useState("new");
    const [editId, setEditId]=useState("new");
    const [editTemplate, setEditTemplate]=useState({});
    const [newDefault, setNewDefault]=useState(false);
    const [userModulePermission, setUserModulePermission] = useState(false);
    const [defaultCheck, setDefaultCheck]=useState(false);

    const getVariables = useCallback(async()=>{
        try{
            let response = await Email.variables.get();
            if(response.status === 200 && mountedRef.current){
                setVariables(response.data);
            }
            else{
                setVariables("Error");
                if(response.errors) setError(setErrorCatcher(response.errors))
                else setError(setErrorCatcher("There was a problem getting the email variables"))
            }
        }catch(ex){
            console.error(ex);
            setVariables("Error")
        }
    },[]);

    const getTemplateTypes=useCallback(async()=>{
        try{
            let response = await Email.templateTypes.get();
            if(response.status === 200 && mountedRef.current) {
                setTemplateTypes(response.data);
            }
            else{
                setTemplateTypes("Error");
                if(response.errors) setError(setErrorCatcher(response.errors))
                else setError(setErrorCatcher("There was a problem loading the template types"))
            }
        }catch(ex){
            console.error(ex);
        };
    },[]);

    const getTemplate = useCallback(async(id)=>{
        try{
            let response = await Email.templates.get({id: id});
            if(response.status === 200 && mountedRef.current){
                setEditTemplate(response.data[0]);
                setCurrentEmail(response.data[0].body);
                setSelectedTemplate(response.data[0].email_template_type_id);
                setDefaultCheck(userModulePermission[317] && response.data[0].company_id === null ? true : false);
            }
            else{
                if(response.errors) setError(setErrorCatcher(response.errors))
                else setError(setErrorCatcher("There was a problem retrieving the template"))
            }
        }catch(ex){
            console.error(ex);
        }
    },[userModulePermission]);

    const getEditId = useCallback(async()=>{
        let splitId = window.location.pathname.split("/")[4];
        if(splitId?.toLowerCase() !== "newdefault"){
            setEditId(splitId);
            getTemplate(splitId);
        }else setEditId("new");
    },[getTemplate]);

    useEffect(()=>{
        const checkPermission = async ()=>{
            try{
                let response = await authUserHasModuleAccess(EDIT_DEFAULT_MODULE_ID);
                if(response){
                    setUserModulePermission(response);
                }
            }catch(ex){
                console.error(ex)
            }
        }

        checkPermission();

    },[])

    useEffect(()=>{
        mountedRef.current = true

        getVariables();
        getTemplateTypes();
        if(params.id !=="newdefault") getEditId();
        else if (params.id === "newdefault") {
            setNewDefault(true);
        }

        return()=> mountedRef.current = false
    },[getVariables, getTemplateTypes, getEditId, params]);

    useEffect(()=>{
        if(loading && Array.isArray(variables) && variables.length > 0 && templateTypes.length > 0){
            setLoading(false);
        }
    },[variables, templateTypes, loading]);

    useEffect(()=>{
        if(editTemplate && variables){
            let selectedType = editTemplate.email_template_type_id;
            const tempFiltered = []
            variables.forEach((variable)=>{
                if(!variable.email_template_types) tempFiltered.push(variable);
                else if(variable.email_template_types){
                    let templateArray = variable.email_template_types.split("[").join(",").split("]").join(",").split(",");
                    templateArray.pop();
                    templateArray.shift();
                    if(templateArray?.includes(selectedType?.toString())) tempFiltered.push(variable);
                }
            })
            setFilteredVariables(tempFiltered);
        }else if (!editTemplate && !editId && variables){
            setFilteredVariables(variables);
        }
    },[editTemplate, variables, editId]);

    const blurHandler=(e, value)=>{
        setCurrentEmail(e.target.value);
    }

    const handleSelectedTemplate =(e)=>{
        e.preventDefault();
        setSelectedTemplate(e.target.value);
    }

    const onSubmit = async (e)=>{
        e.preventDefault();
        const formData = new FormData(e.target)
        const formDataObject = Object.fromEntries(formData.entries())
        let edit = true;

        //id - if a default template is being made into company, id needs to be removed
        if(newDefault) {
            edit = false
        }
        //if there is an id (which means it already exists) and it has no company_id (indicating default), it needs to be created as a new email for that company instead of default
        else if (parseInt(editId) && editTemplate && editTemplate.company_id === null && !defaultCheck) {
            delete formDataObject.id;
            formDataObject.email_template_type_id = selectedTemplate;
            edit = false;
        }
        else if(editId !== "new"){
            formDataObject.id = parseInt(editId); //otherwise, use it's existing id if it has one
            edit = true;
        } 
        else {
            formDataObject.email_template_type_id = selectedTemplate;
        } //or it's new and has no id

        //company id 
        if(newDefault || defaultCheck) formDataObject.company_id = null;
        else formDataObject.company_id = companyId;
        
        formDataObject.body = currentEmail;
        try{
            let response;
            if(edit) response = await Email.templates.edit(formDataObject);
            else if(!edit) response = await Email.templates.add(formDataObject);
            else console.error("Improper Id - cannot edit or add template");
            if(response.status === 200){
                setSuccess(setSuccessToast("Template Saved"));
                history.push("/p/email/templates");
            }
        }catch(ex){
            console.error(ex);
        }
    }

    return (
        <Container fluid>
            <div className="header">
                <Breadcrumb>
                    <Breadcrumb.Item href="/p/">Home</Breadcrumb.Item>
                    <Breadcrumb.Item href="/p/email/templates">Email Template Dashboard</Breadcrumb.Item>
                    <Breadcrumb.Item active>Email Text Editor</Breadcrumb.Item>
                </Breadcrumb>
                {success}
                {error}
            </div>
            <Card className={`content-card ${styles["email-text-editor-wrapper"]}`}>
                <h4 className="section-title">
                    Email Text Editor
                </h4>
                {loading ?
                    <SkeletonTheme color="#e0e0e0">
                        <div className="mt-3 text-center">
                            <Skeleton height={28} width={200}/>
                            <Skeleton height={16} count={4} />
                        </div>
                    </SkeletonTheme>   
                    : 
                    <form onSubmit={onSubmit}>
                        <div className={styles["split-div"]}>
                            {newDefault &&
                                <div>
                                    {userModulePermission[317] ?
                                        <p>
                                            Selecting a template type for which a default email already exists will replace that default email.  This allows us another method to create defaults and to create a default for which an email doesn't already exist.
                                        </p>
                                        :
                                        <p>
                                            You do not have permission to edit default emails.  
                                        </p>
                                    }
                                </div> 
                            }
                            <div className={styles["email-wysiwyg-wrapper"]}>
                                {newDefault && userModulePermission[317] &&
                                    <p className={styles["input-group"]}>
                                        <label htmlFor="template-types">
                                            Template Types
                                        </label>
                                        <select id="template-types" name="email_template_type_id" onChange={(e)=>handleSelectedTemplate(e)}>
                                            <option value={0} disabled> 
                                                Select A Template Type
                                            </option>
                                            {templateTypes.map((type)=>(
                                                <option key={type.id} value={type.id} >
                                                {type.name}
                                                </option>
                                            ))}
                                        </select>
                                    </p>  
                                }
                                {editId !== "new" &&
                                    <>
                                        <p className={styles["input-group"]}>
                                            <label>
                                                Template Type:
                                            </label>
                                            <span>
                                                {templateTypes?.filter((type)=>type.id === editTemplate?.email_template_type_id)[0]?.name}
                                            </span>
                                        </p>
                                    </>
                                }
                                <p className={styles["input-group"]}>
                                    <label htmlFor="email-name">
                                        Email Name:
                                    </label>
                                    <input 
                                        id="email-name"
                                        name="name"
                                        defaultValue={editTemplate ? editTemplate?.name : ""}
                                    />
                                </p>
                                {/* <p className={styles["brand-wrapper-div"]}>
                                    <label>
                                            Brand Wrapper:
                                    </label>
                                    <span>
                                        <select>

                                        </select>
                                        <br />
                                        <select>

                                        </select>
                                    </span>
                                </p> */}
                                <p className={styles["input-group"]}>
                                    <label htmlFor="subject">
                                        Subject:
                                    </label>
                                    <input 
                                        type="text"
                                        id="subject"
                                        name="subject"
                                        defaultValue={editTemplate ? editTemplate.subject : ""}
                                    />
                                </p>
                                {userModulePermission[317] && !newDefault && !editTemplate.company_id &&
                                    <div>
                                        <label htmlFor="default">
                                            Default
                                        </label>
                                        <input 
                                            type="checkbox"
                                            id="default"
                                            name="default"
                                            checked={defaultCheck}
                                            onChange={(e)=>setDefaultCheck(e.target.checked)}
                                        />
                                        <p>
                                            By checking, it will apply no company_id and will be a default email.  Otherwise it will be applied to the company you're logged in for, even if there is already one.
                                        </p>
                                    </div>
                                }
                                <div>
                                    <label htmlFor="email-text-editor">
                                        Body:
                                    </label>
                                    <br />
                                    <Wysiwyg 
                                        rows={10}
                                        name="email-text-editor"
                                        defaultValue={currentEmail}
                                        onBlur={(e, value)=>blurHandler({target: {name: "email-text-editor", value: value}})}
                                        customToolbarButtons={
                                            [<CustomOption 
                                                variables={filteredVariables} 
                                                loading={loading} 
                                                editorState={currentEmail}
                                            />] 
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                        <div className={styles["save-btn-div"]}>
                            <br />
                            <button type="submit" disabled={newDefault && !userModulePermission[317]}>
                                Save
                            </button>
                        </div>
                    </form>
                }  
            </Card>
        </Container>
    )
}
