import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { format } from 'date-fns';
import { useHistory, Link } from "react-router-dom";
import { Card, Container, Col, Row, Table, Form, InputGroup, Button } from 'react-bootstrap';
import SubHeader from '../../../components/common/SubHeader';

import Stack from '../../../components/common/Stack';
import ErrorCatcher from '../../../components/common/ErrorCatcher';
import Pagination from '../../../components/common/Pagination';
import { authCheck } from "../../../utils/auth";

import Transactions from '../Transactions';
import Create from '../Create';

import { authUserHasModuleAccess } from "../../../utils/auth";

import APIGiftCards from '../../../api/GiftCards';
import TableSkeleton from '../../../components/common/TableSkeleton';

const CREATE_MODULE_ID = 322;

const Dashboard = (props) => {
    let history = useHistory();
    let user = useSelector(state => state.auth.user);

    const [error, setError]=useState();
    const [loading, setLoading]=useState(true);
    const [giftcards, setGiftCards]=useState([]);
    const [activeGiftCard, setActiveGiftCard]=useState(null);
    const [totalItems, setTotalItems]=useState(0);
    const [itemsPerPage, setItemsPerPage]=useState(25);
    const [page, setPage]=useState(1);
    const [pages, setPages]=useState([]);
    const [search, setSearch]=useState("");
    const [sortColumn, setSortColumn]=useState("id");
    const [sortOrder, setSortOrder]=useState("DESC");
    const [userCanCreate, setUserCanCreate]=useState(false);
    const [showCreateGiftCard, setShowCreateGiftCard]=useState(false);

    useEffect(() => {
        const checkPermission = async () => {
            setLoading(true);
            try {
                const res = await authUserHasModuleAccess(CREATE_MODULE_ID, user.profile.id);
                setUserCanCreate(res[CREATE_MODULE_ID]);
            } catch (error) { 
                console.error(error);
            }
            setLoading(false);
        }

        const getGiftCards = async () => {
            setActiveGiftCard(null);
            setLoading(true);
            try {
                const res = await APIGiftCards.get({max_records: itemsPerPage, page_no: page, sort_col: sortColumn, sort_direction: sortOrder});
                if (res.data) {
                    setTotalItems(res.data.total_record_count);
                    setGiftCards(res.data?.giftcards?.data || []);
                    const total_pages=Math.ceil(res.data.total_record_count/itemsPerPage);
                    let tmp_pages=[];
                    for(let i=0;i<total_pages;i++){
                        tmp_pages.push(i);
                    }
                    setPages(tmp_pages);
                } else if (res.errors){
                    setError(<ErrorCatcher error={res.errors} />)
                }
            } catch (error){
                console.log(error);
            }
            setLoading(false);
        }

        checkPermission();
        getGiftCards();

        return () => {
            setLoading(false);
            setGiftCards([]);
            setActiveGiftCard(null);
            setUserCanCreate(false);
        }        
    },[page, sortColumn, sortOrder, search, itemsPerPage, user.profile.id]);


    /* part of the hack to change the url to conditionally show the create page, but its buggy
    useEffect(() => {
        const checkForCustomRoute = () => {
            const isCustomRoute = window.location.pathname === '/p/giftcards/new';
            setShowCreateGiftCard(isCustomRoute);
        }
  
        checkForCustomRoute();
  
        window.addEventListener('customRouteChange', checkForCustomRoute);
        return () => {
            window.removeEventListener('customRouteChange', checkForCustomRoute);
        }
    }, []);
    */

    const gotoPage=(page)=>{
        if (page>0) setPage(page);
    }

    const headerClick = (column) => {
        setSortColumn(column);
        if (sortOrder==="ASC") setSortOrder("DESC");
        else setSortOrder("ASC");
    }

    const rowClick = (e, row) => {
        if (row.id) setActiveGiftCard(prev=> prev===row.id ? null : row.id);
    }

    const searchOnChange = (e)=>{
        if(e.target.value.length >2 ){
            setSearch(e.target.value)
        }
        if(e.target.value.length === 0){
            setSearch("")
        }
    }

    return (
        <Container fluid>
            
            <Row className="body">
                <Col>
                    <div className="tab-scrol">
                        <Form.Group>
                            <InputGroup>
                                <Form.Control
                                    id="search_input"
                                    placeholder={`Search gift cards`}
                                    aria-label={`Search gift cards`}
                                    onChange={searchOnChange}
                                />
                            </InputGroup>
                        </Form.Group>
                        <div className="ov-x-sc">
                        <Table className="table">
                            <thead>
                                <tr>
                                    <th onClick={()=>{headerClick("id")}}>
                                        <span>ID</span>
                                        <i className={`ml-1 fad fa-${sortColumn==="id"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                    </th>
                                    <th onClick={()=>{headerClick("card_code")}}>
                                        <span>Code</span>
                                        <i className={`ml-1 fad fa-${sortColumn==="card_code"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                    </th>
                                    <th onClick={()=>{headerClick("balance")}}>
                                        <span>Balance</span>
                                        <i className={`ml-1 fad fa-${sortColumn==="balance"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                    </th>
                                    <th onClick={()=>{headerClick("recipient_full_name")}}>
                                        <span>Recipient Name</span>
                                        <i className={`ml-1 fad fa-${sortColumn==="recipient_full_name"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                    </th>
                                    <th onClick={()=>{headerClick("recipient_email")}}>
                                        <span>Recipient Email</span>
                                        <i className={`ml-1 fad fa-${sortColumn==="recipient_email"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                    </th>
                                    <th onClick={()=>{headerClick("delivery_date")}}>
                                        <span>Delivery Date</span>
                                        <i className={`ml-1 fad fa-${sortColumn==="delivery_date"?sortOrder==="DESC"?'sort-down':'sort-up':'sort'}`}></i>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {loading &&
                                    <TableSkeleton columns={5} rows={5} />
                                }
                                {giftcards?.map((row, i) => (
                                    <React.Fragment key={`gc-row-${i}`}>
                                        <tr onClick={(e)=>{rowClick(e, row)}}> 
                                            <td>{row.id}</td>
                                            <td>{row.card_code}</td>
                                            <td>${row?.current_balance?.toFixed()}</td>
                                            <td>{row.recipient_full_name}</td>
                                            <td>{row.recipient_email}</td>
                                            <td>{format(new Date(row.delivery_date), "M/d/yy h:mm a")}</td>
                                        </tr>
                                        {activeGiftCard === row.id &&
                                            <tr>
                                                <td colSpan="5" className="p-0">
                                                    <Transactions gift_card_id={row.id} />
                                                </td>
                                            </tr>
                                        }
                                    </React.Fragment>
                                ))}
                            </tbody>
                        </Table>
                        </div>
                        {pages.length>1?
                            <div className="d-flex justify-content-end">
                                <Pagination
                                    itemsCount={totalItems}
                                    itemsPerPage={itemsPerPage}
                                    currentPage={page}
                                    setCurrentPage={setPage}
                                    alwaysShown={false}
                                />
                            </div>
                        : null}
                    </div>
                </Col>
            </Row>
            {error}
        </Container>
    );
}

export default Dashboard;