@import '../../assets/css/scss/variables';
@import '../../assets/css/scss/themes';
@import '../../assets/css/scss/mixins';

.streaming-page{
    .card {
        width: 100%;
    }
    .stream-schedule .btn {
        margin-bottom: 10px;
    }
    .stream-schedule .row {
        margin: 0 0 0 40px;
    }
    .stream-schedule {
        margin-bottom: 10px;
        div{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            span:first-child{
                max-width: 170px;
            }
            span{
                width: 350px;
                .btn-primary{
                    width: 350px;
                }
                display: flex;
                justify-content: flex-end;
            }
            @media (max-width: 700px){
                flex-direction: column;
                align-items: center;
            }
            @media (max-width: 500px){
                span{
                    max-width: 250px;
                    .btn-primary{
                        width: 250px;
                    }
                }
            }
        }
    }
    .block-streams{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        .each-block{
            margin: 0 1rem 2rem 1rem;
            border-radius: $card-border-radius;
            border: 1px solid $grey-7;
            padding-top: 8px;
            width: 400px;
            min-width: 275px;
        }
        .title-info{
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            p{
                margin-bottom: 0;
            }
        }
        .game-name{
            font-weight: 700;
            text-align: center;
        }
        .game-descriptor{
            margin-left: 10px;
        }
        .game-info{
            @include basic-flex-column;
            p{
                text-align: center;
            }
            padding: 5px 1rem 1rem 1rem;
        }
    }
    @media (max-width: 500px){
        .card-body{
            padding: 0;
        }
        ul{
            padding-left: 0;
            padding-right: 0;
        }
    }
}
.streaming-modal{
    display: flex;
    justify-content: center;
    iframe {
        width: 100%;
        height: 100%;
        min-height: 480px;
    }
}