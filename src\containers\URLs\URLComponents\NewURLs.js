import React, { useState } from 'react';
import { Button } from 'react-bootstrap';
import { UrlPagesTypeahead, WebsitesTypeahead, ThemesTypeahead } from '../../../components/Typeahead';
import styles from '../Urls.module.scss'

export const NewURLs =({siteboss = true, formRef, handleSubmit, companyId, localErrors})=>{

    const [websiteId, setWebsiteId]=useState(null)
    
    return(
        <form className={styles["new-urls-wrapper"]} onSubmit={handleSubmit}>
            <div className={styles["flex-col"]}>
                <div className={styles["flex-col"]}>
                    <label htmlFor="subdomain">
                        Subdomain
                        {siteboss && 
                            <span className="required-star">
                                {" "}*
                            </span>
                        }
                    </label>
                    <input 
                        type="text"
                        name="subdomain"
                        id="subdomain"
                    />
                </div>
                <label htmlFor="domain">
                    Domain
                </label>
                <input 
                    type="text"
                    name="domain"
                    id="domain"
                    disabled={siteboss ? true : false}
                    defaultValue={siteboss ? ".siteboss.net" : ""}
                />
                {!siteboss &&
                    <p className={styles["sub-text"]}>
                        If no domain is provided, it will default to "siteboss.net"
                    </p>
                }
            </div>
            <div className={styles["flex-col"]}>
                <label htmlFor="website">
                    Website
                    <span className="required-star">
                        {" "}*
                    </span>
                </label>
                <WebsitesTypeahead
                    id="website"
                    name="website"
                    companyId={companyId}
                    async={false}
                    multiple={false}
                    passSelection={(selection)=>{
                        //have to set the state to trigger a change to the other typeahead will render, but also need for form data
                        if(selection.length > 0) {
                            formRef.current.website_id = selection[0]?.id;
                            setWebsiteId(selection[0]?.id);
                        }
                        else {
                            formRef.current.website_id = null;
                            setWebsiteId(null);
                        }
                    }}
                />
            </div>
            <div className={styles["flex-col"]}>
                <label htmlFor="theme">
                    Theme
                    <span className="required-star">
                        {" "}*
                    </span>
                </label>
                <ThemesTypeahead 
                    name="website_theme_id"
                    id="website_theme_id"
                    async={false}
                    multiple={false}
                    passSelection={(selection)=>{
                        if(selection.length > 0) formRef.current.website_theme_id = selection[0]?.id
                        else formRef.current.website_theme_id = null;
                    }}
                />
            </div>
            <div className={styles["flex-col"]}>
                <label htmlFor="home">
                    Home Page
                </label>
                {websiteId ? 
                    <UrlPagesTypeahead 
                        name="index_page"
                        id="index_page"
                        websiteId={websiteId ? websiteId : null}
                        async={false}
                        multiple={false}
                        formatForLabel={(page)=>page?.title}
                        passSelection={(selection)=>{
                            if(selection.length > 0) formRef.current.index_page = selection[0]?.slug
                            else formRef.current.index_page = null;
                        }}
                    />
                    :
                    <p className={styles["sub-text"]}>
                        Select a website to select a home page
                    </p>
                }
            </div>
            <div>
                {localErrors.length > 0 && localErrors.map((error)=>(
                    <p key={error} className="error-text">
                        {error}
                    </p>
                
                ))}
            </div>
            <div className={styles["flex-col"]}>
                <Button type="submit">
                    Save
                </Button>
            </div>
        </form>
    )
}