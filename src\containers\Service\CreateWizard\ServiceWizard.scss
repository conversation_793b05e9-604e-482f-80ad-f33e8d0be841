@import '../../../assets/css/scss/mixins';


.select-token-button {
    margin-left: 6px !important;

    i {
        margin-left: 6px;
    }
}

.associated-fee {
    .custom-control-input {
        margin-right: 0.5rem;
    }
}

.service-edit-token-typeahead{
    min-width: 300px;
    max-width: 400px;
    .tokens-list > .rbt-token{
        min-width: 250px;
        max-width: 400px;
    }
}

.service-edit-product-modal{
    max-width: 90vw !important;
}
.service-token-default-token-select{
    @include basic-input-select;
    min-width: 250px;
    max-width: 400px;
}