import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Col, Row, Form, Button } from 'react-bootstrap';

import * as actions from '../../../store/actions';
import { selectCurrentEvent } from '../../../store/selectors';

const Age = ({ onChangeInput=()=>{} }) => {

    const currentEvent = useSelector(selectCurrentEvent);
    const errors = useSelector(state => state.eventwizard.errors);

    const [hasAgeRequirement, setHasAgeRequirement] = useState(currentEvent?.min_age || currentEvent?.max_age ? 1 : 0);

    return (
        <>
            <Row>
                <Col className="wizard">
                    <span className="title">Is there an age requirement?</span>
                    <Form.Row className="centered">
                        <Form.Check 
                            type="radio"
                            label="No"
                            id={`age_requirement_0`}
                            name="age_requirement"
                            value={0}
                            checked={!currentEvent.min_age && !currentEvent.max_age}
                            onChange={()=>setHasAgeRequirement(0)}
                            isInvalid={!!errors.age_requirement}
                            className="form-radio sm"
                        />
                        <Form.Check 
                            type="radio"
                            label="Yes"
                            id={`age_requirement_1`}
                            name="age_requirement"
                            value={1}
                            checked={currentEvent.min_age || currentEvent.max_age}
                            onChange={()=>setHasAgeRequirement(1)}
                            isInvalid={!!errors.age_requirement}
                            className="form-radio sm"
                        />
                    </Form.Row>
                </Col>
                {hasAgeRequirement === 1 && 
                    <Col sm={12}>
                        <Row className="centered">
                            <Col sm="auto">
                                <Form.Group controlId="min_age">
                                    <Form.Label>Minimum Age</Form.Label>
                                    <Form.Control
                                        name="min_age"
                                        type="number"
                                        min="0"
                                        max="120"
                                        value={currentEvent.min_age}
                                        onChange={onChangeInput}
                                        isInvalid={!!errors.min_age}
                                    />
                                </Form.Group>
                            </Col>
                            <Col sm="auto">
                                <Form.Group controlId="max_age">
                                    <Form.Label>Maximum Age</Form.Label>
                                    <Form.Control
                                        name="max_age"
                                        type="number"
                                        min="0"
                                        max="120"
                                        value={currentEvent.max_age}
                                        onChange={onChangeInput}
                                        isInvalid={!!errors.max_age}
                                    />
                                </Form.Group>
                            </Col>
                        </Row>
                    </Col>
                }
            </Row>

            <div className={`err ${!!errors.age_requirement || !!errors.age_range ? "" : "hidden"}`}>
                {errors.age_requirement}
                {errors.age_range}
            </div>
        </>
    );
}

export default Age;