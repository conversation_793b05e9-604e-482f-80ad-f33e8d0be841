@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes.scss';

.register-category-wrapper{
    @include basic-flex-column;
    flex-wrap: wrap;
    height: 100%;

    .sub-title{
        font-size: $card-title-font-size;
        font-weight: $card-title-font-weight;
    }

    .each-row{
        @include basic-flex-column;
        .each-day-div{
            @include basic-flex-row;
            flex-wrap: wrap;
            margin-bottom: 8px;
            margin-top: 8px;
        }
    }
}

.modal-override{
    &.modal-content .modal-body{
        overflow-x: visible !important;
        overflow-y: visible !important;
    }
}