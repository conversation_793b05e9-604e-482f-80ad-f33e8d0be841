@import '../../../assets/css/scss/mixins';
@import '../../../assets/css/scss/variables';

.transaction-page-wrapper{
    h4{
        i{
            margin-right: 3px;
            color: $secondary-color;
        }
    }
    .data-pair{
        p{
            max-width: 410px;
        }
        display: flex;
        flex-direction: row;
        span:first-child{
            min-width: 150px;
            font-weight: 700;
        }
        span:last-child{
            max-width: 300px;
            width: 300px;
        }
    }
    .transaction-hr{
        border-top-color: 1px solid $primary-color !important;
    }
}