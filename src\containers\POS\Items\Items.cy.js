/*eslint-disable*/
/// <reference types="cypress" />

import { Items } from './Items';
import './Items.scss';
import store from '../../../redux-store';

describe('It will mount the item component and check data rendering', {scrollBehavior: "center"}, ()=>{

    let foodItems;
    let drinkItems;
    let foodCategory;
    let nutellaAddon;

    before(()=>{
        cy.fixture('POS/itemResponse.json').then((data)=>{
            foodItems = data.food;
            drinkItems = data.drinks;
            foodCategory = data.foodCat;
            nutellaAddon = data.nutellaAddons;
        })
        store.dispatch({ //force Redux to have some of the data it needs
            type: 'POS_RESET_ITEMS',
            register_id: 2,
        })
        store.dispatch({
            type: 'SET_COMPANY_CONFIG',
            config: {
                guest_user_id: 7
            }
        })
    });

    beforeEach(()=>{
        cy.intercept('GET', '/api/category/*', foodCategory).as('getCategories')
        cy.intercept('POST', '/api/product', foodItems).as('getFoodProducts');
        cy.intercept('GET', '/api/product/variant/**', {status: 200});
        cy.mount(<Items category_id={[5]} register_id={2} active_register={2} />, {reduxStore: store});
        cy.wait('@getCategories');
        cy.viewport(1000, 800)
    })
    
    it('will mount and check basic data', ()=>{
        cy.get('[data-cy="btn-category-all"]')
            .should('exist')
            .invoke('text')
            .should('include', "All Categories")
        cy.get('.category-buttons > :nth-child(2)') //should be food button
            .should('exist')
            .invoke('text')
            .should('include', "Food")
        cy.get('.category-buttons > :nth-child(3)') //should be drinks
            .should('exist')
            .invoke('text')
            .should('include', "Drinks")
        cy.get('.category-buttons > :nth-child(4)') //should be Smack Extra
            .should('exist')
            .invoke('text')
            .should('include', "Smack extra")
    })

    it('will make sure that variants will display',()=>{
        cy.get('.modal-content').should('not.exist');
        cy.get(':nth-child(5) > [data-cy="product-card"] > .card-body') //should be bakery
            .should('exist')
            .invoke('text')
            .should('include', "Bakery")
        cy.get(':nth-child(5) > [data-cy="product-card"] > .card-body') //should be bakery
            .click();   
        cy.get('.modal-content').should('exist');
        cy.get('[data-cy="variant-picker-wrap"] > .pos-products')
            .children()
            .should('have.length', 3)
        cy.get('[data-cy="variant-picker-wrap"] > .pos-products')
            .invoke('text')
            .should('include', "Brownie")
    })
    
    it("will check to make sure addons display properly",()=>{
        cy.intercept('GET', '/api/product/variant/1452/addons?id=1452', nutellaAddon);
        cy.intercept('POST', '/api/order/add', {status: 200});
        cy.intercept('PUT', '/api/order/update', {status: 200});
        cy.get('.modal-content').should('not.exist');
        cy.get(':nth-child(3) > [data-cy="product-card"] > .card-body') //should be nutella
            .should('exist')
            .invoke('text')
            .should('include', "Add Nutella")
        cy.get(':nth-child(3) > [data-cy="product-card"] > .card-body') 
            .click();
        cy.get('.modal-content').should('exist');
        cy.get('.modal-body').within(()=>{
            cy.get('[data-cy="product-card"]')
                .eq(0)
                .invoke('text')
                .should('include', "No Almond Butter")
        })
    })
})
