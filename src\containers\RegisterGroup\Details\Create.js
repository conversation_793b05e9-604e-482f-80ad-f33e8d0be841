import React from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Container, Col, Row, Card } from "react-bootstrap";
import RegisterGroupForm from "./RegisterGroupForm";
import SubHeader from "../../../components/common/SubHeader";

const Create = (props) => {

    return (
        <Container fluid className="full-height register-container create">
            <SubHeader items = {[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/registers" }, text: "Registers" },
                { linkAs: Link, linkProps: { to: "/p/registers/groups" }, text: "Register Groups" },
                { text: "Create" }
            ]} />

            <Card className="content-card">
                <h2>Create New Register Group</h2>
                <Row className="module-content">
                    <RegisterGroupForm />
                </Row>
            </Card>
        </Container>
    );
};

export default Create;
