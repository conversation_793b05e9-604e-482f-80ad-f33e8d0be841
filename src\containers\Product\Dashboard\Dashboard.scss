@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes';

.product-dashboard {

    .type-button {
        margin: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 130px;
    }

    .type-button-list {
        display: flex;
        flex-wrap: wrap;
    }

    .type-button:focus {
        background-color: $blue-verylight;
    }

    .product-row .form-check-input {
        margin-top: 3px;
    }
}

.row-selected {
    background-color: $primary-light-color !important;
}