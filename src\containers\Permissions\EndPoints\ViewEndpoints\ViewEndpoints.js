import React, { useState, useEffect, useCallback, useRef } from 'react'
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import { useHistory } from 'react-router-dom';

import ModuleTypeahead from '../../../../components/Typeahead/ModuleTypeahead';
import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import Toast from '../../../../components/Toast';
import { NewEndpointRow } from '../CreateEndpoints/NewEndpointRow';
import { getEndpoints } from '../../PermissionsUtils/PermissionUtils';
import { checkForSlash, checkForUniqueness } from '../EndpointUtils';

import Permissions from '../../../../api/Permissions';
import { setErrorCatcher } from '../../../../utils/validation';

export const ViewEndpoints = ({initialEndpoints, setError, setSuccess, setReset, ...props}) => {

    const mountedRef = useRef(false);
    const history = useHistory();
    const [ endpoints, setEndpoints ] =useState(initialEndpoints);
    const [ selectedEndpoint, setSelectedEndpoint ]=useState(null);
    const [ moduleForFilter, setModuleForFilter ]=useState(null);
    const [ slugFilter, setSlugFilter ]=useState("");
    const [ showEditModal, setShowEditModal ]=useState(false);
    const [ selectedModules, setSelectedModules ]=useState([]);

    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current = false
    },[]);

    useEffect(()=>{
        if((moduleForFilter || slugFilter) && mountedRef.current){
            const getEndpointsGet = async()=>{
                let endpointResponse = await getEndpoints({moduleId: moduleForFilter, slug: slugFilter, includeModules: true});
                if(mountedRef.current){
                    if(endpointResponse) setEndpoints(endpointResponse.data);
                    else setError(<ErrorCatcher error={endpointResponse.errors} />)
                }
            }

            getEndpointsGet();

        }else if(!moduleForFilter && !slugFilter && mountedRef.current){
            setEndpoints(initialEndpoints);
        }
    },[moduleForFilter, slugFilter, setError, initialEndpoints])

    const handleSearch = (e)=>{
        if(!e.target.value) setSlugFilter("");
        else setSlugFilter(e.target.value);
    }

    const handleViewModuleClick = (id)=>{
        if(selectedEndpoint && id === selectedEndpoint) setSelectedEndpoint(null);
        else setSelectedEndpoint(id);
    }

    const handleEdit = (endpoint)=>{
        setSelectedEndpoint(endpoint);
        setShowEditModal(true)
    }

    const handleHide=()=>{
        setSelectedEndpoint(null);
        setShowEditModal(false);
        setReset(true);
    }

    const handleDelete=async(id)=>{
        setSuccess(null)
        try{
            let response = await Permissions.Endpoints.delete({id:id});
            handleResponse(response, "Deleted");
        }catch(ex){
            console.error(ex)
        }
    }

    const checkForSlash=(slug)=>{
        if(slug.charAt(slug.length -1) === "/") slug = slug.slice(0, -1);
        if(slug[0]!=="/") slug = `/${slug}`
        return slug;
    }

    const handleSave=async(id, slug, method)=>{
        setSuccess(null)
        let uniqueMatch = await checkForUniqueness(slug, id);
        if(!uniqueMatch || uniqueMatch?.existing?.id === id){
            const editObject={id: id}
            if(slug) editObject.slug = slug;
            if(method) editObject.method = method;
            editObject.modules_to_associate = selectedModules?.map((module)=>module.id)
            try{
                let response = await Permissions.Endpoints.edit(editObject);
                handleResponse(response, "Edited");
            }catch(ex){
                console.error(ex)
            }
        }else setError(setErrorCatcher("Slug already exists", true))
    }

    const handleResponse=(response, wording)=>{
        if(response.status===200){
            setSuccess(<Toast>Endpoint {wording} Successfully</Toast>)
            handleHide()
        }
        else if(response.errors){
            setError(<ErrorCatcher error={response.errors} />)
            handleHide()
        }
    }

    return (
        <div className="view-endpoints">

            <div className="filter-row">
                <h6>Filter By:</h6>
                <div className="input-col" data-cy="module-typeahead-div">
                    <label htmlFor="module-filter">Module</label>
                    <ModuleTypeahead name="module-filter" passSelection={(selection)=>setModuleForFilter(selection[0]?.id)}/>
                </div>
                <div className="input-col" data-cy="slug-filter-div">
                    <label htmlFor="slug-filter">Slug</label>
                    <input name="slug-filter" onChange={handleSearch} />
                </div>
            </div>


            {endpoints?.length > 0 && 
                endpoints.map((endpoint)=>(
                    <div className="each-endpoint" key={`each-endpoint-${endpoint.id}`}>
                        <div className="endpoint-row" data-cy="each-endpoint-div">
                            <div className="method-slug">
                                <span className={`method ${endpoint?.method}`} data-cy="endpoint-type">
                                    {endpoint?.method?.toUpperCase()}
                                </span>
                                <span className="slug" data-cy="endpoint-slug">
                                    {endpoint?.slug}
                                </span>
                            </div>
                            <div>
                                <span>
                                    <Button onClick={()=>handleEdit(endpoint)} className="edit-btn" data-cy="edit-btn">
                                        <i className="far fa-pencil" /> 
                                        Edit
                                    </Button>
                                    <Button onClick={()=>handleViewModuleClick(endpoint.id)} data-cy="view-modules-btn">
                                        {selectedEndpoint && endpoint.id === selectedEndpoint ? 
                                            <i className="far fa-chevron-down" />
                                        :
                                            <i className="far fa-chevron-right" />
                                        }
                                        View Associated Modules
                                    </Button>
                                </span>
                            </div>
                        </div>
                        <div>
                            {selectedEndpoint && endpoint.id === selectedEndpoint && 
                                <>
                                    {endpoint?.modules?.length > 0 ?
                                        <>
                                            {endpoint?.modules?.map((module)=>(
                                                <div data-cy="module-list-item" key={`${module.name}-${module.id}`} className="module-row">
                                                    <div className="name-url">
                                                        <span className="name" data-cy="module-name">
                                                            {module.name}
                                                        </span>
                                                        <span className="url" data-cy="module-url">
                                                            {module.url}
                                                        </span>
                                                    </div>
                                                    <span data-cy="module-edit-btn">
                                                        <Button onClick={()=>{history.push(`/p/module/${module.id}`)}}>Edit Module <i className="far fa-arrow-right" /></Button>
                                                    </span>
                                                </div>
                                            ))}
                                        </>
                                    :
                                        <span className="module-row" data-cy="no-modules">No Associated Modules</span>
                                    }
                                </>
                            }
                        </div>
                    </div>
                ))
            }
            <Modal size={'lg'} show={showEditModal} onHide={handleHide}>
                <Modal.Header closeButton>
                    Edit Endpoint
                </Modal.Header>
                <Modal.Body className="edit-endpoint-modal">
                    <NewEndpointRow 
                        endpoint={selectedEndpoint} 
                        editEndpoint={true} 
                        removeEndpoint={handleDelete} 
                        passEndpoints={handleSave}
                        setSelectedModules={setSelectedModules}
                        validateSlug={checkForSlash}
                    />
                </Modal.Body>
            </Modal>
        </div>
    )
}
