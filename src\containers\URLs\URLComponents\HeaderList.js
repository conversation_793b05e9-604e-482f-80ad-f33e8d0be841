export const HeaderList =({handleShowEdit, showHideEditText, styles, showSubdomain, activeEdit=null})=>{
    return(
        <div className={styles["siteboss-subdomain-wrapper"]}>
            {/* <div className={styles["default"]}>
                <span className="bold">
                    Default
                </span>
            </div> */}
            {activeEdit && showSubdomain &&
                <div className={styles["domain"]}>
                    <span className="bold">
                        SubDomain
                    </span>
                </div>
            }
            {activeEdit &&
                <div className={styles["domain"]}>
                    <span className="bold">
                        Domain
                    </span>
                </div>
            }
            {!activeEdit &&
                <div className={styles["domain"]}>
                    <span className="bold">
                        URL
                    </span>
                </div>
            }
            <div className={styles["website"]}>
                <span className="bold">
                    Website
                </span>
            </div>
            <div className={styles["theme"]}>
                <span className="bold">
                    Theme
                </span>
            </div>
            <div className={styles["home"]}>
                <span className="bold">
                    Home
                </span>
            </div>
            {/* <div className={styles["active"]}>
                <span className="bold">
                    Active
                </span>
            </div>
            <div className={styles["dns"]}>
                <span className="bold">
                    DNS
                </span>
            </div>
            <div className={styles["ssl"]}>
                <span className="bold">
                    SSL
                </span>
            </div> */}
        </div>
    )
}