import React, { useState, useEffect, useCallback, useRef } from 'react'
import { Container, Card, Button, Modal } from 'react-bootstrap';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { useParams, useHistory, Link } from 'react-router-dom';
import ModuleTypeahead from '../../../../components/Typeahead/ModuleTypeahead';
import SubHeader from '../../../../components/common/SubHeader';
import Permissions from '../../../../api/Permissions';

import '../Features.scss';
import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import Toast from '../../../../components/Toast';
import RedirectModal from '../../../../components/common/RedirectModal';
import { getFeatures } from '../../PermissionsUtils/PermissionUtils';
import ProductVariantTypeahead from '../../../../components/Typeahead/ProductVariantTypeahead';
import { NewProduct } from '../../../Product/NewProduct/NewProduct';

export const CreateEditFeature = () => {

    const mountedRef = useRef(false);
    const history = useHistory();
    const params = useParams();
    const editId = params.id

    const [ error, setError ]=useState();
    const [ success, setSuccess ]=useState();
    const [ loading, setLoading ]=useState(true);

    const [ selectedModules, setSelectedModules ]=useState([]);
    const [ selectedFeature, setSelectedFeature ]=useState(null);
    const [ selectedVariants, setSelectedVariants ]=useState([]);
    const [ showProductModal, setShowProductModal ]=useState(false);
    const [ redirectHideShow, setRedirectHideShow ]=useState(false);
    const [ firstLoad, setFirstLoad ]=useState(null);
    const adminDash = JSON.parse(localStorage.getItem("adminDash"))

    const passSelection=useCallback((selection)=>{
        setSelectedVariants(selection?.map(selection=>selection.id))
    },[])

    useEffect(()=>{
        mountedRef.current = true

        return () => mountedRef.current = false
    },[]);

    useEffect(()=>{
        if(!editId){
            setLoading(false)
        }
        else if (editId){
            const getSingleFeature=async()=>{
                let response = await getFeatures({id: editId, includeModules: true, includeVariants: true});
                if(mountedRef.current && response.data){
                    setSelectedFeature(response.data[0]);
                    setSelectedVariants(response.data[0].product_variants.map((variant)=>variant.id))
                    setLoading(false)
                }
                else setError(<ErrorCatcher error={response.errors} />)
            }
            getSingleFeature();
        }
    },[editId]);

    useEffect(()=>{
        if(selectedFeature) setLoading(false);
    },[selectedFeature])

    const handleSubmit = async (e)=>{
        e.preventDefault();
        let success;
        let error;
        
        const formData = new FormData(e.target);
        const formDataObj = Object.fromEntries(formData.entries());
        if(selectedVariants.length > 0) formDataObj.product_variant_ids = selectedVariants;
        else formDataObj.product_variant_ids = [];
        if(editId) formDataObj.id = editId

        let response
        try{
            if(editId) response = await Permissions.Features.edit(formDataObj);
            else response = await Permissions.Features.create(formDataObj);

            if(response.status === 200 && mountedRef.current){
                success = "Feature has been added successfully"
                setSuccess(<Toast>{success}</Toast>)
                if(selectedModules.length > 0){
                    for(let i = 0; i < selectedModules.length; i++){
                        let module = selectedModules[i];
                        let featureId;
                        if (editId) featureId = editId;
                        else featureId = response.data.id;
                        let moduleResponse = await moduleToFeature(module, featureId);
                        if(moduleResponse){
                            error += moduleResponse;
                        }
                    }
                    if(error) setError(<ErrorCatcher error={error} />)
                }
                setRedirectHideShow(true)
            }else if(response.errors || response.status !== 200){
                setError(<ErrorCatcher error={response.errors} />)
            }
        }catch(ex){console.error(ex)}
    }

    const moduleToFeature = async(module, featureId)=>{
        try{
            let response = await Permissions.Modules.update({id: module.id, feature_id: featureId})
            if(response.status === 200 && mountedRef.current){
                return null;
            }else return response.errors;
        }catch(ex){console.error(ex)}
    }

    //for product modal
    const handleHide=(response, ids)=>{
        if(response){
            setSuccess(<Toast>Product Created Successfully</Toast>);
            setSelectedVariants([...selectedVariants, ...ids]);
            setShowProductModal(false);
            setFirstLoad(true);
        }
        //have to specify false because if !response, will trigger on null too.  Null is free pass to just close the modal with no error
        else if(response===false) {
            setError(<ErrorCatcher error={response} />)
        }
        setShowProductModal(false);
    }

    if(loading) return(
        <SkeletonTheme color="#e0e0e0">
            <div className="mt-3 text-center">
                <Skeleton height={28} width={200}/>
                <Skeleton height={16} count={4} />
            </div>
        </SkeletonTheme>
    )
    // create the breadcrumbs array
    const breadcrumbs = [
        { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" }
    ];
    // if admin dashboard, add the admin dashboard link to the breadcrumbs
    if (adminDash) {
        breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/permission/admin" }, text: "Permission Dashboard" })
    }
    // add feature dashboard to breadcrumbs
    breadcrumbs.push({ linkAs: Link, linkProps: { to: "/p/features/dashboard" }, text: "Feature Dashboard" })
    // add the current page to the breadcrumbs
    breadcrumbs.push({ text: "Feature Dashboard" })

    return (
        <Container fluid>
            <SubHeader items={breadcrumbs} />
            <Card className="content-card feature-create-edit-wrapper">
                {error}
                {success}
                <h4 className="section-title">
                    {editId ? "Edit Feature" : "New Feature"}
                </h4>
                <form onSubmit={handleSubmit} id="feature-form">
                    <div className="input-col">
                        <label htmlFor="name">
                            Name
                        </label>
                        <input required name="name" defaultValue={selectedFeature?.name || null}/>
                    </div>
                    <div className="input-col">
                        <label htmlFor="description">
                            Description
                        </label>
                        <textarea name="description" defaultValue={selectedFeature?.description || null} className="description" />
                    </div>
                    <div className="input-col">
                        <label htmlFor="modules">
                            Included Modules
                        </label>
                        {!loading &&
                            <ModuleTypeahead 
                                initialDataIds={selectedFeature?.modules?.length > 0 ? selectedFeature?.modules?.map((module)=>module.id) : null}
                                passSelection={(selection)=>setSelectedModules(selection)}
                                multiple={true}
                            />
                        }
                    </div>
                    <div className="input-col">
                        <label htmlFor="variants">
                            Select Which SiteBoss Product Variants will give access to this feature:
                        </label>
                        {!loading &&
                            <div>
                                <ProductVariantTypeahead 
                                    parentDoneLoading={loading ? false : true}
                                    initialDataIds={selectedVariants ? selectedVariants : null}
                                    passSelection={passSelection}
                                    multiple={true}
                                    variants={true}
                                    parameters={{
                                        sort_col: "name",
                                        product_type_id:11
                                    }}
                                    overrideFirstLoad={firstLoad}
                                    setOverrideFirstLoad={setFirstLoad}
                                />
                                <Button className="outline-primary" onClick={()=>setShowProductModal(true)}>Create New Product</Button>
                            </div>
                        }
                    </div>
                    <div className="btn-row">
                        <Button type="submit">Save</Button>
                        {/* <Button variant="danger">Disable Feature</Button> */}
                    </div>
                </form>
            </Card>
            <Modal dialogClassName="product-modal" show={showProductModal} onHide={()=>setShowProductModal(false)}>
                <Modal.Body>
                    <NewProduct importedType={11} onHide={handleHide}/>
                </Modal.Body>
            </Modal>
            <RedirectModal 
                show={redirectHideShow}
                onHide={()=>setRedirectHideShow(false)}
                redirectOneText="Feature Dashboard"
                redirectOneUrl="/p/features/dashboard"
                redirectTwoText="New Feature"
                redirectTwoUrl="/p/features/new"
                keyboard={false}
                backdrop="static"
                createWhat="Feature"
                create={editId ? false : true}
            />
        </Container>
    )
}
