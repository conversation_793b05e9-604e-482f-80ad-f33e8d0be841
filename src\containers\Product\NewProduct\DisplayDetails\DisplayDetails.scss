@import '../../../../assets/css/scss/variables';
@import '../../../../assets/css/scss/mixins';
@import '../../../../assets/css/scss/themes';


.product-display-wrapper{
    background-color: $card-background-color;
    margin: 1rem;
    padding: 1rem;
    border-radius: $card-border-radius;
    .display-product-heading{
        display:flex;
        align-items: center;
        flex-direction: column;
        margin-bottom: 1rem;
    }
    .prod-display-basic-details{
        display: flex;
        flex-direction: column;
        .property-group{
            display: flex;
            flex-direction: row;
            @media (max-width: 1200px){
                flex-direction: column;
            }
            div{
                min-width: 400px;
                @media (max-width: 1200px){
                    min-width: 300px;
                }
            }
        }
        .property{
            display: flex;
            flex-direction: row;
            margin-top: 3px;
            margin-bottom: 8px;
            border-bottom: solid 1px $divider-color;
            span:first-child{
                font-weight: $bold-font-weight;
                min-width: 165px;
                @media (max-width: 700px){
                    min-width: 140px;
                }
            }
            span:last-child{
                margin-right: 1rem;
            }
        }
        .description{
            @media (max-width: 600px){
                flex-direction: column;
            }
        }
        .categories{
            display: flex;
            flex-wrap:wrap;
            @media (max-width:600px){
                flex-direction: column;
            }
            .cat-name{
                margin-right: 10px;
                margin-left: 10px;
            }
        }
    }
    .prod-display-variant-details{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        .prod-display-each-variant{
            min-width: 500px;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            padding-bottom: .5rem;
            @media (max-width:600px){
                min-width: 300px;
            }
        }
        .each-wrapper{
            border: solid 1px $divider-color;
            border-radius: $card-border-radius;
            padding: .5rem;
            margin: .5rem;
        }
        .each-wrapper:hover{
            @include focus-glow-mix;
        }
        .property{
            margin: 0 1rem .5rem 1rem;
            display: flex;
            flex-direction: column;
            span:first-child{
                font-weight: 700;
                min-width: 150px;
            }
        }
    }
}