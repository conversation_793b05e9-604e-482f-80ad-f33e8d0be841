@import '../../assets/css/scss/mixins';
@import '../../assets/css/scss/variables';

.urls-wrapper{
    input, select{
        @include basic-input-select;
    }
    .header-btns{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        max-width: 800px;
    }
    .siteboss-subdomain-wrapper, .each-subdomain-wrapper{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        margin: 5px 0;
        .default{
            width: 75px;
        }
        .domain{
            width: 225px;
            word-break: break-all;
        }
        .website{
            width: 175px;
        }
        .theme{
            width: 150px;
        }
        .home{
            width: 100px;
            word-break: break-all;
        }
        // .active{
        //     width: 50px;
        // }
        // .dns{
        //     width: 50px;
        // }
        // .ssl{
        //     width: 50px;
        // }
        .delete{
            width: 25px;
        }
        .save{
            width: 75px;
        }
        .edit{
            width: 75px;
            span{
                border: 1px solid $primary-color;
                padding: 5px;
                border-radius: 2px;
            }
        }
    }
}
.edit-column-wrapper, .new-urls-wrapper{
    padding: 12px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: center;
    max-width: 800px;
    .final-row{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }
    label{
        @include basic-label;
        padding-left: 8px;
    }
    input, select{
        @include basic-input-select;
        min-width: 300px;
    }
    .flex-col{
        @include basic-flex-column;
    }
    .sub-text{
        padding: 3px 15px;
        max-width: 300px;
    }
}
.confirm-modal{
    padding: 8px;
    @include secondary-modal-shadow;
    div:not(.modal-header){
        text-align: center;
        margin-top: 5px;
    }
}
