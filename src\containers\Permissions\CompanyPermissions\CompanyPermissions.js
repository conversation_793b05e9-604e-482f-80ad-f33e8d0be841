import React, { useState, useEffect, useRef, useCallback } from 'react'

import ViewAssignFeatures from '../CompanyFeatures/ViewAssignFeatures'
import ErrorCatcher from '../../../components/common/ErrorCatcher'

import Companies from '../../../api/Companies'

export const CompanyPermissions = ({setSuccess, setError, ...props}) => {

    const mountedRef = useRef(false);
    const [ companies, setCompanies ]=useState([]);

    const mockFeatures = [
        {
            id: 1,
            name: "Services",
            default: false,
            purchased: false,
        },
        {
            id: 2,
            name: "Events",
            default: false,
            purchased: true,
        },
        {
            id: 3,
            name: "Registers",
            default: false,
            purchased: false,
        },
        {
            id: 4,
            name: "Admin Functions",
            default: true,
            purchased: false,
        },
    ]

    useEffect(()=>{
        mountedRef.current = true

        return ()=> mountedRef.current = false
    },[]);

    return (
        <div>
            <ViewAssignFeatures setSuccess={setSuccess} setError={setError} companies={companies} features={mockFeatures} /> 
        </div>
    )
}
