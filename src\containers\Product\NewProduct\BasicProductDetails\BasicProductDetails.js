import React, {useState, useEffect, useRef, useCallback, createRef} from 'react';
import { Button } from 'react-bootstrap';
import { useSelector } from 'react-redux';
import DatePicker from 'react-datepicker';

import Uploader from '../../../../components/Uploader';
import PrinterTypeahead from '../../../../components/Typeahead/PrinterTypeahead';
import Products from '../../../../api/Products';
import CategorySelect from '../../BasicInfo/CategorySelect';
import { AddOnsTypeahead } from '../../../../components/Typeahead/AddOnsTypeahead';
import Tooltip from '../../../../components/common/Tooltip';
import { authUserHasModuleAccess } from "../../../../utils/auth";

const ASSIGN_SPECIAL_PRINTER_MODULE_ID = 126;

export const BasicProductDetails = ({productStatuses, basicProduct, setBasicProductDetails, editingFirstLoad, setEditingFirstLoad, setJointAddOnCategories, setBasicProductReset, basicProductReset, setImageChanged}) => {

    const userId = useSelector(state =>state.auth.user.profile.id)
    const mountedRef = useRef(false);
    const [productName, setProductName]=useState("");
    const [productDescription, setProductDescription]=useState("");
    const [productImage, setProductImage]=useState(null);
    const [productStatus, setProductStatus]=useState(1);
    const [taxable, setTaxable]=useState(true);
    const [selectedCategories, setSelectedCategories]=useState([]);
    const [printLocations, setPrintLocations]=useState([]);
    const [productStart, setProductStart]=useState(new Date());
    const [triggerAfterLoad, setTriggerAfterLoad]=useState(0); //to trigger the useeffect that send everything upstream.  Will only change after loading the initial date from an imported item
    const [userHasModulePermission, setUserHasModulePermission] = useState(false);
    const [isAddon, setIsAddon]=useState(false);

    const checkCategoryType=useCallback(async(category)=>{
        try{
            let response = await Products.Categories.get({id: category.id});
            if(response.data){
                let addon = response.data[0]?.add_on_only;
                setIsAddon(addon === 1 ? true : false);
            }
        }catch(ex){
            console.error(ex)
        }
    },[])

    useEffect(()=>{
        mountedRef.current = true;

        const checkPermission = async () => {
            try {
                let response = await authUserHasModuleAccess(ASSIGN_SPECIAL_PRINTER_MODULE_ID);
                setUserHasModulePermission(response);
            } catch (error) { console.error(error) }
        }
        checkPermission();

        return()=>mountedRef.current = false;
    },[userId]);

    useEffect(()=>{
        if(basicProductReset){
            setProductName();
            setProductDescription();
            setProductStatus(1);
            setProductImage();
            setTaxable(true);
            setSelectedCategories([]);
            setPrintLocations([]);
            setBasicProductReset(false);
            setProductStart();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[basicProductReset])

    useEffect(()=>{
        if(editingFirstLoad.current.edit && !editingFirstLoad.current.basicInfoDone && basicProduct && mountedRef.current){
            setProductName(basicProduct?.name);
            setProductDescription(basicProduct?.description || "");
            setProductStatus(parseInt(basicProduct?.product_status_id));
            setProductImage(basicProduct?.media?.length > 0 ? basicProduct?.media[basicProduct?.media?.length -1] : "");
            setTaxable(basicProduct?.is_taxable === 1 ? true : false);
            setSelectedCategories(basicProduct?.categories);
            basicProduct?.categories.length > 0 && checkCategoryType(basicProduct?.categories[0]);
            setPrintLocations(basicProduct?.print_locations);
            setProductStart(basicProduct?.date_available);
            editingFirstLoad.current.basicInfoDone = true;
            if(editingFirstLoad.current.edit && editingFirstLoad.current.bundleDone && editingFirstLoad.current.variantsDone && editingFirstLoad.current.basicInfoDone && mountedRef.current) editingFirstLoad.current.edit=false;
            setTriggerAfterLoad(1)
        };
    },[editingFirstLoad, basicProduct, setEditingFirstLoad, checkCategoryType]);

    useEffect(()=>{
        if(!editingFirstLoad.current.edit && editingFirstLoad.current.basicInfoDone){
            let temp={...basicProduct}
            if(productName) temp.name= productName;
            if(productDescription) temp.description=productDescription;
            if(productImage) temp.media=productImage;
            if(productStatus) temp.product_status_id=productStatus;
            if(productStart) temp.date_available=productStart;
            temp.is_taxable=taxable ? 1 : 0 ;
            if(selectedCategories?.length>0) temp.category_ids=selectedCategories?.map(category => category.id); 
            else temp.category_ids=[];
            if(printLocations?.length>0) temp.print_locations=printLocations?.map(location=>location.id);
            else temp.print_locations=[];
            setBasicProductDetails(temp);
        }
    //don't want the trigger from basicProduct, just condition
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[productName, productDescription, productImage, productStatus, taxable, selectedCategories, printLocations, productStart, setBasicProductDetails, editingFirstLoad, triggerAfterLoad]);

    const handleTaxable=(e)=>{
        if(e.target.checked) setTaxable(true);
        else setTaxable(false);
    };

    const handleCategories=(e)=>{
        if(!editingFirstLoad.current.edit && editingFirstLoad.current.basicInfoDone){
            setSelectedCategories(e);
        };
    };

    const handleSelectPrinter=(e)=>{
        //we don't want the typeahead initiating any change itself until everything is done and loaded
        if(!editingFirstLoad.current.edit && editingFirstLoad.current.basicInfoDone && editingFirstLoad.current.variantsDone && editingFirstLoad.current.bundleDone){
            setPrintLocations(e);
        }
    }

    const handleImage=(imgData)=>{
        setProductImage(imgData);
        setImageChanged(true);
    }

    const handleIsAddon=(e)=>{
        setIsAddon(e.target.checked);
        setSelectedCategories([]);
    }

  return (
    <>
        <div className="basic-product-wrapper" data-cy="new-basic-product-wrapper">
            <div className="name-desc">
                <div data-cy="new-product-bd-name" className="product-flex-col">
                    <label htmlFor="product-name">
                        Name<span className="required">*</span>
                        {" "}
                        <Tooltip
                            direction="top"
                            text="This is the overall name for your whole product.  This should be a descriptive name that makes it easy for customers and staff to identify the product."
                            width="250px"
                            height="auto"
                        >
                            <i className="far fa-question-circle"/>
                        </Tooltip>
                    </label>
                    <input 
                        required 
                        name="product-name" 
                        value={productName}
                        onChange={(e)=>setProductName(e.target.value)}
                    />
                </div>
                <br />
                <div data-cy="basic-product-available-start" className="product-flex-col">
                    <label htmlFor="variant-available-at">
                        Available Starting<span className="required">*</span>
                        {" "}
                        <Tooltip
                            direction="top"
                            text="This is the date that this product will come available on.  If this is an edited product and has today's date in it, it means there wasn't originally a start date on this product."
                            width="150px"
                            height="auto"
                        >
                            <i className="far fa-question-circle"/>
                        </Tooltip>
                    </label>
                    <DatePicker
                        required
                        dateFormat="MM/dd/yyyy"
                        minDate={new Date("01/01/2021")}
                        showMonthDropdown
                        showYearDropdown
                        scrollableYearDropdown={10}
                        placeholderText="Available Start"
                        onChange={(e)=> setProductStart(e)}
                        selected={productStart}
                    />
                </div>
                <br />
                <div data-cy="new-product-bd-status" className="product-flex-col">
                    <label htmlFor="product-status">Status</label>
                    <select value={productStatus} onChange={(e)=>setProductStatus(parseInt(e.target.value))}>
                        {productStatuses.map((status)=>(
                            <option key={`status-dd-${status.id}`} value={status.id}>{status.name}</option>
                        ))}
                    </select>
                </div>
                <br />
                <div data-cy="new-product-bd-taxable">
                    <label htmlFor="product-taxable">Taxable</label>
                    <input 
                        type="checkbox" 
                        name="product-taxable"
                        checked={taxable}
                        onChange={handleTaxable}
                    />
                </div>
                <br />
                <div className="description" data-cy="new-product-bd-description">
                    <label htmlFor="product-description">Description</label>
                    <textarea 
                        name="product-description" 
                        value={productDescription}
                        onChange={(e)=>setProductDescription(e.target.value)} 
                    />
                </div>
            </div>
            <div className="status-tax-print">
                <div data-cy="new-product-new-image" className="product-flex-col" style={{alignItems: "left"}}>
                    <label>Product Image</label>
                    <Uploader 
                        DZRef={createRef()}
                        type="image/*, .heic, .heif"
                        previewHeight={!productImage ? "35px" : "256px"}
                        minWidth="150px"
                        maxWidth={productImage ? "256px" : "170px"}
                        maxHeight="350px"
                        croppedWidth={512} //size for cropping the image
                        croppedHeight={512} //size for cropping the image
                        backgroundSize="contain"
                        previewSrc={productImage?.path}
                        onSend={handleImage}
                    >
                        {!productImage &&
                            <Button>
                                Upload an Image
                            </Button>
                        }
                    </Uploader>
                </div>
                <br />
                {userHasModulePermission[ASSIGN_SPECIAL_PRINTER_MODULE_ID] &&
                    <div data-cy="new-product-bd-printer-select" className="product-flex-col">
                        <label>Printer</label>
                        <PrinterTypeahead 
                            multiple
                            initialDataIds={printLocations}
                            label={
                                <>
                                    Printers
                                    {" "}
                                    <Tooltip
                                        direction="top"
                                        text="If this product needs to be printed in a special printer, choose it here."
                                        width="240px"
                                        height="auto"
                                    >
                                        <span>
                                            <i className="far fa-question-circle"/>
                                        </span>
                                    </Tooltip>
                                </>
                            } 
                            passSelection={handleSelectPrinter}/>
                    </div>
                }
                <br />
                <div data-cy="new-product-bd-add-on-categories" className="product-flex-col">
                    <label htmlFor="Add Ons Category">
                        Add Ons (Add to all Variants) {" "}
                        <Tooltip
                            direction="left"
                            text="Select addons here that you would like applied to every variant.  
                                You can then remove them from individual variants if you see fit.  
                                Using addons on this manner gives the product access to ADDING addons in the POS, 
                                such as being able to add cheese or onions to a burger via a 'burger toppings' addon."
                            width="300px"
                            height="auto"
                        >
                            <span>
                                <i className="far fa-question-circle"/>
                            </span>
                        </Tooltip>
                    </label>
                    <AddOnsTypeahead 
                        allowNew={true} 
                        name="product-addons-typeahead" 
                        data-cy="product-addons-typeahead" 
                        setParentAddOnCategories={(e)=>setJointAddOnCategories(e)} 
                        multiple={false}
                        hideSelected={true}
                    />
                </div>
                <br />
                <div data-cy="new-product-bd-new-categories" className="product-flex-col categories-div">
                    <div>
                        <label htmlFor = "is-addon">
                            Is this an Add On Product? {" "}
                            <Tooltip
                                text="If this product should appear as an addon product, click this checkbox.  
                                    Clicking this checkbox will ensure that only addon categories can be selected so that there is no confusion. 
                                    Flipping back and forth between addon/not will remove categories. 
                                    If this is left unchecked, the categories will dictate what registers this product will appear on."
                            >
                                <span>
                                    <i className="far fa-question-circle"/>
                                </span>
                            </Tooltip>    
                        </label>
                        <input 
                            type="checkbox"
                            name="is-addon"
                            checked={isAddon ? true : false}
                            onChange={handleIsAddon}
                        />
                    </div> 
                    <CategorySelect 
                        key={isAddon}
                        name="product-categories" 
                        initCategories={selectedCategories} 
                        onSelectCategories={handleCategories} 
                        isAddon={isAddon ? true : false}
                        label={
                            <>
                                Categories
                            </>
                        }
                    />
                </div>
            </div>
        </div>
    </>
  )
}
