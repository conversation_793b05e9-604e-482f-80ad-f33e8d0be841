import React, { useEffect, useState, useCallback } from 'react';
import { Container, Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import DatePicker from 'react-datepicker';

import SubHeader from '../../../../components/common/SubHeader'; 
import { PickupOrderList } from '../SubComponents/PickupOrderList';
import { getPickupOrders } from '../OnlinePickupUtils';
import Pagination from '../../../../components/common/Pagination';
import { setSuccessToast, setErrorCatcher } from '../../../../utils/validation';

import OrderStatusesTypeahead from '../../../../components/Typeahead/OrderStatusesTypeahead';

import styles from '../OnlinePickup.module.scss'
import { endOfDay, startOfDay } from 'date-fns';

export const OnlinePickupPage = () =>{

    const [success, setSuccess]=useState(null);
    const [error, setError]=useState(null);
    const [loading, setLoading]=useState(true);
    const [orders, setOrders]=useState([]);

    //filter options
    const [orderStatusIds, setOrderStatusIds]=useState([8,9,10]);
    const [startDateFilter, setStartDateFilter]=useState(new Date());
    const [endDateFilter, setEndDateFilter]=useState(null);
    const [orderIdFilter, setOrderIdFilter]=useState(null);
    const [userIdFilter, setUserIdFilter]=useState(null);
    //secondary filter options
    const [currentPage, setCurrentPage]=useState(1);
    const [totalRecords, setTotalRecords]=useState();
    const [recordsPerPage, setRecordsPerPage]=useState(25);
    const [sortCol, setSortCol]=useState("id");
    const [sortDirection, setSortDirection]=useState("ASC")

    
    const getOrders = useCallback(async()=>{
        setLoading(true);
        setError(null);
        setSuccess(null);
        let filters={
            order_status_id: orderStatusIds.length ? orderStatusIds : [8,9,10,11],
            method: "POST",
            max_records: recordsPerPage,
            page_no: currentPage,
            sort_col: sortCol,
            sort_direction: sortDirection,
            date_search_by: "updated_at"
        }
        
        if(startDateFilter && !endDateFilter){
            filters.start_datetime=startDateFilter
            filters.end_datetime = endOfDay(startDateFilter);
        }else if(endDateFilter && !startDateFilter){
            filters.end_datetime=endDateFilter
            filters.start_datetime = startOfDay(endDateFilter);
        } else if (endDateFilter && startDateFilter){
            filters.start_datetime = startOfDay(startDateFilter);
            filters.end_datetime = endOfDay(endDateFilter);
        }
        if(orderIdFilter) filters.order_id=orderIdFilter
        if(userIdFilter) filters.user_id=userIdFilter

        let orders = await getPickupOrders(filters)
        if(orders.data) {
            setOrders(orders.data.orders)
            setTotalRecords(orders.data.total_record_count)
        }else if (orders.errors) setError(setErrorCatcher(orders.errors))

        setLoading(false)
    },[orderStatusIds, startDateFilter, endDateFilter, orderIdFilter, userIdFilter, currentPage, recordsPerPage, sortCol, sortDirection])

    /** Initial useEffect on load */
    useEffect(()=>{ 
        getOrders();

        return ()=> {
            setError(null);
            setSuccess(null);
            setOrders([]);
        }
    },[getOrders])

    /** function passed to the PickupOrderList for when changing status */
    const handleSuccess=()=>{
        setSuccess(setSuccessToast("Status Changed Successfully"));
        getOrders();
    }

    /** function for setting the basic details for the filters */
    const handlePropChange=(e, hook, parse=false)=>{
        e.preventDefault();
        if(parse) hook(parseInt(e.target.value))
        else hook(e.target.value);
    }

    const handleDateChange=(value, hook, parse=false)=>{
        if(parse) hook(parseInt(value))
        else hook(value);
    }

    const handleItemsPerPage=(e)=>{
        e.preventDefault()
        if(e.target.value==="") setRecordsPerPage(1)
        else setRecordsPerPage(parseInt(e.target.value))
    }

    const handleSort=(key, dir)=>{
        setSortDirection(dir);
        setSortCol(key)
    }

    return(
        <Container fluid>
            <SubHeader 
                items={[
                    {linkAs: Link, linkProps: { to: "/p/home"}, text: "Home"},
                    {text: "Online Pickup Management"}
                ]}
            />
            <Card className="content-card">
                {error}
                {success}
                <h4 className="section-title">
                    Online Pickup Order Management
                </h4>
                <div className={styles["filter-row"]}>
                    <div className={styles["col-pair"]}>
                        <label 
                            htmlFor="o_id"
                        > 
                            Order Id
                        </label>
                        <input 
                            placeholder="Order Id"
                            name="o_id"
                            onChange={(e)=>handlePropChange(e, setOrderIdFilter, true)}
                        />
                    </div>
                    <div className={styles["col-pair"]}>
                        <label 
                            htmlFor="u_id"
                        > 
                            User Id
                        </label>
                        <input 
                            placeholder="User Id"
                            name="u_id"
                            onChange={(e)=>handlePropChange(e, setUserIdFilter, true)}
                        />
                    </div>
                    <div className={styles["col-pair"]}>
                        <label htmlFor="start_date">
                            Start Date
                        </label>
                        <DatePicker 
                            required={false}
                            dateFormat="MM/dd/yyyy"
                            minDate={new Date("01/01/2024")} //2024 is when this feature is being implemented so they won't exist further back then that
                            showMonthDropdown
                            selected={startDateFilter}
                            onChange={(e)=>handleDateChange(e, setStartDateFilter)}
                            placeholderText={"Start Date"}
                        />
                    </div>
                    <div className={styles["col-pair"]}>
                        <label htmlFor="end_date">
                            End Date
                        </label>
                        <DatePicker 
                            required={false}
                            dateFormat="MM/dd/yyyy"
                            minDate={new Date("01/01/2024")} //2024 is when this feature is being implemented so they won't exist further back then that
                            showMonthDropdown
                            selected={endDateFilter}
                            onChange={(e)=>handleDateChange(e, setEndDateFilter)}
                            placeholderText={"End Date"}
                        />
                    </div>
                    <div className={styles["col-pair"]}>
                        <label
                            htmlFor="order-status-id"
                        >
                            Order Status
                        </label>
                        <OrderStatusesTypeahead 
                            name="order-status-id"
                            ids={[8,9,10,11]}
                            initialDataIds={orderStatusIds}
                            passSelection={(selection)=>setOrderStatusIds(selection.map((selection)=>selection.id))}
                        />
                    </div>
                </div>
                <PickupOrderList 
                    loading={loading}
                    data={orders}
                    handleSuccess={handleSuccess}
                    sortOnClick={handleSort}
                    modal={true}
                />
                <div className={styles["col-pair"]}>
                    <div>
                        <label htmlFor="items_per_page">Items Per Page:</label>
                        <input
                            name="items_per_page"
                            type="numeric"
                            onChange={handleItemsPerPage}
                            placeholder={recordsPerPage}
                        />
                    </div>
                    <Pagination
                        itemsCount={totalRecords}
                        itemsPerPage={recordsPerPage}
                        currentPage={currentPage}
                        setCurrentPage={setCurrentPage}
                        alwaysShown={false}
                    />
                </div>
            </Card>
        </Container>
    )
}