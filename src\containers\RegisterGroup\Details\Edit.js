import React, { useEffect, useState } from "react";
import { useParams, useLocation, Link } from "react-router-dom";
import Container from "react-bootstrap/Container";
import Row from "react-bootstrap/Row";
import Col from "react-bootstrap/Col";
import Card from "react-bootstrap/Card";
import SubHeader from "../../../components/common/SubHeader";

import RegisterGroupForm from './RegisterGroupForm';

import "../../Register/Register.scss";

const Edit = (props) => {

    const location = useLocation();
    const { id } = useParams();

    const [name, setName] = useState("Edit");

    return (
        <Container fluid className="register-container edit">
            <SubHeader items={[
                { linkAs: Link, linkProps: { to: "/p/home" }, text: "Home" },
                { linkAs: Link, linkProps: { to: "/p/registers" }, text: "Registers" },
                { linkAs: Link, linkProps: { to: "/p/registers/groups" }, text: "Register Groups" },
                { text: name }
            ]} />

            <Card className="content-card">
                <h2>Edit Register Group</h2>
                <div className="order-2 order-lg-1">
                    <RegisterGroupForm
                        id={id}
                        referer={location.pathname}
                        setDisplayName={setName}
                    />
                </div>
            </Card>
        </Container>
    )
};

export default Edit;
