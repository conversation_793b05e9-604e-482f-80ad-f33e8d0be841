import React, { useEffect, useState, use<PERSON><PERSON>back, useMemo, useRef } from "react";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import { useHistory } from "react-router-dom";
import { Container, Col, Row, Form, Button, InputGroup } from "react-bootstrap";
import { JsonEditor } from 'jsoneditor-react';

import {confirm} from '../../../components/Confirmation';
import ErrorCatcher from "../../../components/common/ErrorCatcher";
import Toast from "../../../components/Toast";
import { CategoryTypeahead, LocationTypeahead } from "../../../components/Typeahead";
import { authUserHasModuleAccess } from "../../../utils/auth";

import Registers from "../../../api/Registers";

import "../../Register/Register.scss";
import 'jsoneditor-react/es/editor.min.css';

const WIDGET_MODULE_ID = 89; // delete register groups

// all of the defaults - if we have an input for it it MUST be listed in here to not break things
const defaultRegisterGroup = {
    id: 0,
    description: "",
    name: "",
    register_group_definition: {
        num_days_display: null,   // null or int
    },
};

const RegisterGroupForm = ({ id=null, setDisplayName=()=>{} }) => {

    let history = useHistory();

    let jsonEditorRef = useRef(null);

    const [locationSelection, setLocationSelection] = useState(null);
    const [registerStart, setRegisterStart] = useState(null);
    const [registerDefinition, setRegisterDefinition] = useState(null);
    const [name, setName] = useState(defaultRegisterGroup.name);
    const [description, setDescription] = useState(defaultRegisterGroup.description);
    const [numDays, setNumDays] = useState(1); // this is for the input box
    const [hideRegisterDefinition, setHideRegisterDefinition] = useState(true);
    const [jsonFieldHasFocus, setJsonFieldHasFocus] = useState(false);
    const [validated, setValidated] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState();
    const [success, setSuccess] = useState();
    const [userHasModulePermission, setUserHasModulePermission] = useState(false);

    useEffect(()=>{
        const checkPermission = async () => {
            try {
                let response = await authUserHasModuleAccess(WIDGET_MODULE_ID);
                setUserHasModulePermission(response);
            } catch (error) { console.error(error) }
        }
        checkPermission();
    },[])

    // first load, check for id
    useEffect(() => {
        let mounted = true;

        // get register data if page is editing an existing register
        if (id) {
            Registers.Groups.get({ register_group_id: id }).then((response) => {
                if (mounted && response.data[0]) {
                    setRegisterStart(response.data[0]);
                }
            }).catch((e) => console.error(e));
        } else {
            setRegisterStart(defaultRegisterGroup);
        }

        // cancel stuff when component unmounts
        return() => {
            mounted = false;
        };
    }, [id]);

    useEffect(() => {
        if (registerStart) {
            let registerDefinitionCopy = defaultRegisterGroup.register_group_definition;
            if (registerStart?.register_group_definition) {
                registerDefinitionCopy = {...registerStart.register_group_definition};
                // make sure the register_definition has all the expected parts, if not use the default values
                Object.keys(defaultRegisterGroup.register_group_definition).forEach((key) => {
                    if (!registerDefinitionCopy[key]) {
                        registerDefinitionCopy[key] = defaultRegisterGroup.register_group_definition[key];
                    }
                });
            }
            setRegisterDefinition(registerDefinitionCopy);
            setName(registerStart.name || defaultRegisterGroup.name);
            setDescription(registerStart.description || defaultRegisterGroup.description);
        }
    }, [registerStart]);

    useEffect(() => {
        setDisplayName(name);
    }, [name, setDisplayName]);

    function clickCancel() {
        history.goBack();
    }

    const clickDelete = async (e) => {
        // TODO: move any registers to another Register Group prior to deletion
        confirmDeleteHandler({
            text: `Are you sure you want to remove this Register Group? You cannot undo this!`,
            click: async () => {
                let response = await Registers.Groups.delete({id: id}).catch((e) => console.error(e));
                try {
                    if (!response?.errors) {
                        setSubmitting(false);
                        setValidated(false);
                        setSuccess (
                            <Toast>Register Group deleted successfully!</Toast>
                        );
                    } else { // api returned errors
                        setSubmitting(false);
                        setError (
                            <ErrorCatcher error={
                                response.errors
                            }/>
                        );
                    }
                } catch (e) { // no response at all
                    setSubmitting(false);
                    setError (
                        <ErrorCatcher error={e}/>
                    );
                }
            }
        });
    };

    const confirmDeleteHandler = useCallback((props) => {
        confirm(props.text, {
            title: "Whoa!",
            okText: "Yes",
            cancelText: "No",
            cancelButtonStyle: "light"
        }).then((result) => {
            if (result === true) 
                props.click();
        });
    }, []);

    const handleChangeName = (e) => {
        setName(e.target.value);
    }

    const handleChangeDescription = (e) => {
        setDescription(e.target.value);
    }

    /* CHANGLE LIMIT DAYS */

    const handleChangeLimitDays = useCallback((e) => {
        let num_days_display = null;
        if (e.target.name==="limit_days" && parseInt(e.target.value)===1) {
            num_days_display = 1;
        }
        let newRegisterDefinition = {...registerDefinition, num_days_display: num_days_display};
        setRegisterDefinition(newRegisterDefinition);
    }, [registerDefinition]);

    const handleChangeNumDays = useCallback((e) => {
        let num = e.target.value;
        // allow numerical values but also allow a blank string
        if (num === "" || num === null) {
            setNumDays("");
        }
        let val = parseInt(num);
        if (!isNaN(val) && val > 0) {
            setNumDays(val);
        }
    }, []);

    useEffect(() => {
        // only update the register definition if it's a number, not a blank string - blank strings will get removed onBlur
        if (!isNaN(numDays)) {
            setRegisterDefinition(prev => ({...prev, num_days_display: parseInt(numDays)}));
        }
    }, [numDays]);

    useEffect(() => {
        // update the input field if the register definition is directly typed in
        if (jsonFieldHasFocus && !isNaN(registerDefinition?.num_days_display) && registerDefinition?.num_days_display > 0) {
            setNumDays(registerDefinition.num_days_display);
        }
    }, [registerDefinition, jsonFieldHasFocus]);
    
    const handleBlurNumDays = useCallback((e) => {
        // if it's still blank then reset it
        if (numDays === "") {
            let number = !isNaN(registerDefinition.num_days_display) ? registerDefinition.num_days_display : defaultRegisterGroup.register_group_definition.num_days_display;
            setNumDays(number);
        }
    }, [numDays, registerDefinition]);

    /*  */

    const handleChangeRegisterDefinition = useCallback((e) => {
        setRegisterDefinition(e);
    }, []);

    useEffect(() => {
        if (success) {
            const timer = setTimeout(() => {
                history.push("/p/registers");
            }, 3000);
            return() => clearTimeout(timer);
        }
    }, [history, success]);

    // form submission
    const submitHandler = async (e) => {
        const form = e.currentTarget;

        e.preventDefault();
        e.stopPropagation();

        setValidated(true);
        setSubmitting(true);
        setError(null);
        setSuccess(null);

        if (form.checkValidity() === true) {
            const formData = new FormData(e.target);
            const formDataObj = Object.fromEntries(formData.entries());

            formDataObj.register_group_definition = JSON.stringify(registerDefinition);   // must stringify before sending to API
            
            // When editing a register
            if (id) {
                formDataObj.id = parseInt(id);
            }
            let response;
            if (id) 
                response = await Registers.Groups.edit(formDataObj).catch((e) => console.error(e));
             else 
                response = await Registers.Groups.create(formDataObj).catch((e) => console.error(e));
            
            try {
                if (!response?.errors) {
                    setValidated(false);
                    setSuccess (
                        <Toast>Register Group saved successfully!</Toast>
                    );
                } else { // api returned errors
                    setSubmitting(false);
                    setError (
                        <ErrorCatcher error={
                            response.errors
                        }/>
                    );
                }
            } catch (e) { // no response at all
                setSubmitting(false);
                setError (
                    <ErrorCatcher error={e}/>
                );
            }
        } else {
            setSubmitting(false);
        }
    };

    // work around to making the JsonEditor update with changes to registerDefinition
    useEffect(() => {
        const editor = jsonEditorRef && jsonEditorRef.current && jsonEditorRef.current.jsonEditor;
        if(editor && registerDefinition){
            editor.update(registerDefinition);
        }
    },[jsonEditorRef, registerDefinition])

    const pagePartRegisterDefinitionInput = useMemo(() => {
        if (registerDefinition===null) return <p>Loading...</p>
        return (
            <JsonEditor
                ref={jsonEditorRef}
                name="registerDefinition-input"
                value={registerDefinition}
                onChange={handleChangeRegisterDefinition}
                onBlur={() => setJsonFieldHasFocus(false)}
                onFocus={() => setJsonFieldHasFocus(true)}
                mode='code'
                navigationBar={false}
                statusBar={false}
                search={false}
                sortObjectKeys={false}
            />
        );
    }, [registerDefinition, handleChangeRegisterDefinition, jsonEditorRef]);


    return (
        <Container fluid>
            {success}
            <Form noValidate
                validated={validated}
                onSubmit={submitHandler}>
                <Form.Row>
                    <Col sm="12" lg="6">
                        <Form.Group controlId="name">
                            <Form.Label>Name</Form.Label>
                            <Form.Control required type="text" name="name" value={name} onChange={handleChangeName} maxLength="45" />
                        </Form.Group>
                    </Col>
                    <Col xs="12">
                        <Form.Group controlId="description">
                            <Form.Label>Description</Form.Label>
                            <Form.Control type="text" name="description" value={description} onChange={handleChangeDescription} maxLength="255" />
                        </Form.Group>

                        {!!registerDefinition ?
                        (<>
                            <Form.Group className="radio-left">
                                <Form.Check
                                    type="radio"
                                    label="Show All Orders"
                                    id="limit_days-false"
                                    name="limit_days"
                                    value="0"
                                    checked={registerDefinition?.num_days_display===null}
                                    onChange={handleChangeLimitDays}
                                    required
                                />
                                <div>
                                    <Form.Check
                                        type="radio"
                                        label="Limit Display"
                                        id="limit_days-true"
                                        name="limit_days"
                                        value="1"
                                        checked={registerDefinition?.num_days_display!==null}
                                        onChange={handleChangeLimitDays}
                                        required
                                    />
                                    {registerDefinition?.num_days_display!==null &&
                                        <div className="num-days-display">
                                            <Form.Label>Number of days to display</Form.Label>
                                            <Form.Control
                                                type="text"
                                                name="num_days"
                                                value={numDays}
                                                onChange={handleChangeNumDays}
                                                onBlur={handleBlurNumDays}
                                            />
                                        </div>
                                    }
                                </div>
                            </Form.Group>
                        </>)
                        :
                            <>loading...</>
                        }

                        <Form.Group controlId="register_definition">
                            <div className="show-register-button-row">
                                <Button
                                    variant="light"
                                    onClick={() => setHideRegisterDefinition(prev => !prev)}
                                >
                                    {hideRegisterDefinition ? "Edit Register Definition (Advanced)" : "Hide Register Definition"}
                                </Button>
                            </div>

                            {!hideRegisterDefinition &&
                                <>
                                    <Form.Label>Register Definition</Form.Label>
                                    {pagePartRegisterDefinitionInput}
                                </>
                            }
                        </Form.Group>

                    </Col>
                </Form.Row>

                <div className="button-row">
                    <div>
                        <Button
                            variant="secondary"
                            onClick={clickCancel}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            type="submit"
                            disabled={submitting}
                            className={`${submitting ? " submitting" : ""}`}
                        >
                            Save Changes
                        </Button>
                    </div>
                    <div>
                        {userHasModulePermission &&
                            <Button
                                variant="danger"
                                onClick={clickDelete}
                                disabled={submitting}
                                className={id ? "" : "hidden"}
                            >
                                Delete This Register Group
                            </Button>
                        }
                    </div>
                </div>
            </Form>
            {error} </Container>
    );
};


export default RegisterGroupForm;
