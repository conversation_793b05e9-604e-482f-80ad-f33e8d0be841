import React, {useState, useEffect, useRef, useCallback} from 'react'
import Skeleton, {SkeletonTheme} from 'react-loading-skeleton';

import { 
    ItemizedVariants, 
    SubscriptionVariant,
    MeasurementAndSKUExtension,
    SingleVariant,
    EachBasicVariant,
    TokenVariants,
    FeatureVariant
} from './VariantTypes';
import usePrevious from '../../../../components/common/CustomHooks'
import Products from '../../../../api/Products';

const SUB_TYPES = [
    {id: 1, name: "Individual"},
    {id: 2, name: "Family"},
    {id: 3, name: "Group"},
]

export const Variants = ({
    resetChildren, 
    propVariant, 
    editingFirstLoad,
    setCreatedVariants, 
    jointAddOnCategories, 
    setJointAddOnCategories, 
    ...props
}) => {

    const mountedRef=useRef(false);
    let variant = {}
    
    const [loading, setLoading]=useState(true);
    const [variantName, setVariantName]=useState(propVariant.name);
    const [variantPrice, setVariantPrice]=useState("");
    const [dateAvailable, setDateAvailable]=useState();
    const [dateEnd, setDateEnd]=useState();
    const [activeStatus, setActiveStatus]=useState(parseInt(propVariant.product_status_id) || 1);
    const [billInterval, setBillInterval]=useState("");
    const [intervalQuantity, setIntervalQuantity]=useState("");
    const [billNumTimes, setBillNumTimes]=useState("");
    const [subscriptionTypeId, setSubscriptionTypeId]=useState(1);
    const [subMaxUsers, setSubMaxUsers]=useState(1);
    const [activationFee, setActivationFee]=useState("");
    const [expiresIn, setExpiresIn]=useState("");
    const [SKU, setSKU]=useState("");
    const [UPC, setUPC]=useState("");
    const [isShippable, setIsShippable]=useState(false);
    const [weight, setWeight]=useState("");
    const [height, setHeight]=useState("");
    const [variantLength, setLength]=useState("");
    const [width, setWidth]=useState("");
    const [addOnCategories, setAddOnCategories]=useState([]);
    const [variantId, setVariantId]=useState("");
    const [selectedFeatures, setSelectedFeatures]=useState([]);
    const [triggerAfterLoad, setTriggerAfterLoad]=useState(0); //to trigger the useeffect that send everything upstream.  Will only change after loading the initial date from an imported item
    const [showMeasurementOptions, setShowMeasurementOptions]=useState(props.defaultShowMeasurements);
    const [includeNew, setIncludeNew]=useState(true);
    const oldRefresh=usePrevious(resetChildren)

    const getVariant=useCallback(async(id)=>{
        try{
            let response = await Products.Variants.get({id: id})
            if(!response.errors && mountedRef.current){
                setSelectedFeatures(response.data[0]?.features)
                setAddOnCategories(response.data[0]?.add_on_categories)
                setVariantName(response.data[0]?.name);
                setVariantPrice(parseFloat(response.data[0].price));
                if(response.data[0].date_available && (response.data[0].date_available?.includes('1970') || response.data[0].date_available?.includes('19769'))) setDateAvailable(new Date())
                else if(response.data[0].date_available) setDateAvailable(new Date(response.data[0]?.date_available));
                if(response.data[0].date_available_until && !response.data[0]?.date_available_until?.toString()?.includes('1970') && !response.data[0]?.date_available_until?.toString()?.includes("1969")) setDateEnd(new Date(response.data[0]?.date_available_until));
                setActiveStatus(parseInt(response.data[0]?.product_status_id));
                setBillInterval(response.data[0]?.bill_interval);
                setBillNumTimes(response.data[0].bill_num_times);
                setIntervalQuantity(response.data[0]?.interval_quantity);
                if(response.data[0]?.bill_interval && !response.data[0]?.interval_quantity) setIntervalQuantity(1);
                setSubscriptionTypeId(response.data[0].subscription_type_id ? response.data[0].subscription_type_id : 1);
                setSubMaxUsers(response.data[0]?.subscription_max_users)
                setActivationFee(response.data[0]?.activation_fee);
                setExpiresIn(response.data[0]?.expires_in_days);
                setSKU(response.data[0]?.sku);
                setUPC(response.data[0]?.upc);
                setIsShippable(response.data[0]?.is_shippable ===1 ? true : false);
                setWeight(response.data[0]?.weight);
                setHeight(response.data[0]?.height);
                //have to use this method for length otherwise it tries to do the length of the object
                setLength(response.data[0]["length"]);
                setWidth(response.data[0].width);
                setVariantId(response.data[0]?.id);
            }
            else if(response.errors) {
                console.error(response.errors)
            }
        }catch(ex){
            console.error(ex);
        }
        finally{
            editingFirstLoad.current.loadedVariants ++;
            if(editingFirstLoad.current.loadedVariants === editingFirstLoad.current.numberOfVariants) editingFirstLoad.current.variantsDone = true
            if(editingFirstLoad.current.edit && editingFirstLoad.current.bundleDone && editingFirstLoad.current.variantsDone && editingFirstLoad.current.basicInfoDone && mountedRef.current) editingFirstLoad.current.edit=false;
            if(mountedRef.current) setLoading(false);
            setTriggerAfterLoad(1);
        }
    // don't want it triggering on 'editingFirstLoad'.  Just want editingFirstLoad to be reacting to what's happening here.
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[]);

//#region useEffect

    useEffect(()=>{
        mountedRef.current = true

        return()=>mountedRef.current=false
    },[]);

    useEffect(()=>{
        if(resetChildren !== oldRefresh && oldRefresh !==undefined && !editingFirstLoad.current.edit && mountedRef.current){
            setSelectedFeatures([]);
            setVariantName();
            setVariantPrice();
            setDateAvailable();
            setDateEnd();
            setActiveStatus(1);
            setBillInterval("");
            setIntervalQuantity("");
            setBillNumTimes("");
            setSubscriptionTypeId(1);
            setSubMaxUsers(1);
            setActivationFee("");
            setExpiresIn("");
            setSKU("");
            setUPC("");
            setIsShippable(false);
            setWeight("");
            setHeight("");
            setLength("");
            setWidth("");
            setAddOnCategories([]);
            setShowMeasurementOptions(false);
            setIncludeNew(true);
        }
    // we don't want editing first load to be a trigger, just to check it
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[resetChildren, oldRefresh]);

    useEffect(()=>{
        //using date available as a check to see if the product has loaded or not, as it's required for product creation
        if(editingFirstLoad.current.edit 
            && !editingFirstLoad.current.variantsDone 
            && propVariant 
            && !dateAvailable
        ){
            if(propVariant && propVariant.id) getVariant(propVariant.id);
            else if(editingFirstLoad.current.edit && propVariant) {
                editingFirstLoad.current.variantsDone = true;
                if(editingFirstLoad.current.edit && editingFirstLoad.current.bundleDone && editingFirstLoad.current.variantsDone && editingFirstLoad.current.basicInfoDone && mountedRef.current) editingFirstLoad.current.edit=false;
                setLoading(false)
            }
        }else if(!editingFirstLoad.current.edit && mountedRef.current) setLoading(false);
        
    //not including date available in the dependency array - using it to see if there's a product or not, as it's a required element of a product.
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[editingFirstLoad, propVariant, getVariant]);

    useEffect(()=>{
        if(!editingFirstLoad.current.edit){
            let tempVariants = new Array(...props.createdVariants)
            let index = tempVariants.findIndex(variant => variant.temp_id === propVariant.temp_id)
            tempVariants.splice(index, 1);
            variant.feature_ids=selectedFeatures?.map(feature=>feature.id);
            if(variantName) variant.name=variantName;
            if(variantPrice || variantPrice===0 || variantPrice==="0") variant.price=variantPrice;
            if(dateAvailable) variant.date_available=dateAvailable;
            if(dateEnd) variant.date_available_until=dateEnd;
            else variant.date_available_until = null;
            if(billInterval) variant.bill_interval=billInterval;
            if(billInterval && intervalQuantity && billInterval==="m") variant.interval_quantity=intervalQuantity;
            else if(billInterval && billInterval==="m" && !intervalQuantity) variant.interval_quantity=1;
            if(billNumTimes && billInterval) variant.bill_num_times=billNumTimes;
            else if(billInterval && !billNumTimes) variant.bill_num_times=null;
            if(subscriptionTypeId)variant.subscription_type_id=subscriptionTypeId;
            if(subscriptionTypeId && subscriptionTypeId===1)variant.subscription_max_users = 1; 
            else if(subscriptionTypeId && subscriptionTypeId !==1 && subMaxUsers) variant.subscription_max_users=subMaxUsers;
            if(activationFee) variant.activation_fee=activationFee;
            if(expiresIn) variant.expires_in_days=parseInt(expiresIn); //for tokens
            if(SKU) variant.sku=SKU;
            if(UPC) variant.upc=UPC;
            if(weight) variant.weight=weight;
            if(height) variant.height=height;
            if(variantLength) variant.length=variantLength;
            if(width) variant.width=width;
            if(isShippable) variant.is_shippable=isShippable ? 1 : 0 ;
            if(variantId)variant.id=variantId;
            if(addOnCategories.length>0)variant.add_on_categories = addOnCategories?.map(addOn => addOn.id);
            else variant.add_on_categories = [];
            if(props.importedType===11) variant.include_new=includeNew;
            variant.product_status_id=activeStatus || 1;
            variant.temp_id=propVariant.temp_id;
            tempVariants.splice(index, 0, variant); //splicing it back in where it was removed to maintain the order
            setCreatedVariants(tempVariants);
        }
    // eslint disabled because "problem" is that propVariant and variant are not passed in.  We don't want these triggering a render, we want them to match the new local state    
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[variantName, variantPrice, dateAvailable, dateEnd, activeStatus, billInterval, intervalQuantity, billNumTimes, subscriptionTypeId, subMaxUsers, activationFee, expiresIn, SKU, UPC, weight, height, variantLength, width, isShippable, addOnCategories, triggerAfterLoad, selectedFeatures, includeNew])

//#endregion useEffect
//#region Handlers
    const changePriceHandler=(e)=>{
        if(e.target.value==="") setVariantPrice(0);
        else setVariantPrice(e.target.value);
    }
//#endregion Handlers

    if(loading) return(
        <SkeletonTheme color="#e0e0e0">
            <Skeleton height={12} width={1000} style={{marginTop:"1rem"}} />
            <br />
            <Skeleton height={24} width={200} />
            <Skeleton height={24} width={200} style={{marginLeft: "1rem", marginRight: "1rem"}} />
            <Skeleton height={24} width={200} style={{marginRight: "1rem"}} />
            <Skeleton height={24} width={200} style={{marginBottom: "2rem"}} />
            <br />
        </SkeletonTheme>
    )

  return (
    <div className="variant-component-wrapper">
        {/* 11 Features */}
        {props.selectedProductType===11 &&
            <FeatureVariant 
                variant={propVariant}
                importedType={props.importedType}
                selectedFeatures={selectedFeatures}
                setSelectedFeatures={setSelectedFeatures}
                setIncludeNew={setIncludeNew}
            />
        }
        {/*1 Subscriptions*/}
        {props.selectedProductType===1 &&
            <SubscriptionVariant
                variant={propVariant}
                setVariantName={setVariantName}
                variantName={variantName}
                changePriceHandler={changePriceHandler}
                variantPrice={variantPrice}
                setDateAvailable={setDateAvailable}
                dateAvailable={dateAvailable}
                setDateEnd={setDateEnd}
                dateEnd={dateEnd}
                activeStatus={activeStatus}
                setActiveStatus={setActiveStatus}
                setBillInterval={setBillInterval}
                billInterval={billInterval}
                setIntervalQuantity={setIntervalQuantity}
                intervalQuantity={intervalQuantity}
                setBillNumTimes={setBillNumTimes}
                billNumTimes={billNumTimes}
                setActivationFee={setActivationFee}
                activationFee={activationFee}
                productStatuses={props.productStatuses} 
                removeVariantHandler={props.removeVariantHandler}
                createdVariants={props.createdVariants}
                selectedProductType={props.selectedProductType}
                addOnCategories={addOnCategories}
                setAddOnCategories={setAddOnCategories}
                jointAddOnCategories={jointAddOnCategories}
                setJointAddOnCategories={setJointAddOnCategories}
                subscriptionTypeId={subscriptionTypeId}
                setSubscriptionTypeId={setSubscriptionTypeId}
                subMaxUsers={subMaxUsers}
                setSubMaxUsers={setSubMaxUsers}
                subTypes={SUB_TYPES}
            />
        }
        {/*2 Physical */}
        {/*7 Rental */}
        {(props.selectedProductType===2 || props.selectedProductType===7)&&
            <ItemizedVariants 
                variant={propVariant}
                productStatuses={props.productStatuses} 
                variantName={variantName}
                setVariantName={setVariantName}
                variantPrice={variantPrice}
                dateAvailable={dateAvailable}
                setDateAvailable={setDateAvailable}
                dateEnd={dateEnd}
                setDateEnd={setDateEnd}
                activeStatus={activeStatus}
                setActiveStatus={setActiveStatus}
                removeVariantHandler={props.removeVariantHandler}
                createdVariants={props.createdVariants}
                changePriceHandler={changePriceHandler}
                addOnCategories={addOnCategories}
                setAddOnCategories={setAddOnCategories}
                jointAddOnCategories={jointAddOnCategories}
                setJointAddOnCategories={setJointAddOnCategories}
            />
        }
        {/* 8 Food and drink */}
        {props.selectedProductType===8 &&
            <>
                {props.createdVariants.length===1 &&
                    <SingleVariant 
                        variant={propVariant}
                        changePriceHandler={changePriceHandler} 
                        variantPrice={variantPrice}
                        setDateAvailable={setDateAvailable}
                        dateAvailable={dateAvailable}
                        setDateEnd={setDateEnd}
                        dateEnd={dateEnd}
                        addOnCategories={addOnCategories}
                        setAddOnCategories={setAddOnCategories}
                        jointAddOnCategories={jointAddOnCategories}
                        setJointAddOnCategories={setJointAddOnCategories}
                        createdVariants={props.createdVariants}
                    />
                }
                {props.createdVariants.length >1 &&
                    <EachBasicVariant 
                        variant={propVariant}
                        setVariantName={setVariantName}
                        variantName={variantName}
                        changePriceHandler={changePriceHandler}
                        variantPrice={variantPrice}
                        activeStatus={activeStatus}
                        setActiveStatus={setActiveStatus}
                        setDateAvailable={setDateAvailable}
                        dateAvailable={dateAvailable}
                        setDateEnd={setDateEnd}
                        dateEnd={dateEnd}
                        removeVariantHandler={props.removeVariantHandler} 
                        productStatuses={props.productStatuses} 
                        addOnCategories={addOnCategories}
                        setAddOnCategories={setAddOnCategories}
                        jointAddOnCategories={jointAddOnCategories}
                        setJointAddOnCategories={setJointAddOnCategories}
                        createdVariants={props.createdVariants}
                    />
                }
            </>
        } 
        {/*2 Physical */} {/*7 Rental */} {/* 8 Food and Drink */}
        {(props.selectedProductType === 8 || props.selectedProductType===2 || props.selectedProductType===7) &&
            <MeasurementAndSKUExtension 
                isShippable={isShippable}
                setIsShippable={setIsShippable}
                setUPC={setUPC}
                UPC={UPC}
                setSKU={setSKU}
                SKU={SKU}
                weight={weight}
                setWeight={setWeight}
                height={height}
                setHeight={setHeight}
                variantLength={variantLength}
                setLength={setLength}
                width={width}
                setWidth={setWidth}
                defaultShowMeasurements={props.defaultShowMeasurements}
                defaultShowSKUs={props.defaultShowSKUs}
                addOnCategories={addOnCategories}
                setAddOnCategories={setAddOnCategories}
                jointAddOnCategories={jointAddOnCategories}
                setJointAddOnCategories={setJointAddOnCategories}
                setShowMeasurementOptions={setShowMeasurementOptions}
                showMeasurementOptions={showMeasurementOptions}
            />
        }
        {/*9 Tokens */}
        {props.selectedProductType===9 &&
            <TokenVariants 
                setDateAvailable={setDateAvailable}
                dateAvailable={dateAvailable}
                variantPrice={variantPrice}
                setExpiresIn={setExpiresIn}
                expiresIn={expiresIn}
                changePriceHandler={changePriceHandler}
                addOnCategories={addOnCategories}
                setAddOnCategories={setAddOnCategories}
                jointAddOnCategories={jointAddOnCategories}
                setJointAddOnCategories={setJointAddOnCategories}
            />
        }
        {/*3 Digital */} {/*4 Service */} {/*5 Class */} {/*6 Bundle */} {/* 10 Cancellation Fee */} {/* 11 Features */}
        {/*Includes everything except the special ones, so new product categories added will default to this model of product entry*/}
        {props.selectedProductType && props.selectedProductType!==1 && props.selectedProductType!==2 && props.selectedProductType!==7 && props.selectedProductType!==8 && props.selectedProductType !==9 &&
            <>
                {props.createdVariants.length===1 &&
                    <SingleVariant 
                        variant={propVariant}
                        changePriceHandler={changePriceHandler} 
                        variantPrice={variantPrice}
                        setDateAvailable={setDateAvailable}
                        dateAvailable={dateAvailable}
                        setDateEnd={setDateEnd}
                        dateEnd={dateEnd}
                        addOnCategories={addOnCategories}
                        setAddOnCategories={setAddOnCategories}
                        jointAddOnCategories={jointAddOnCategories}
                        setJointAddOnCategories={setJointAddOnCategories}
                        createdVariants={props.createdVariants}
                    />
                }
                {props.createdVariants.length >1 &&
                    <EachBasicVariant 
                        variant={propVariant}
                        setVariantName={setVariantName}
                        variantName={variantName}
                        changePriceHandler={changePriceHandler}
                        variantPrice={variantPrice}
                        setDateAvailable={setDateAvailable}
                        dateAvailable={dateAvailable}
                        setDateEnd={setDateEnd}
                        dateEnd={dateEnd}
                        activeStatus={activeStatus}
                        setActiveStatus={setActiveStatus}
                        removeVariantHandler={props.removeVariantHandler} 
                        productStatuses={props.productStatuses} 
                        setAddOnCategories={setAddOnCategories}
                        addOnCategories={addOnCategories}
                        jointAddOnCategories={jointAddOnCategories}
                        setJointAddOnCategories={setJointAddOnCategories}
                    />
                }
            </>
        }
    </div>
  )
}
