import { lazy } from 'react';

// this component tries to solve the problem of "chunk" loading errors after pushing a change to production
// hopefully this will catch that error and force reload the page instead of crashing the app

export const lazyWithRetry = (componentImport) =>
    lazy(async () => {
        //console.log('lazyWithRetry');

        const pageHasAlreadyBeenForceRefreshed = JSON.parse(
            window.localStorage.getItem(
                'page-has-been-force-refreshed'
            ) || 'false'
        );

        try {
            const component = await componentImport();

            window.localStorage.setItem(
                'page-has-been-force-refreshed',
                'false'
            );

            return component;
        } catch (error) {
            if (!pageHasAlreadyBeenForceRefreshed) {
                console.log('force refesh from lazyWithRetry');
                // Assuming that the user is not on the latest version of the application.
                // Let's refresh the page immediately.
                window.localStorage.setItem(
                    'page-has-been-force-refreshed',
                    'true'
                );
                return window.location.reload();
            }

            console.log('error from lazyWithRetry');
            // The page has already been reloaded
            // Assuming that user is already using the latest version of the application.
            // Let's let the application crash and raise the error.
            throw error;
        }
    });