@import '../../../assets/css/scss/variables';
@import '../../../assets/css/scss/themes.scss';

.archived-streams{
    .card{
        margin-top: 1rem;
        @media(max-width: 400px){
            padding: 10px;
        }
    }
    .main-body{
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        @media (max-width: 1350px){
            flex-direction: column;
        }
        @media (max-width: 500px){
            align-items: center;
        }
        .available-products{
            min-width: 300px;
            width: 400px;
            margin-right: 3rem;
            display: flex;
            flex-direction: column;
            .accordion{
                width: 100%;
            }
            @media (max-width: 1350px){
                flex-direction: row;
                flex-wrap: wrap;
                margin-right: 1rem;
                justify-content: center;
            }
            @media(max-width: 500px){
                justify-content: center;
                margin-top: .5rem;
                margin-bottom: 1.5rem;
            }
            .event-name{
                margin-right: 3rem;
                @media (max-width: 1700px){
                    margin-right: 1rem;
                    width: 250px;
                }
                @media (max-width: 500px){
                    text-align: center;
                }
            }
            .each-stream-event{
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                @media (max-width: 1700px){
                    flex-direction: column;
                }
                @media (max-width: 500px){
                    align-items: center;
                }
            }
            .stream-event-btn.btn{
                width: 125px;
                color: $primary-font-color;
                &:hover {
                    background-color: $secondary-hover-color;
                    color: $primary-inverse-color;
                }
            }
        }
        .current-list{
            display: flex;
            flex-direction: column;
            align-items: center;
            width: auto;
            .sub-title{
                @media(max-width: 400px){
                    text-align: center;
                }
            }
            .new-stream-btn.btn{
                width: 200px;
            }
            .video-player{
                display: flex;
                flex-direction: column;
                align-items: center;
                video{
                    margin: 1rem;
                    width: 800px;
                    height: auto;
                    @media (max-width: 1670px){
                        width: 600px;
                    }
                    @media(max-width: 765px){
                        width: 400px;
                    }
                    @media (max-width: 450px){
                        width: 320px;
                        margin: 1px;
                    }
                }
                button{
                    width: 200px;
                }
            }
        
            .schedule-list{
                margin-top: 2rem;
                height: 450px;
                overflow-y: scroll;
                margin-bottom: 1.5rem;
                @media (max-width: 750px){
                    height: 250px;
                }
            }
            .schedule-row{
                display: flex;
                flex-direction: column;
                border-bottom: 1px solid $grey-7;
                margin-bottom: .5rem;
                margin-right: 1rem;
                &:disabled{
                    opacity: .5;
                }
                p.main-row{
                    @media(max-width: 550px){
                        margin-bottom: 0;
                    }
                }
                .main-row{
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    @media (max-width: 550px){
                        flex-direction: column;
                    }
                    .name{
                        font-weight: 700;
                        width: 530px;
                        @media (max-width: 1670px){
                            width: 450px;
                        }
                        @media (max-width: 765px){
                            width: 250px;
                        }
                    }
                    .status{
                        width: 100px;
                    }
                    .date{
                        width: 100px;
                    }
                    .descriptor{
                        display: flex;
                        justify-content: center;
                    }
                }
                .stream-recording{
                    color: $active-sub;
                }
                .stream-unavailable{
                    color: $expired-sub;
                }
            }
        }
    }
}