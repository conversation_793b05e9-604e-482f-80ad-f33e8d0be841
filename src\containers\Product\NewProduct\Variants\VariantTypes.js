import React, {useState, useEffect, useRef} from 'react';
import DatePicker from 'react-datepicker'
import {But<PERSON>} from 'react-bootstrap';
import NumericInput from "react-numeric-input2";

import { AddOnsTypeahead } from '../../../../components/Typeahead/AddOnsTypeahead';
import FeatureTypeahead from '../../../../components/Typeahead/FeatureTypeahead';
import Tooltip from '../../../../components/common/Tooltip';
import usePrevious from '../../../../components/common/CustomHooks';

import './Variants.scss'

//All called in src\containers\Product\NewProduct\Variants\Variants.js

const basicToolTipSettings={
    direction:"top",
    width:"300px",
    height:"auto",
    mobile: {height: "auto", width: "150px", direction:"top"}
}
const toolTipIcon = <i className="far fa-question-circle"/>
const toolTipNameText = "Your variant names should be descriptive but brief.  They should add add on to the original product name.  Names of \"default\" will not show up on things like receipts."
const addOnText = "These categories are what affect which add ons are selectable in a register for a product, such as +onions, + pickles on a burger."
const measurementText = "Each unit of measurement can saved to the thousandths if need be.  If 'Is Shippable' is marked and true, all four dimensions are required for a product to be created.  If an item truly has a negligible dimension (like measuring a single sheet of paper), enter a decimal number for a unit of measurement."
const priceText = "Even if you want no price, you must enter one to prevent products accidentally being made without one.  If you want a no price product, include a 0."
const billIntervalText = "Here you can select if you want the billing to happen on a month to month basis or a yearly basis."
const intervalQuantityText = "This is how often a recurring subscription will be billed.  For example, if you selected month and 5, you would be billed every 5 months.  Selecting yearly allows you to select recur every year or not recur at all"
const billNumText = "This dictates the number of times a subscription bills, including the initial purchase.  For example, if you set this to three months, the customer will pay initially and be billed for two more months following."
const subscriptionTypeText="This is the type of subscription.  If, for example, you select 'Individual', that subscription will only ever apply to a single person."
const maxUsersText="This is the maximum number of users that will be able to use this same subscription"

export const EachBasicVariant=(props)=>{
    return (
        <div>
            <div className="each-basic-wrapper" data-cy="variant-type-each-basic">
                <div className="product-name-row">
                    <span className="product-flex-col" data-cy="variant-name">
                        <label htmlFor="variant-name">
                            Variant Name<span className="required">*</span>
                            <Tooltip 
                                text={toolTipNameText}
                                {...basicToolTipSettings}
                            >
                                {toolTipIcon}
                            </Tooltip>
                        </label>
                        <input 
                            required
                            name="variant-name" 
                            value={props.variantName || ""} 
                            onChange={(e)=>props.setVariantName(e.target.value)}
                            placeholder="Variant Name"
                        />
                    </span>
                    <span className="product-flex-col" data-cy="new-product-bd-add-on-categories">
                        <label htmlFor="product-addons-typeahead">Add Ons Category
                            {" "}
                            <Tooltip 
                                text={addOnText}
                                {...basicToolTipSettings}
                            >
                                {toolTipIcon}    
                            </Tooltip>
                        </label>
                        <AddOnsTypeahead 
                            name="product-addons-typeahead" 
                            data-cy="product-addons-typeahead" 
                            initialCategories={props?.addOnCategories} 
                            setParentAddOnCategories={(e)=>props.setAddOnCategories(e)}
                            singleImportAddon={props.jointAddOnCategories}
                            setSingleImportAddon={props.setJointAddOnCategories}
                        />
                    </span>
                </div>
                <div className="product-flex-row">
                    <span className="product-flex-col" data-cy="variant-description">
                        <label htmlFor="variant-price">
                            Price<span className="required">*</span>
                            {" "}
                            <Tooltip
                                text={priceText}
                                {...basicToolTipSettings}
                            >
                                {toolTipIcon}
                            </Tooltip>
                        </label>
                        <input 
                            required
                            name="variant-price"
                            type="number"
                            value={props?.variantPrice} 
                            onChange={(e)=>props.changePriceHandler(e)}
                            placeholder="Variant Price"
                        />
                    </span>
                    <span className="product-flex-col" data-cy="variant-status">
                        <label htmlFor="variant-status">Status</label>
                        <select value={parseInt(props.variant.product_status_id)} onChange={(e)=>props.setActiveStatus(parseInt(e.target.value))}>
                            {props?.productStatuses?.map((status)=>(
                                <option key={`status-dd-${status.id}`} value={status.id}>{status.name}</option>
                            ))}
                        </select>
                    </span>
                    <span className="product-flex-col" data-cy="variant-available-start">
                        <label htmlFor="variant-available-at">Available Starting</label>
                        <DatePicker
                            dateFormat="MM/dd/yyyy"
                            minDate={new Date("01/01/2021")}
                            showMonthDropdown
                            showYearDropdown
                            scrollableYearDropdown={10}
                            placeholderText="Available Start"
                            onChange={(e)=>props.setDateAvailable(e)}
                            selected={props.dateAvailable}
                        />
                    </span>
                    <span className="product-flex-col" data-cy="variant-available-end">
                        <label htmlFor="variant-end-at">Available Ending</label>
                        <DatePicker 
                            dateFormat={"MM/dd/yyyy"}
                            showMonthDropdown
                            showYearDropdown
                            scrollableYearDropdown={10}
                            minDate={new Date("01/01/1969")}
                            openToDate={new Date()}
                            placeholderText="Available End"
                            onChange={(e)=>props.setDateEnd(e)}
                            selected={props.dateEnd}
                            isClearable={true}
                        />
                    </span>
                </div>

                <span className="last-col" data-cy="variant-remove-btn">
                    <Button variant="danger" onClick={()=>props.removeVariantHandler(props.variant.temp_id)}>X</Button> 
                </span>
            </div>
        </div>
      )
};

export const SingleVariant=(props)=>{

    const handleMonthBillNumTimes=(e)=>{
        if(e) props.setBillNumTimes(e)
        else if(!e) props.setBillNumTimes(0) //bill_num_times being null means that it will continue to bill
    }

    const handleMaxUsers=(e)=>{
        if(e) props.setSubMaxUsers(e);
        else if(!e) props.setSubMaxUsers(1);
    }

    return (
        <div className="single-variant-wrapper" data-cy="variant-type-single-variant">
            <div className="product-flex-col" data-cy="variant-price">
                <label htmlFor="variant-price">Price<span className="required">*</span></label>
                <input 
                    required
                    type="number"
                    name="variant-price" 
                    value={props?.variantPrice} 
                    onChange={(e)=>props.changePriceHandler(e)}
                    placeholder="Variant Price"
                />
            </div>
            <div className="product-flex-col" data-cy="new-product-bd-add-on-categories">
                <label htmlFor="product-addons-typeahead">Add Ons Category
                    {" "}
                    <Tooltip 
                        text={addOnText}
                        {...basicToolTipSettings}
                    >
                        {toolTipIcon}    
                    </Tooltip>
                </label>
                <AddOnsTypeahead 
                    name="product-addons-typeahead" 
                    data-cy="product-addons-typeahead" 
                    initialCategories={props?.addOnCategories} 
                    setParentAddOnCategories={(e)=>props.setAddOnCategories(e)}
                    singleImportAddon={props.jointAddOnCategories}
                    setSingleImportAddon={props.setJointAddOnCategories}
                />
            </div>
            <div className="product-flex-col" data-cy="variant-available-start">
                <label htmlFor="variant-starting">Available Starting</label>
                <DatePicker 
                    name="variant-starting"
                    dateFormat={"MM/dd/yyyy"}
                    minDate={new Date("01/01/2021")}
                    showMonthDropdown
                    showYearDropdown
                    scrollableYearDropdown={100}
                    placeholderText="Available Start"
                    onChange={e=>props.setDateAvailable(e)}
                    selected={props.dateAvailable}
                />
            </div>
            <div className="product-flex-col" data-cy="variant-available-end">
                <label htmlFor="variant-ending">Available Ending</label>
                <DatePicker 
                    name="variant-ending"
                    dateFormat={"MM/dd/yyyy"}
                    minDate={new Date("01/01/1969")}
                    openToDate={new Date()}
                    showMonthDropdown
                    showYearDropdown
                    scrollableYearDropdown={100}
                    placeholderText="Available End"
                    onChange={e=>props.setDateEnd(e)}
                    selected={props.dateEnd}
                    isClearable={true}
                />
            </div>
            {props.selectedProductType===1 &&
                <>
                    <div className="product-flex-col" data-cy="varaint-bill-interval">
                        <label htmlFor="billing-interval">
                            Bill Interval<span className="required">*</span>
                            {" "}
                            <Tooltip 
                                text={billIntervalText}
                                {...basicToolTipSettings}
                            >
                                {toolTipIcon}
                            </Tooltip>
                        </label>
                        <select value={props.billInterval} onChange={(e)=>props.setBillInterval(e.target.value)}>
                            <option style={{display: "none"}}>Select an Interval</option>
                            {/* <option value="d">Daily</option> */}
                            <option value="m">Monthly</option>
                            <option value="y">Yearly</option>
                        </select>
                    </div>
                    {props.billInterval==="m" &&
                        <div className="product-flex-col" data-cy="variant-bill-interval-quantity">
                            <label htmlFor="interval-quantity-m">
                                Interval Quantity<span className="required">*</span>
                                {" "}
                                <Tooltip
                                    {...basicToolTipSettings}
                                    text={intervalQuantityText}
                                >
                                    {toolTipIcon}
                                </Tooltip>
                            </label>
                            <NumericInput 
                                name="interval-quantity-m"
                                disabled={props.billInterval==="y"}
                                value={props.intervalQuantity}
                                onChange={props.handleQuantity}
                                placeholder={"1"}
                                min={1}
                                max={11}
                                /* es-lint thinks this needs to be an object but it's done according to the docs for this component */
                                /* eslint-disable */
                                style={false}
                            />
                        </div>
                    }
                    {props.billInterval==="y" ?
                        <div className="product-flex-col" data-cy="variant-bill-interval-quantity">
                            <label htmlFor="interval-quantity-yr">
                                Bill Frequency
                                {" "}
                                <Tooltip 
                                    text={billNumText}
                                    {...basicToolTipSettings}
                                >
                                    {toolTipIcon}
                                </Tooltip>
                            </label>
                            <select
                                value={props.billNumTimes} 
                                name="interval-quantity-yr"
                                onChange={(e)=>props.setBillNumTimes(e.target.value)}
                             >
                                <option value={""}>Recurs Yearly</option>
                                <option value={1}>Bill One Time</option>
                             </select>
                        </div>
                        :
                        <div className="product-flex-col" data-cy="variant-bill-num-times">
                            <label htmlFor="bill-num-times">
                                Number of Times Billed
                                {" "}
                                <Tooltip 
                                    text={billNumText}
                                    {...basicToolTipSettings}
                                >
                                    {toolTipIcon}
                                </Tooltip>
                            </label>
                            <NumericInput 
                                value={props.billNumTimes}
                                onChange={handleMonthBillNumTimes}
                                placeholder={"Number of Times to Bill"}
                                style={false}
                            />
                        </div>
                    }
                    <div className="product-flex-col" data-cy="variant-activation-fee">
                        <label htmlFor="activation-fee">Activation Fee</label>
                        <input 
                            value={props?.activationFee || ""}
                            onChange={(e)=>props.setActivationFee(e.target.value)} 
                            placeholder="0"
                        />
                    </div>
                    <div className="product-flex-col" data-cy="variant-subscription-type">
                        <label htmlFor="subscription-type">
                            Subscription Type <span className="required">*</span>
                            {" "}
                            <Tooltip
                                text={subscriptionTypeText} 
                                {...basicToolTipSettings}
                            >
                                {toolTipIcon}
                            </Tooltip>
                        </label>
                        <select 
                            value={props.subscriptionTypeId}
                            onChange={(e)=>props.setSubscriptionTypeId(+e.target.value)}
                        >
                            {props.subTypes.map((type, i)=>(
                                <option key={i} value={type.id}>{type.name}</option>
                            ))}
                        </select>
                    </div>
                    {props.subscriptionTypeId !== 1 && //1 is individual membership, it will default to one in that case
                        <div className="product-flex-col" data-cy="variant-subscription-max-users">
                            <label htmlFor="subscription-max-users">
                                Max Users <span className="required">*</span>
                                {" "}
                                <Tooltip
                                    text={maxUsersText}
                                    {...basicToolTipSettings}
                                >
                                    {toolTipIcon}
                                </Tooltip>
                            </label>
                            <NumericInput 
                                value = {props.subMaxUsers}
                                onChange={handleMaxUsers}
                                placeholder={"Max Users"}
                                style={false}
                            />
                        </div>
                    }
                </>
            }
        </div>
      )
};

export const SubscriptionVariant=({billInterval, setIntervalQuantity, ...props})=>{

    const previousInterval=usePrevious(billInterval);

    useEffect(()=>{
        if(previousInterval==="y" && (billInterval==="m" || billInterval==="d")) setIntervalQuantity("")
    // previousInterval will be changed anytime the interval is changed, don't need another render to check it too
    // eslint-disable-next-line react-hooks/exhaustive-deps 
    },[billInterval, setIntervalQuantity]);

    const handleQuantity=(e)=>{
        if(e === null || e === 0 ) setIntervalQuantity(1);
        else if (parseInt(e) > 11) setIntervalQuantity(11);
        else setIntervalQuantity(parseInt(e))
    }

    return (
        <div data-cy="variant-type-subscription-variant">
            {props.createdVariants.length > 1 &&
                <div className="subscription-var-wrapper">
                    <div className="product-flex-row">
                        <span className="product-flex-col" data-cy="variant-status">
                            <label htmlFor="variant-status">Status</label>
                            <select value={parseInt(props.variant.product_status_id)} onChange={(e)=>props.setActiveStatus(parseInt(e.target.value))}>
                                {props?.productStatuses?.map((status)=>(
                                    <option key={`status-dd-${status.id}`} value={status.id}>{status.name}</option>
                                ))}
                            </select>
                        </span>
                        <span className="product-flex-col" data-cy="variant-name">
                            <label htmlFor="variant-name">
                                Variant Name<span className="required">*</span>
                                {" "}
                                <Tooltip 
                                    text={toolTipNameText}
                                    {...basicToolTipSettings}
                                >
                                    {toolTipIcon}
                                </Tooltip>
                            </label>
                            <input 
                                required 
                                name="variant-name" 
                                onChange={(e)=>props.setVariantName(e.target.value)}
                                value={props.variantName || ""}
                                placeholder="Variant Name"
                            />
                        </span>
                    </div>
                    <div className="last-col" data-cy="variant-remove-btn">
                        <Button variant="danger" onClick={()=>props.removeVariantHandler(props.variant.temp_id)}>X</Button> 
                    </div>
                </div>
            }
            <SingleVariant
                variant={props.variant}
                changePriceHandler={props.changePriceHandler} 
                variantPrice={props.variantPrice}
                setDateAvailable={props.setDateAvailable}
                dateAvailable={props.dateAvailable}
                setDateEnd={props.setDateEnd}
                dateEnd={props.dateEnd}   
                selectedProductType={props.selectedProductType}
                setBillInterval={props.setBillInterval}
                billInterval={billInterval}
                setIntervalQuantity={setIntervalQuantity}
                intervalQuantity={props.intervalQuantity}
                billNumTimes={props.billNumTimes}
                setBillNumTimes={props.setBillNumTimes}
                activationFee={props.activationFee}
                setActivationFee={props.setActivationFee}
                addOnCategories={props.addOnCategories}
                setAddOnCategories={props.setAddOnCategories}
                jointAddOnCategories={props.jointAddOnCategories}
                setJointAddOnCategories={props.setJointAddOnCategories}
                handleQuantity={handleQuantity}
                subscriptionTypeId={props.subscriptionTypeId}
                setSubscriptionTypeId={props.setSubscriptionTypeId}
                subMaxUsers={props.subMaxUsers}
                setSubMaxUsers={props.setSubMaxUsers}
                subTypes={props.subTypes}
            />
            {billInterval ==="m" && !isNaN(props.intervalQuantity) &&
                <p className="text-center">
                    This subscription will be billed every {props.intervalQuantity} month{props.intervalQuantity > 1 ? "s" : ""}. 
                    {props.billNumTimes===1 ? `This subscription will be billed ${props.billNumTimes} time${props.billNumTimes > 1 ? "s" : ""} total, including the initial purchase.` : "This subscription will continue until cancelled."}
                </p>
            }
            {billInterval === "y" &&
                <p className="text-center">
                    {props.billNumTimes ? `This subscription will be billed only on purchase and will not recur.` : "This subscription will be billed every year."}
                </p>
            }
        </div>
    )
};

export const TokenVariants=(props)=>{
    return(
        <div className="token-variant-wrapper" data-cy="variant-type-token-variant">
            <span className="product-flex-col" data-cy="variant-price">
                <label htmlFor="variant-price">Price<span className="required">*</span></label>
                <input 
                    required
                    name="variant-price" 
                    value={props?.variantPrice} 
                    onChange={(e)=>props.changePriceHandler(e)}
                    placeholder="Variant Price"
                />
            </span>
            <span className="product-flex-col" data-cy="variant-available-start">
                <label htmlFor="date-available">Available Starting</label>
                <DatePicker 
                    name="date-available"
                    dateFormat="MM/dd/yyy"
                    minDate={new Date("01/01/2021")}
                    showMonthDropdown
                    showYearDropdown
                    scrollableYearDropdown={10}
                    placeholderText="Available Start"
                    onChange={(date)=>props.setDateAvailable(date)}
                />
            </span>
            <span className="product-flex-col" data-cy="variant-expires-in">
                <label htmlFor="variant-expires">Expires In</label>
                <input 
                    name="variant-expires"
                    value={props.expiresIn}
                    onChange={(e)=>props.setExpiresIn(e.target.value)}
                    placeholder="0"
                />
            </span>
            <span className="product-flex-col" data-cy="new-product-bd-add-on-categories">
                <label htmlFor="product-addons-typeahead">Add Ons Category
                    {" "}
                    <Tooltip 
                        text={addOnText}
                        {...basicToolTipSettings}
                    >
                        {toolTipIcon}    
                    </Tooltip>
                </label>
                <AddOnsTypeahead 
                    name="product-addons-typeahead" 
                    data-cy="product-addons-typeahead" 
                    initialCategories={props?.addOnCategories} 
                    setParentAddOnCategories={(e)=>props.setAddOnCategories(e)}
                    singleImportAddon={props.jointAddOnCategories}
                    setSingleImportAddon={props.setJointAddOnCategories}
                />
            </span>
        </div>
    )
};

export const ItemizedVariants=(props)=>{

    return(
        <div className="itemized-var-wrapper" data-cy="variant-type-itemized-variants">
            <div className="product-flex-row">
                {props.createdVariants.length>1 &&
                    <span className="product-flex-col" data-cy="variant-name">
                        <label htmlFor="variant-name">
                            Variant Name<span className="required">*</span>
                            {" "}
                            <Tooltip 
                                text={toolTipNameText}
                                {...basicToolTipSettings}
                                >
                                {toolTipIcon}
                            </Tooltip>
                        </label>
                        <input 
                            required
                            name="variant-name" 
                            value={props.variantName}
                            onChange={(e)=>props.setVariantName(e.target.value)}
                            placeholder="Variant Name"
                        />
                    </span>
                }
            </div>
            <div className="product-flex-row">
                <span className="product-flex-col" data-cy="new-product-bd-add-on-categories">
                    <label htmlFor="product-addons-typeahead">Add Ons Category
                        {" "}
                        <Tooltip 
                            text={addOnText}
                            {...basicToolTipSettings}
                        >
                            {toolTipIcon}    
                        </Tooltip>
                    </label>
                    <AddOnsTypeahead 
                        name="product-addons-typeahead" 
                        data-cy="product-addons-typeahead" 
                        initialCategories={props?.addOnCategories} 
                        setParentAddOnCategories={(e)=>props.setAddOnCategories(e)}
                        singleImportAddon={props.jointAddOnCategories}
                        setSingleImportAddon={props.setJointAddOnCategories}
                    />
                </span>
                <span className="product-flex-col" data-cy="variant-price">
                    <label htmlFor="variant-price">Price<span className="required">*</span></label>
                    <input 
                        required
                        name="variant-price" 
                        value={props?.variantPrice} 
                        onChange={(e)=>props.changePriceHandler(e)}
                        placeholder="0"
                    />
                </span>
                <span className="product-flex-col" data-cy="variant-available-start">
                    <label htmlFor="date-available">Available Starting</label>
                    <DatePicker
                        name="date-available"
                        dateFormat="MM/dd/yyyy"
                        minDate={new Date("01/01/2021")}
                        showMonthDropdown
                        showYearDropdown
                        scrollableYearDropdown={10}
                        placeholderText="Available Start"
                        onChange={(date)=>props.setDateAvailable(date)}
                        selected={props.dateAvailable}
                    />
                </span>
                <span className="product-flex-col" data-cy="variant-available-end">
                    <label htmlFor="available-end">Available Ending</label>
                    <DatePicker 
                        name="available-endings"
                        dateFormat="MM/dd/yyyy"
                        minDate={new Date("01/01/1969")}
                        openToDate={new Date()}
                        showMonthDropdown
                        showYearDropdown
                        scrollableYearDropdown={10}
                        placeholderText="Available End"
                        onChange={(e)=>props.setDateEnd(e)}
                        selected={props.dateEnd}
                        isClearable={true}
                    />
                </span>
                {props.createdVariants.length > 1 && 
                    <span className="product-flex-col" data-cy="variant-status">
                        <label htmlFor="variant-status">Status</label>
                        <select value={parseInt(props.variant.product_status_id)} onChange={(e)=>props.setActiveStatus(parseInt(e.target.value))}>
                            {props?.productStatuses?.map((status)=>(
                                <option key={`status-dd-${status.id}`} value={status.id}>{status.name}</option>
                            ))}
                        </select>
                    </span>
                }
            </div>

            {props.createdVariants.length > 1 &&
                <span className="last-col"data-cy="variant-remove-btn">
                    <Button variant="danger" onClick={()=>props.removeVariantHandler(props.variant.temp_id)}>X</Button>
                </span>
            }
        </div>
    )
};

export const MeasurementAndSKUExtension=({setIsShippable, setShowMeasurementOptions, showMeasurementOptions, ...props})=>{

    const [showSkuDetails, setShowSkuDetails]=useState(props.defaultShowSKUs);

    useEffect(()=>{
        if(props.UPC || props.SKU) setShowSkuDetails(true);
        if(props.weight || props.height || props.width || props.variantLength) setShowMeasurementOptions(true);
    },[props, setShowMeasurementOptions]);

    return(
        <div className="measure-sku-wrapper" data-cy="variant-type-measurement-sku-variant">
            <div className="product-flex-row">
                <div className="product-flex-col">
                    <span className="checkmark-top-row" data-cy="variant-measurement-details-check">
                        <label htmlFor="measurement-input">
                            Measurement Details?
                            {" "}
                            <Tooltip 
                                text={measurementText}
                                {...basicToolTipSettings}
                            >
                                {toolTipIcon}
                            </Tooltip>
                        </label>
                        <input 
                            type="checkbox"
                            name="measurement-input"
                            value={showMeasurementOptions}
                            checked={showMeasurementOptions}
                            onChange={()=>setShowMeasurementOptions(!showMeasurementOptions)}
                        />
                    </span>
                    <span data-cy="variant-sku-upc-check">
                        <label htmlFor="measurement-input">SKU/UPC?</label>
                        <input 
                            type="checkbox"
                            name="measurement-input"
                            checked={showSkuDetails}
                            onChange={()=>setShowSkuDetails(!showSkuDetails)}
                        />
                    </span>
                    {showMeasurementOptions &&
                        <span data-cy="variant-shippable">
                            <label htmlFor="is-shippable">Is Shippable?</label>
                            <input 
                                type="checkbox"
                                name="is-shippable"
                                value={props.isShippable}
                                checked={props.isShippable}
                                onChange={(e)=>setIsShippable(!props.isShippable)}
                            />
                        </span>
                    }
                </div>
                {showSkuDetails &&
                    <SkuAndUpc 
                        setUPC={props.setUPC}
                        UPC={props.UPC}
                        setSKU={props.setSKU}
                        SKU={props.SKU}
                    />
                }  
                {showMeasurementOptions && 
                    <MeasurementDetails 
                        weight={props.weight}
                        setWeight={props.setWeight}
                        height={props.height}
                        setHeight={props.setHeight}
                        variantLength={props.variantLength}
                        setLength={props.setLength}
                        width={props.width}
                        setWidth={props.setWidth}
                        isShippable={props.isShippable}
                    />
                }
            </div>
        </div>
    )
};

export const SkuAndUpc=(props)=>{
    return(
        <div className="sku-col" data-cy="variant-type-sku-upc">
            <span className="product-flex-col" data-cy="variant-sku">
                <label htmlFor="SKU-input">SKU</label>
                <input 
                    name="SKU-input" 
                    className="sku-input"
                    value={props.SKU}
                    onChange={(e)=>props.setSKU(e.target.value)}
                    placeholder="XXXXXXX"
                />
            </span>
            <span className="product-flex-col" data-cy="variant-upc">
                <label htmlFor="UPC-input">UPC</label>
                <input 
                    name="UPC-input" 
                    className="sku-input"
                    value={props.UPC}
                    onChange={(e)=>props.setUPC(e.target.value)}
                    placeholder="00000-00000"
                />
            </span>
        </div>
    )
};

export const MeasurementDetails=({setWeight, setHeight, setWidth, setLength, weight, height, width, variantLength, ...props})=>{

    const mountedRef = useRef(false);

    const weightMeasurements=['oz', 'lbs', 'g', 'kg',];
    const lengthHeightMeasurements=['in', 'cm', 'ft'];

    const [selectedWeightUnit, setSelectedWeightUnit]=useState('oz');
    const [selectedHeightUnit, setSelectedHeightUnit]=useState('in');
    const [selectedWidthUnit, setSelectedWidthUnit]=useState('in');
    const [selectedLengthUnit, setSelectedLengthUnit]=useState('in');
    //temp units are used for units that are converted to a desired measurement.
    const [tempWeight, setTempWeight]=useState(weight ? parseInt(weight).toFixed(3) : "");
    const [tempWidth, setTempWidth]=useState(width ? parseInt(width).toFixed(3) : "");
    const [tempHeight, setTempHeight]=useState(height ? parseInt(height).toFixed(3) : "");
    const [tempLength, setTempLength]=useState(variantLength ? parseInt(variantLength).toFixed(3) : "");

    useEffect(()=>{
        mountedRef.current=true;

        return()=> mountedRef.current=false;
    },[]);

    //if for some reason the props aren't loaded in time, useEffects to catch the props
    useEffect(()=>{
        if(height && tempHeight ==="") setTempHeight(parseInt(height).toFixed(3));
    //there's no reason to include the temp value because we don't want it triggering when it changes.  Only when the prop changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[height]);

    useEffect(()=>{
        if(weight && tempWeight==="") setTempWeight(parseInt(weight).toFixed(3))
    //there's no reason to include the temp value because we don't want it triggering when it changes.  Only when the prop changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[weight])

    useEffect(()=>{
        if(variantLength && tempLength === "") setTempLength(parseInt(variantLength).toFixed(3))
    //there's no reason to include the temp value because we don't want it triggering when it changes.  Only when the prop changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[variantLength])

    useEffect(()=>{
        if(width && tempWidth==="") setTempWidth(parseInt(width).toFixed(3));
    //there's no reason to include the temp value because we don't want it triggering when it changes.  Only when the prop changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[width])

    //until we can change units of measurement - what you enter is what you add
    useEffect(()=>{
        if(tempWeight) setWeight(tempWeight);
        if(tempWidth) setWidth(tempWidth);
        if(tempHeight) setHeight(tempHeight);
        if(tempLength) setLength(tempLength);
    },[tempWeight, tempWidth, tempHeight, tempLength, setWeight, setWidth, setHeight, setLength]);

    // //don't need to check for the unit because there will always be at least a default one in the beginning.
    // useEffect(()=>{
    //     if(tempWeight && mountedRef.current) {
    //        let ounceWeight=convertWeightMeasurementToOunce(selectedWeightUnit, tempWeight)
    //        setWeight(+ounceWeight);
    //     };
    // }, [tempWeight, selectedWeightUnit, setWeight]);


    // useEffect(()=>{
    //     if(tempHeight && mountedRef.current){
    //         let tempH=convertHeightMeasurementToCm(selectedHeightUnit, tempHeight);
    //         setHeight(+tempH);
    //     }
    //     if(tempWidth && mountedRef.current){
    //         let tempW=convertHeightMeasurementToCm(selectedWidthUnit, tempWidth);
    //         setWidth(+tempW);
    //     }
    //     if(tempLength && mountedRef.current){
    //         let tempL=convertHeightMeasurementToCm(selectedLengthUnit, tempLength);
    //         setLength(+tempL);
    //     }
    // },[tempHeight, selectedHeightUnit, tempWidth, selectedWidthUnit, tempLength, selectedLengthUnit, setHeight, setLength, setWidth]);

     //convert everything to the same unit of measurement for the backend (oz)
     const convertWeightMeasurementToOunce=(measurement, value)=>{
        let totalOunces;
        switch(measurement){
            case "lbs":
                totalOunces=(+value*16).toFixed(3);
                break;
            case "g":
                totalOunces=(+value/28.36).toFixed(3);
                break;
            case "kg":
                totalOunces=(+value*35.274).toFixed(3);
                break;
            default:
                //default is ounce input
                totalOunces=+value;
                break;
        }
        return totalOunces;
    };

    const convertHeightMeasurementToCm=(measurement, value)=>{
        let totalCm;
        switch(measurement){
            case "ft":
                totalCm=(+value*30.48).toFixed(3);
                break;
            case "in":
                totalCm=(+value*2.54).toFixed(3);
                break;
            default:
                totalCm=+value;
        }
        return totalCm;
    }
    
    return(
        <div className="measurement-details-wrapper" data-cy="variant-type-meausurement-variants">
            <div className="product-flex-col">
                <span className="product-flex-col" data-cy="variant-weight">
                    <label htmlFor="ship-weight">
                        Weight
                        {props.isShippable ? <span className="required">*</span> : ""}
                    </label>
                    <div className="product-flex-row">
                        <select 
                            disabled
                            className="measure-select"
                            defaultValue={selectedWeightUnit}
                            onChange={(e)=>setSelectedWeightUnit(e.target.value)}
                        >
                            {weightMeasurements?.map((option, i)=>(
                                <option key={`measurement-w-dd-${i}`} value={option}>{option}</option>
                            ))}
                        </select>
                        <input 
                            name="ship-weight" 
                            className="measure-value"
                            placeholder={0}
                            value={tempWeight}
                            onChange={(e)=>setTempWeight(e.target.value)}
                        />
                    </div>
                </span>
                <span className="product-flex-col" data-cy="variant-height">
                    <label htmlFor="ship-height">
                        Height
                        {props.isShippable ? <span className="required">*</span> : ""}    
                    </label>
                    <div className="product-flex-row">
                        <select 
                            disabled
                            className="measure-select"
                            defaultValue={selectedHeightUnit}
                            onChange={(e)=>setSelectedHeightUnit(e.target.value)}
                        >
                            {lengthHeightMeasurements.map((option, i)=>(
                                <option key={`measurement-lh-dd-${i}`} value={option}>{option}</option>
                            ))}
                        </select>
                        <input 
                            name="ship-Height" 
                            className="measure-value"
                            placeholder={0}
                            value={tempHeight}
                            onChange={(e)=>setTempHeight(e.target.value)}
                        />
                    </div>
                </span>
            </div>
            <div className="product-flex-col">
                <span className="product-flex-col" data-cy="variant-length">
                    <label htmlFor="ship-length">
                        Length
                        {props.isShippable ? <span className="required">*</span> : ""}    
                    </label>
                    <div className="product-flex-row">
                        <select 
                            disabled
                            className="measure-select"
                            defaultValue={selectedLengthUnit}
                            onChange={(e)=>setSelectedLengthUnit(e.target.value)}
                        >
                            {lengthHeightMeasurements.map((option, i)=>(
                                <option key={`measurement-lh-dd-${i}`} value={option}>{option}</option>
                            ))}
                        </select>
                        <input 
                            name="ship-length" 
                            className="measure-value"
                            placeholder={0}
                            value={tempLength}
                            onChange={(e)=>setTempLength(e.target.value)}
                        />
                    </div>
                </span>
                <span className="product-flex-col" data-cy="variant-width">
                    <label htmlFor="ship-width">
                        Width
                        {props.isShippable ? <span className="required">*</span> : ""}    
                    </label>
                    <div className="product-flex-row">
                        <select 
                            disabled
                            className="measure-select"
                            defaultValue={selectedWidthUnit}
                            onChange={(e)=>setSelectedWidthUnit(e.target.value)}
                        >
                            {lengthHeightMeasurements.map((option, i)=>(
                                <option key={`measurement-lh-dd-${i}`} value={option}>{option}</option>
                            ))}
                        </select>
                        <input 
                            name="ship-width" 
                            className="measure-value"
                            placeholder={0}
                            value={tempWidth}
                            onChange={(e)=>setTempWidth(e.target.value)}
                        />
                    </div>
                </span>
            </div>
        </div>
    )
};

export const AddAVariant=(props)=>{
    return(
        <Button onClick={props.newVariantClickHandler} data-cy="add-variant-btn">Add a Variant</Button>
    )
};

export const FeatureVariant=({variant, importedType, selectedFeatures, setSelectedFeatures, setIncludeNew, ...props})=>{

    const [ checked, setChecked]=useState(true);

    useEffect(()=>{
        setIncludeNew(checked)
    },[checked])

    return(
        <div className="feature-variant-wrapper">
            <div className="feature-typeahead">
                <label>{importedType===11 ? "Additional" : "" } Linked Features</label>
                <FeatureTypeahead 
                    initialDataIds={selectedFeatures?.length > 0 ? selectedFeatures?.map((variant)=>variant.id) : null}
                    multiple={true}
                    passSelection={(selection)=>setSelectedFeatures(selection)}
                />
            </div>
            {importedType===11 &&
                <div className="include-check">
                    <label htmlFor="include-new">Include New Feature Too?</label>
                    <input type="checkbox" name="include-new" checked={checked} onChange={(e)=>setChecked(e.target.checked)} />
                </div>
            }
        </div>
    )
}
