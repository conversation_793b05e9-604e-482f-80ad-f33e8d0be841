import React, {useState, useEffect, useCallback, useRef} from 'react';
import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import { format } from 'date-fns';

import ErrorCatcher from '../../../../components/common/ErrorCatcher';
import Products from '../../../../api/Products';
import Locations from '../../../../api/Locations';
import Services from '../../../../api/Services';

import './DisplayDetails.scss'
import { ProductImage } from '../../../POS/Items/ProductImage/ProductImage';

export const DisplayDetails = ({product, importedBtn, associatedServices, propLoading, ...props}) => {

    const WEIGHT_UNIT="oz"
    const MEASURE_UNIT="in"

    const mountedRef=useRef(false);
    const [loading, setLoading]=useState(true);
    const [printersDone, setPrintersDone]=useState(false);
    const [error, setError]=useState();
    const [variants, setVariants]=useState([]);
    const [productStatus, setProductStatus]=useState("");
    const [statuses, setAllStatus]=useState([]);

    const getVariant=useCallback(async()=>{
        let tempVariants = []
        for(let i=0; i<product?.product_variants?.length; i++){
                let id = product.product_variants[i].id
                try{
                    let response = await Products.Variants.get({id: id});
                    if(!response.error && mountedRef.current){
                        let hasVariant = variants.some(variant=>{
                            return variant.id === response.data[0].id})
                        if(!hasVariant)  tempVariants.push(response.data[0])
                    }else if(response.error){
                        setError(<ErrorCatcher error={response.errors} />)
                    }
                }catch(ex){
                    console.error(ex)
                }
            }
        setVariants(tempVariants);
    },[variants, product]);

    const getProductStatuses=useCallback(async()=>{
        try{
            let response = await Products.Status.get()
            if(!response.errors && response.data && mountedRef.current){
                setAllStatus(response.data)
                setProductStatus(response.data.filter((status)=>status.id===product.product_status_id))
            }else if(response.errors) setError(response.errors)
        }catch(ex){console.error(ex)}
    },[product]);

    const getPrinterNames=useCallback(async()=>{
        let printLocations = []
        for(let i = 0; i<product.print_locations?.length; i++){
            if(!product.print_locations[i].hasOwnProperty('name')){
                try{
                    let response = await Locations.get({id:product.print_locations[i]})
                    if(!response.errors && mountedRef.current){
                        printLocations.push(response.data[0]);
                    } else if(response.errors && mountedRef.current){
                        setError(<ErrorCatcher error={response.errors} />)
                    }
                }catch(ex){
                    console.error(ex)
                }
            }
        }
        if(printLocations.length>0) {
            product.print_locations = printLocations
            setPrintersDone(true);
        }
    },[product])

    useEffect(()=>{
        mountedRef.current = true;

        return ()=> mountedRef.current = false;
    },[]);

    useEffect(()=>{
        if(product){
            if(product.print_locations?.length>0) getPrinterNames();
            if(product.product_variants?.length===0) setLoading(false);
            getProductStatuses()
        }
    },[product, getPrinterNames, getProductStatuses])

    useEffect(()=>{
        if(product && product?.product_variants?.length > 0 && variants.length === 0){
            getVariant()
        }
    },[getVariant, product, variants]);

    useEffect(()=>{
        //variants **should** never be 0 if they were made properly - they should always at least have the variant "default"
        if(product && product?.product_variants?.length > 0 && variants?.length > 0 && product.product_variants?.length === variants.length){
            if(product.print_locations.length === 0 || !product.print_locations) setLoading(false);
            if(product.print_locations.length > 0 && printersDone) setLoading(false)
        }
    },[product, variants, printersDone]);

    if(loading){
        return(
            <SkeletonTheme color="#e0e0e0">
                <div className="mt-3 text-center">
                    <Skeleton height={28} width={200}/>
                    <Skeleton height={16} count={4} />
                </div>
            </SkeletonTheme>    
        )
    }

  return (
    <div className="product-display-wrapper">
        <div className="display-product-heading">
            {error}
            <h2 data-cy="product-name">{product.name}</h2>
            <h4>Product Details</h4>
            {importedBtn}
        </div>
        <div>
            <ProductImage 
                imgSrc={product?.media?.length ? product.media[product.media.length - 1]?.path : ""}
                aligment="center"
                size="medium"
            />
        </div>
        <div className="prod-display-basic-details">
            <div className="property-group">
                <div className="property" data-cy="product-type">
                    <span>
                        Product Type: 
                    </span>
                    <span>
                        {product?.product_type_name}
                    </span>
                </div>
                <div className="property" data-cy="product-status">
                    <span>
                        Product Status:
                    </span> 
                    <span>
                        {productStatus[0]?.name}
                    </span>
                </div>
            </div>
            <div className="property-group" data-cy="product-taxable">
                <div className="property">
                    <span>
                        Taxable: 
                    </span>
                    <span>
                        {product.is_taxable===1 ? "Yes" : "No"} 
                    </span>
                </div>
                {product?.date_available ? 
                    <div className="property" data-cy="product-available">
                        <span>
                            Date Available:
                        </span>
                        <span>
                            {format(new Date(product?.date_available), "MM/dd/yyy")}
                        </span>
                    </div>
                    :
                    ""
                }
            </div>
            <div className="property description" data-cy="product-description">
                <span>
                    Description:
                </span>
                <span>
                    {product?.description}
                </span>
            </div>
            <div className="property categories" data-cy="product-printers">
                <span>
                    Printer Locations:
                </span>
                {product?.print_locations.map(location=>(
                    <span key={`display-printer-location-${location.id}`} className="cat-name">
                        {location?.name}
                    </span>
                ))}
            </div>
                {product?.bundled_products?.length >0 &&
                    <div className="property-group" data-cy="product-bundles">
                        <div className="property">
                            <span>Bundled Product:</span>
                            <span className="cp" onClick={()=>window.location.replace(`/p/products/${product?.bundled_products[0]?.id}`)}>
                                {product.bundled_products[0].name}
                            </span>
                        </div>
                        <div className="property">
                            <span>Bundle Quantity:</span>
                            <span>{product.bundled_products[0].quantity}</span>
                        </div>
                    </div>
                }
            <div className="property categories" data-cy="product-categories">
                <span>
                    Categories:
                </span>
                {product?.categories.map(category =>(
                    <span key={`product-category-${category.id}`} className="cat-name">
                        {category.name}
                    </span>
                ))}
            </div>
            {product?.product_type_id === 9 &&
                <div className="property" data-cy="product-services">
                    <span>
                        Associated Services:
                    </span>
                    {associatedServices.length > 0 && associatedServices?.map((service, i)=>(
                            <span key={`product-services-${service.id}`}>
                                {service.name}{i < associatedServices.length-1 && <span>, {" "}</span>}
                            </span>
                        ))
                    }
                    {associatedServices.length === 0 && 
                        <span className="error-text">
                            No associated services!
                        </span>
                    }
                </div>
            }
        </div>
        <div className="display-product-heading">
            <h4>Variants</h4>
        </div>
        <div className="prod-display-variant-details">
            {variants?.map(variant=>(
                <div key={`each-variant-${variant.id}`} className="each-wrapper box-shadow-2" data-cy={`variant-${variant.id}`}>
                    <h4 data-cy="variant-name">{variant.name} - ${variant.price}</h4>
                    <div className="prod-display-each-variant">
                        {variant.product_status_id &&
                            <div className="property" data-cy="variant-status">
                                <span>Variant Status</span>
                                    {statuses?.filter(stat=>(stat.id === variant.product_status_id)).map(stat=>(
                                        <span key={`status-dd-${stat.id}`}>{stat.name}</span>
                                    ))} 
                            </div>
                        }
                        {variant.activation_fee && 
                            <div className="property" data-cy="variant-activation-fee">
                                <span>Activation Fee</span>
                                <span>{variant.activation_fee}</span>
                            </div>
                        }
                        {variant.bill_interval && 
                            <div className="property" data-cy="variant-bill-interval">
                                <span>Bill Interval</span>
                                <span>{variant.bill_interval}</span>
                            </div>
                        }
                        {variant.date_available &&
                            <div className="property" data-cy="variant-date-available">
                                <span>Date Available</span>
                                <span>{format (new Date(variant.date_available), "MM/dd/yyy")}</span>
                            </div>
                        }
                        {variant.date_available_until &&
                            <div className="property" data-cy="variant-date-available-until">
                                <span>Date Avilable Until</span>
                                <span>{format(new Date(variant.date_available_until), "MM/dd/yyyy")}</span>
                            </div>
                        }
                        {variant.expires_in_days !==null && 
                            <div className="property" data-cy="variant-expires-in">
                                <span>Expires In</span>
                                <span>{variant.expires_in_days} days</span>
                            </div>
                        }
                        {variant.fullfillment_fee &&
                            <div className="property" data-cy="variant-fullfillment-fee">
                                <span>Fullfillment Fee</span>
                                <span>{variant.fullfillmnent_fee}</span>
                            </div>
                        }
                        {variant.handling_fee &&
                            <div className="property" data-cy="variant-handling-fee">
                                <span>Handling Fee</span>
                                <span>{variant.handling_fee}</span>
                            </div>
                        }
                        {variant.interval_quantity &&
                            <div className="property" data-cy="variant-interval-quantity">
                                <span>Interval Quantity</span>
                                <span>{variant.interval_quantity}</span>
                            </div>
                        }
                        {variant.bill_num_times && variant.bill_num_times!==0 && variant.bill_interval==="m" && 
                            <div className="property" data-cy="variant-m-bill-num-times">
                                <span>Number of Billings</span>
                                <span>{variant.bill_num_times}</span>
                            </div>
                        }
                        {variant.bill_interval==="y" && variant.bill_num_times &&
                            <div className="property" data-cy="variant-year-bill-num-times">
                                <span>Bill Interval</span>
                                <span>{variant.bill_num_times ? `${variant.bill_num_times}`: "" }</span>
                            </div>
                        }
                        {variant.subscription_type_id &&
                            <div className="property" data-cy="variant-subscription-type">
                                <span>Subscription Type</span>
                                <span>{variant.subscription_type_name}</span>
                            </div>
                        }
                        {variant.subscription_max_users && variant.subscription_max_users > 1 &&
                            <div className="property" data-cy="variant-subscription-max-users">
                                <span>Max Subscription Users</span>
                                <span>{variant.subscription_max_users}</span>
                            </div>
                        }
                        {
                        product.product_type_id !==9 && //not on tokens
                        product.product_type_id !== 3 && //not on digital
                        product.product_type_id !== 1 &&  //not on subscriptions
                        product.product_type_id !== 10 && //not on cancellation fees
                        product.product_type_id !== 5 && //not on class
                            <div className="property" data-cy="variant-shippable">
                                <span>Shippable?</span>
                                <span>{variant.is_shippable === 1 ? "Yes" : "No"}</span>
                            </div>
                        }
                        {variant.minimum_quantity &&
                            <div className="property" data-cy="variant-minimum-quantity">
                                <span>Minimum Quantity</span>
                                <span>{variant.minimum_quantity}</span>
                            </div>
                        }
                        {variant.quantity_units &&
                            <div className="property" data-cy="variant-quantity-units">
                                <span>Quantity Units</span>
                                <span>{variant.quantity_units}</span>
                            </div>
                        }
                        {variant.sale_price &&
                            <div className="property" data-cy="variant-sale-price">
                                <span>Sale Price</span>
                                <span>{variant.sale_price}</span>
                            </div>
                        }
                        {variant.token_price && 
                            <div className="property" data-cy="variant-token-price">
                                <span>Token Price</span>
                                <span>{variant.token_price}</span>
                            </div>
                        }
                        {variant.sku &&
                            <div className="property" data-cy="variant-sku">
                                <span>SKU</span>
                                <span>{variant.sku}</span>
                            </div>
                        }
                        {variant.upc &&
                            <div className="property" data-cy="variant-upc">
                                <span>UPC</span>
                                <span>{variant.upc}</span>
                            </div>
                        }
                        {variant.height &&
                            <div className="property" data-cy="variant-height">
                                <span>Height {MEASURE_UNIT}</span>
                                <span>{parseInt(variant.height)?.toFixed(3)} {MEASURE_UNIT}</span>
                            </div>
                        }
                        {variant["length"] &&
                            <div className="property" data-cy="variant-length">
                                <span>Length</span>
                                <span>{parseInt(variant["length"])?.toFixed(3)} {MEASURE_UNIT}</span>
                            </div>
                        }
                        {variant.width &&
                            <div className="property" data-cy="variant-width">
                                <span>Width</span>
                                <span>{parseInt(variant.width)?.toFixed(3)} {MEASURE_UNIT}</span>
                            </div>
                        }
                        {variant.weight &&
                            <div className="property" data-cy="variant-weight">
                                <span>Weight</span>
                                <span>{parseInt(variant.weight)?.toFixed(3)} {WEIGHT_UNIT}</span>
                            </div>
                        }
                        {variant.add_on_categories.length > 0 &&
                            <div className="property" data-cy="variant-addons">
                                <span>AddOns</span>
                                {variant.add_on_categories?.map((addon, i)=>(
                                    <span key={`add-on-category-${addon}-${i}`}>{addon.name}</span>
                                ))}
                            </div>
                        }
                        {product.product_type_id === 11 && variant.features.length > 0 &&
                            <div className="property" data-cy="variant-features">
                                <span>Features</span>
                                {variant.features.map((feature)=>(
                                    <span key={`feature-display-${feature.id}`}>{feature.name}</span>
                                ))}
                            </div>
                        }
                    </div>
                    <div className="text-center">
                        <p>
                            {product.product_type_id ===1 &&
                                <>
                                    {variant.bill_interval==="m" &&
                                        `This subscription will be billed every ${variant.interval_quantity || ""} month${variant.interval_quantity > 1 ? "s" : ""}.
                                        ${variant.bill_num_times ? `This subscription will be billed ${variant.bill_num_times} time${variant.bill_num_times > 1 ? "s" : ""} total.` : "This subscription will continue until cancelled."}`
                                    }
                                    {variant.bill_interval==="y" &&
                                        <>
                                            {variant.bill_num_times===1 && `This subscription will be billed only on purchase and will not recur.` }
                                            {variant.bill_num_times===null && "This subscription will be billed every year."}
                                        </>
                                    }
                                </>
                            }
                        </p>
                    </div>
                </div>
            ))}
        </div>
        <div className='display-product-heading'>
            {importedBtn}
        </div>
    </div>
  )
}
