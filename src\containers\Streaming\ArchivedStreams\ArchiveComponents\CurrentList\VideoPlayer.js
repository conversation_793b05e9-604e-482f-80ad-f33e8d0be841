import React, {useState, useEffect} from 'react'
import { Button } from 'react-bootstrap';

const VideoPlayer = (props) => {

    const {videoLink, purchased} = props;
    const [showLinkTwo, setShowLinkTwo] = useState(false);

    useEffect(()=>{
        if(videoLink && !videoLink?.link2) setShowLinkTwo(false)
    },[videoLink])

    return (
        <div className="video-player">
            {purchased &&
               <>
                    {!showLinkTwo &&
                        <video controls src={videoLink?.link} />
                    }
                    {showLinkTwo &&
                        <video controls src={videoLink?.link2} />
                    }
                    {videoLink?.link2 &&
                        <Button onClick={()=>{setShowLinkTwo(!showLinkTwo)}}>View Alternate Link</Button>
                    }
                </>
            }
        </div>
    )
}

export default VideoPlayer